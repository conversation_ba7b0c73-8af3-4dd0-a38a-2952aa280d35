#!/usr/bin/env python3
"""
MCP Feedback Enhanced 快速测试脚本
用于验证 MCP Feedback Enhanced 是否正确整合到 Augment

作者: Augment Agent
日期: 2025-06-17
"""

import json
import subprocess
import sys
from pathlib import Path


def print_status(message: str, status: str = "info"):
    """打印状态信息"""
    icons = {
        "success": "✅",
        "error": "❌", 
        "warning": "⚠️",
        "info": "ℹ️"
    }
    icon = icons.get(status, "ℹ️")
    print(f"{icon} {message}")


def test_mcp_version():
    """测试 MCP 版本"""
    print("\n🔍 测试 MCP Feedback Enhanced 版本...")
    
    try:
        result = subprocess.run(
            ["uvx", "mcp-feedback-enhanced@latest", "version"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=30
        )
        
        if result.returncode == 0:
            version_info = result.stdout.strip()
            print_status("MCP Feedback Enhanced 可用", "success")
            print(f"   {version_info}")
            return True
        else:
            print_status("版本检查失败", "error")
            return False
            
    except Exception as e:
        print_status(f"版本检查异常: {e}", "error")
        return False


def test_config_files():
    """测试配置文件"""
    print("\n📄 检查配置文件...")
    
    config_files = [
        "augment-mcp-feedback-enhanced-config.json",
        "augment-mcp-feedback-enhanced-desktop-config.json"
    ]
    
    valid_configs = []
    
    for config_file in config_files:
        config_path = Path(config_file)
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                if "mcpServers" in config and "mcp-feedback-enhanced" in config["mcpServers"]:
                    print_status(f"配置文件有效: {config_file}", "success")
                    valid_configs.append(config_file)
                else:
                    print_status(f"配置文件结构无效: {config_file}", "error")
                    
            except Exception as e:
                print_status(f"配置文件错误 {config_file}: {e}", "error")
        else:
            print_status(f"配置文件不存在: {config_file}", "warning")
    
    return len(valid_configs) > 0


def test_basic_functionality():
    """测试基础功能"""
    print("\n🧪 测试基础功能...")
    
    try:
        result = subprocess.run(
            ["uvx", "mcp-feedback-enhanced@latest", "test"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            timeout=20
        )
        
        output = (result.stdout or "") + (result.stderr or "")
        
        if "測試功能已簡化" in output or "可用的測試選項" in output:
            print_status("基础功能测试通过", "success")
            return True
        else:
            print_status("基础功能测试未通过", "warning")
            return False
            
    except Exception as e:
        print_status(f"功能测试异常: {e}", "error")
        return False


def show_next_steps():
    """显示下一步操作"""
    print("\n🎯 下一步操作:")
    print("1. 打开 Augment")
    print("2. 进入 MCP 设置/配置")
    print("3. 导入以下配置文件之一:")
    print("   • augment-mcp-feedback-enhanced-config.json (Web UI 模式)")
    print("   • augment-mcp-feedback-enhanced-desktop-config.json (桌面模式)")
    print("4. 重启 Augment")
    print("5. 测试命令: '请使用 interactive_feedback 工具测试功能'")


def show_config_content():
    """显示推荐配置内容"""
    print("\n📋 推荐配置 (Web UI 模式):")
    
    config = {
        "mcpServers": {
            "mcp-feedback-enhanced": {
                "command": "uvx",
                "args": ["mcp-feedback-enhanced@latest"],
                "timeout": 600,
                "autoApprove": ["interactive_feedback"],
                "description": "Enhanced Interactive Feedback MCP - 增强版交互反馈工具",
                "env": {
                    "MCP_DEBUG": "false",
                    "MCP_WEB_PORT": "8765"
                }
            }
        }
    }
    
    print(json.dumps(config, indent=2, ensure_ascii=False))


def main():
    """主函数"""
    print("🚀 MCP Feedback Enhanced 快速测试")
    print("=" * 50)
    
    # 测试版本
    version_ok = test_mcp_version()
    
    # 测试配置文件
    config_ok = test_config_files()
    
    # 测试基础功能
    func_ok = test_basic_functionality()
    
    # 总结
    print("\n📊 测试总结:")
    print_status(f"版本检查: {'通过' if version_ok else '失败'}", "success" if version_ok else "error")
    print_status(f"配置文件: {'有效' if config_ok else '无效'}", "success" if config_ok else "error")
    print_status(f"基础功能: {'可用' if func_ok else '异常'}", "success" if func_ok else "warning")
    
    if version_ok and config_ok:
        print_status("MCP Feedback Enhanced 已准备就绪！", "success")
        show_next_steps()
        
        if input("\n是否显示推荐配置内容？(y/N): ").lower() == 'y':
            show_config_content()
            
    else:
        print_status("存在问题，请检查安装", "error")
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
