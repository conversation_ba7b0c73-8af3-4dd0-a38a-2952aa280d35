"""
akshare 数据采集客户端
提供统一的 akshare 数据接口封装
"""

import asyncio
import logging
from datetime import datetime, date
from typing import List, Dict, Optional, Any
import pandas as pd
import akshare as ak
from concurrent.futures import ThreadPoolExecutor
import time

from app.core.config import settings

logger = logging.getLogger(__name__)


class AkshareClient:
    """akshare 数据采集客户端"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=5)
        self.last_request_time = 0
        self.min_interval = 0.1  # 最小请求间隔(秒)
    
    async def _rate_limit(self):
        """请求限流"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_interval:
            await asyncio.sleep(self.min_interval - time_since_last)
        
        self.last_request_time = time.time()
    
    async def _run_in_executor(self, func, *args, **kwargs):
        """在线程池中执行同步函数"""
        await self._rate_limit()
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, func, *args, **kwargs)
    
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """
        获取股票列表
        
        Returns:
            股票列表，包含代码、名称、市场等信息
        """
        try:
            logger.info("开始获取股票列表...")
            
            # 获取沪深A股列表
            def _get_stock_list():
                # 获取沪市A股
                sh_stocks = ak.stock_info_a_code_name()
                sh_stocks['market'] = 'SH'
                
                # 获取深市A股
                sz_stocks = ak.stock_info_a_code_name()
                sz_stocks['market'] = 'SZ'
                
                # 合并数据
                all_stocks = pd.concat([sh_stocks, sz_stocks], ignore_index=True)
                return all_stocks
            
            df = await self._run_in_executor(_get_stock_list)
            
            # 转换为字典列表
            stocks = []
            for _, row in df.iterrows():
                stock = {
                    'code': row['code'],
                    'name': row['name'],
                    'market': row['market'],
                }
                stocks.append(stock)
            
            logger.info(f"获取股票列表成功，共 {len(stocks)} 只股票")
            return stocks
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            raise
    
    async def get_stock_info(self, stock_code: str) -> Dict[str, Any]:
        """
        获取股票基本信息
        
        Args:
            stock_code: 股票代码
            
        Returns:
            股票基本信息
        """
        try:
            logger.debug(f"获取股票 {stock_code} 基本信息...")
            
            def _get_stock_info():
                # 获取股票基本信息
                info = ak.stock_individual_info_em(symbol=stock_code)
                return info
            
            df = await self._run_in_executor(_get_stock_info)
            
            # 转换为字典
            stock_info = {}
            for _, row in df.iterrows():
                key = row['item']
                value = row['value']
                stock_info[key] = value
            
            return stock_info
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 基本信息失败: {e}")
            raise
    
    async def get_realtime_quotes(self, stock_codes: List[str]) -> List[Dict[str, Any]]:
        """
        获取实时行情数据
        
        Args:
            stock_codes: 股票代码列表
            
        Returns:
            实时行情数据列表
        """
        try:
            logger.debug(f"获取 {len(stock_codes)} 只股票实时行情...")
            
            def _get_realtime_quotes():
                # 获取实时行情
                quotes = []
                for code in stock_codes:
                    try:
                        quote = ak.stock_zh_a_spot_em()
                        # 筛选指定股票
                        stock_quote = quote[quote['代码'] == code]
                        if not stock_quote.empty:
                            quotes.append(stock_quote.iloc[0].to_dict())
                    except Exception as e:
                        logger.warning(f"获取股票 {code} 实时行情失败: {e}")
                        continue
                
                return quotes
            
            quotes_data = await self._run_in_executor(_get_realtime_quotes)
            
            # 标准化数据格式
            standardized_quotes = []
            for quote in quotes_data:
                standardized_quote = {
                    'code': quote.get('代码'),
                    'name': quote.get('名称'),
                    'current_price': float(quote.get('最新价', 0)),
                    'change': float(quote.get('涨跌额', 0)),
                    'pct_change': float(quote.get('涨跌幅', 0)),
                    'open_price': float(quote.get('今开', 0)),
                    'high_price': float(quote.get('最高', 0)),
                    'low_price': float(quote.get('最低', 0)),
                    'pre_close': float(quote.get('昨收', 0)),
                    'volume': float(quote.get('成交量', 0)),
                    'amount': float(quote.get('成交额', 0)),
                    'turnover_rate': float(quote.get('换手率', 0)),
                    'pe_ratio': float(quote.get('市盈率-动态', 0)),
                    'pb_ratio': float(quote.get('市净率', 0)),
                    'market_cap': float(quote.get('总市值', 0)),
                    'timestamp': datetime.now(),
                }
                standardized_quotes.append(standardized_quote)
            
            logger.debug(f"获取实时行情成功，共 {len(standardized_quotes)} 条数据")
            return standardized_quotes
            
        except Exception as e:
            logger.error(f"获取实时行情失败: {e}")
            raise
    
    async def get_historical_quotes(
        self,
        stock_code: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        period: str = "daily"
    ) -> List[Dict[str, Any]]:
        """
        获取历史行情数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            period: 周期 (daily/weekly/monthly)
            
        Returns:
            历史行情数据列表
        """
        try:
            logger.debug(f"获取股票 {stock_code} 历史行情...")
            
            def _get_historical_quotes():
                # 设置默认日期
                if not start_date:
                    start_str = "20200101"
                else:
                    start_str = start_date.strftime("%Y%m%d")
                
                if not end_date:
                    end_str = datetime.now().strftime("%Y%m%d")
                else:
                    end_str = end_date.strftime("%Y%m%d")
                
                # 根据周期选择接口
                if period == "daily":
                    df = ak.stock_zh_a_hist(
                        symbol=stock_code,
                        period="daily",
                        start_date=start_str,
                        end_date=end_str,
                        adjust=""
                    )
                elif period == "weekly":
                    df = ak.stock_zh_a_hist(
                        symbol=stock_code,
                        period="weekly",
                        start_date=start_str,
                        end_date=end_str,
                        adjust=""
                    )
                elif period == "monthly":
                    df = ak.stock_zh_a_hist(
                        symbol=stock_code,
                        period="monthly",
                        start_date=start_str,
                        end_date=end_str,
                        adjust=""
                    )
                else:
                    raise ValueError(f"不支持的周期: {period}")
                
                return df
            
            df = await self._run_in_executor(_get_historical_quotes)
            
            # 转换为字典列表
            quotes = []
            for _, row in df.iterrows():
                quote = {
                    'trade_date': pd.to_datetime(row['日期']).date(),
                    'open_price': float(row['开盘']),
                    'high_price': float(row['最高']),
                    'low_price': float(row['最低']),
                    'close_price': float(row['收盘']),
                    'volume': float(row['成交量']),
                    'amount': float(row['成交额']),
                    'pct_change': float(row.get('涨跌幅', 0)),
                    'change': float(row.get('涨跌额', 0)),
                    'turnover_rate': float(row.get('换手率', 0)),
                }
                quotes.append(quote)
            
            logger.debug(f"获取历史行情成功，共 {len(quotes)} 条数据")
            return quotes
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 历史行情失败: {e}")
            raise
    
    async def get_financial_data(self, stock_code: str) -> Dict[str, Any]:
        """
        获取财务数据
        
        Args:
            stock_code: 股票代码
            
        Returns:
            财务数据
        """
        try:
            logger.debug(f"获取股票 {stock_code} 财务数据...")
            
            def _get_financial_data():
                # 获取财务数据
                financial_data = {}
                
                try:
                    # 获取利润表
                    profit_df = ak.stock_financial_analysis_indicator(symbol=stock_code)
                    if not profit_df.empty:
                        latest_profit = profit_df.iloc[0]
                        financial_data.update({
                            'revenue': float(latest_profit.get('营业收入', 0)),
                            'net_profit': float(latest_profit.get('净利润', 0)),
                            'roe': float(latest_profit.get('净资产收益率', 0)),
                            'roa': float(latest_profit.get('总资产收益率', 0)),
                            'gross_margin': float(latest_profit.get('毛利率', 0)),
                            'net_margin': float(latest_profit.get('净利率', 0)),
                        })
                except Exception as e:
                    logger.warning(f"获取利润表数据失败: {e}")
                
                try:
                    # 获取资产负债表
                    balance_df = ak.stock_balance_sheet_by_report_em(symbol=stock_code)
                    if not balance_df.empty:
                        latest_balance = balance_df.iloc[0]
                        financial_data.update({
                            'total_assets': float(latest_balance.get('资产总计', 0)),
                            'total_liabilities': float(latest_balance.get('负债合计', 0)),
                            'total_equity': float(latest_balance.get('股东权益合计', 0)),
                        })
                except Exception as e:
                    logger.warning(f"获取资产负债表数据失败: {e}")
                
                return financial_data
            
            financial_data = await self._run_in_executor(_get_financial_data)
            
            logger.debug(f"获取财务数据成功")
            return financial_data
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 财务数据失败: {e}")
            raise
    
    async def get_sector_data(self) -> List[Dict[str, Any]]:
        """
        获取板块数据
        
        Returns:
            板块数据列表
        """
        try:
            logger.info("获取板块数据...")
            
            def _get_sector_data():
                # 获取行业板块
                industry_df = ak.stock_board_industry_name_em()
                
                sectors = []
                for _, row in industry_df.iterrows():
                    sector = {
                        'code': row['板块代码'],
                        'name': row['板块名称'],
                        'category': 'industry',
                    }
                    sectors.append(sector)
                
                return sectors
            
            sectors = await self._run_in_executor(_get_sector_data)
            
            logger.info(f"获取板块数据成功，共 {len(sectors)} 个板块")
            return sectors
            
        except Exception as e:
            logger.error(f"获取板块数据失败: {e}")
            raise
    
    async def close(self):
        """关闭客户端"""
        self.executor.shutdown(wait=True)
        logger.info("akshare 客户端已关闭")
