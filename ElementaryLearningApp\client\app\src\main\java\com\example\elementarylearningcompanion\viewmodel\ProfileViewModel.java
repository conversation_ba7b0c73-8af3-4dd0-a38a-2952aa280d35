package com.example.elementarylearningcompanion.viewmodel;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.example.elementarylearningcompanion.dto.ApiResponse;
import com.example.elementarylearningcompanion.dto.Student;
import com.example.elementarylearningcompanion.network.RetrofitClient;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ProfileViewModel extends ViewModel {

    private MutableLiveData<Student> studentProfile = new MutableLiveData<>();
    private MutableLiveData<Map<String, Object>> statistics = new MutableLiveData<>();
    private MutableLiveData<Map<String, Object>> settings = new MutableLiveData<>();
    private MutableLiveData<Map<String, Object>> availableGrades = new MutableLiveData<>();
    private MutableLiveData<Boolean> isLoading = new MutableLiveData<>();
    private MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private MutableLiveData<ApiResponse> updateResult = new MutableLiveData<>();

    public LiveData<Student> getStudentProfile() {
        return studentProfile;
    }

    public LiveData<Map<String, Object>> getStatistics() {
        return statistics;
    }

    public LiveData<Map<String, Object>> getSettings() {
        return settings;
    }

    public LiveData<Map<String, Object>> getAvailableGrades() {
        return availableGrades;
    }

    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public LiveData<ApiResponse> getUpdateResult() {
        return updateResult;
    }

    public void loadStudentProfile(long studentId) {
        isLoading.setValue(true);
        RetrofitClient.getApiService().getStudentProfile(studentId).enqueue(new Callback<Student>() {
            @Override
            public void onResponse(Call<Student> call, Response<Student> response) {
                isLoading.setValue(false);
                if (response.isSuccessful() && response.body() != null) {
                    studentProfile.setValue(response.body());
                } else {
                    errorMessage.setValue("加载学生信息失败");
                }
            }

            @Override
            public void onFailure(Call<Student> call, Throwable t) {
                isLoading.setValue(false);
                errorMessage.setValue("网络错误：" + t.getMessage());
            }
        });
    }

    public void loadStatistics(long studentId) {
        RetrofitClient.getApiService().getStudentStatistics(studentId).enqueue(new Callback<Map<String, Object>>() {
            @Override
            public void onResponse(Call<Map<String, Object>> call, Response<Map<String, Object>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    statistics.setValue(response.body());
                } else {
                    errorMessage.setValue("加载统计信息失败");
                }
            }

            @Override
            public void onFailure(Call<Map<String, Object>> call, Throwable t) {
                errorMessage.setValue("网络错误：" + t.getMessage());
            }
        });
    }

    public void loadSettings(long userId) {
        RetrofitClient.getApiService().getAppSettings(userId).enqueue(new Callback<Map<String, Object>>() {
            @Override
            public void onResponse(Call<Map<String, Object>> call, Response<Map<String, Object>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    settings.setValue(response.body());
                } else {
                    errorMessage.setValue("加载设置失败");
                }
            }

            @Override
            public void onFailure(Call<Map<String, Object>> call, Throwable t) {
                errorMessage.setValue("网络错误：" + t.getMessage());
            }
        });
    }

    public void loadAvailableGrades() {
        RetrofitClient.getApiService().getAvailableGrades().enqueue(new Callback<Map<String, Object>>() {
            @Override
            public void onResponse(Call<Map<String, Object>> call, Response<Map<String, Object>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    availableGrades.setValue(response.body());
                } else {
                    errorMessage.setValue("加载年级信息失败");
                }
            }

            @Override
            public void onFailure(Call<Map<String, Object>> call, Throwable t) {
                errorMessage.setValue("网络错误：" + t.getMessage());
            }
        });
    }

    public void updateStudentProfile(long studentId, String name, Integer grade, Integer textbookVersionId) {
        Map<String, Object> request = new HashMap<>();
        if (name != null) request.put("name", name);
        if (grade != null) request.put("grade", grade);
        if (textbookVersionId != null) request.put("textbookVersionId", textbookVersionId);

        RetrofitClient.getApiService().updateStudentProfile(studentId, request).enqueue(new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    updateResult.setValue(response.body());
                    if (response.body().isSuccess()) {
                        // Refresh profile
                        loadStudentProfile(studentId);
                    }
                } else {
                    updateResult.setValue(new ApiResponse(false, "更新失败"));
                }
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                updateResult.setValue(new ApiResponse(false, "网络错误：" + t.getMessage()));
            }
        });
    }

    public void updateSettings(long userId, Map<String, Object> newSettings) {
        RetrofitClient.getApiService().updateAppSettings(userId, newSettings).enqueue(new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    updateResult.setValue(response.body());
                    if (response.body().isSuccess()) {
                        // Refresh settings
                        loadSettings(userId);
                    }
                } else {
                    updateResult.setValue(new ApiResponse(false, "设置更新失败"));
                }
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                updateResult.setValue(new ApiResponse(false, "网络错误：" + t.getMessage()));
            }
        });
    }

    public void clearUpdateResult() {
        updateResult.setValue(null);
    }

    public void clearErrorMessage() {
        errorMessage.setValue(null);
    }
}
