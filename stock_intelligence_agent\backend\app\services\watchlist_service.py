"""
自选股服务层
提供自选股管理、监控、分析等功能
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, func
from sqlalchemy.orm import selectinload
from uuid import UUID

from app.models.user import Watchlist, WatchlistItem
from app.models.stock import Stock
from app.services.stock_service import StockService

logger = logging.getLogger(__name__)


class WatchlistService:
    """自选股服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_watchlist(
        self,
        user_id: UUID,
        name: str,
        description: Optional[str] = None,
        is_public: bool = False
    ) -> Watchlist:
        """
        创建自选股列表
        
        Args:
            user_id: 用户ID
            name: 列表名称
            description: 描述
            is_public: 是否公开
            
        Returns:
            创建的自选股列表
        """
        try:
            watchlist = Watchlist(
                user_id=user_id,
                name=name,
                description=description,
                is_public=is_public
            )
            
            self.db.add(watchlist)
            await self.db.commit()
            
            logger.info(f"自选股列表创建成功: {name}")
            return watchlist
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建自选股列表失败: {e}")
            raise
    
    async def get_user_watchlists(
        self,
        user_id: UUID,
        include_items: bool = False
    ) -> List[Watchlist]:
        """
        获取用户自选股列表
        
        Args:
            user_id: 用户ID
            include_items: 是否包含股票项目
            
        Returns:
            自选股列表
        """
        try:
            stmt = select(Watchlist).where(Watchlist.user_id == user_id)
            
            if include_items:
                stmt = stmt.options(selectinload(Watchlist.items))
            
            stmt = stmt.order_by(Watchlist.created_at)
            
            result = await self.db.execute(stmt)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error(f"获取用户自选股列表失败: {e}")
            raise
    
    async def get_watchlist_by_id(
        self,
        watchlist_id: UUID,
        user_id: UUID,
        include_items: bool = False
    ) -> Optional[Watchlist]:
        """获取自选股列表详情"""
        try:
            stmt = select(Watchlist).where(
                and_(
                    Watchlist.id == watchlist_id,
                    Watchlist.user_id == user_id
                )
            )
            
            if include_items:
                stmt = stmt.options(selectinload(Watchlist.items))
            
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"获取自选股列表详情失败: {e}")
            return None
    
    async def update_watchlist(
        self,
        watchlist_id: UUID,
        user_id: UUID,
        **kwargs
    ) -> Optional[Watchlist]:
        """
        更新自选股列表
        
        Args:
            watchlist_id: 列表ID
            user_id: 用户ID
            **kwargs: 更新字段
            
        Returns:
            更新后的列表
        """
        try:
            watchlist = await self.get_watchlist_by_id(watchlist_id, user_id)
            if not watchlist:
                return None
            
            # 更新字段
            for key, value in kwargs.items():
                if hasattr(watchlist, key) and value is not None:
                    setattr(watchlist, key, value)
            
            watchlist.updated_at = datetime.utcnow()
            await self.db.commit()
            
            return watchlist
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新自选股列表失败: {e}")
            raise
    
    async def delete_watchlist(self, watchlist_id: UUID, user_id: UUID) -> bool:
        """删除自选股列表"""
        try:
            watchlist = await self.get_watchlist_by_id(watchlist_id, user_id)
            if not watchlist:
                return False
            
            await self.db.delete(watchlist)
            await self.db.commit()
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除自选股列表失败: {e}")
            return False
    
    async def add_stock_to_watchlist(
        self,
        watchlist_id: UUID,
        user_id: UUID,
        stock_code: str,
        notes: Optional[str] = None
    ) -> Optional[WatchlistItem]:
        """
        添加股票到自选股列表
        
        Args:
            watchlist_id: 列表ID
            user_id: 用户ID
            stock_code: 股票代码
            notes: 备注
            
        Returns:
            添加的股票项目
        """
        try:
            # 验证自选股列表
            watchlist = await self.get_watchlist_by_id(watchlist_id, user_id)
            if not watchlist:
                raise ValueError("自选股列表不存在")
            
            # 验证股票代码
            stock_service = StockService(self.db)
            stock = await stock_service.get_stock_by_code(stock_code)
            if not stock:
                raise ValueError(f"股票代码不存在: {stock_code}")
            
            # 检查是否已存在
            existing_item = await self._get_watchlist_item(watchlist_id, stock_code)
            if existing_item:
                raise ValueError("股票已在自选股列表中")
            
            # 获取当前最大排序号
            max_sort_stmt = select(func.max(WatchlistItem.sort_order)).where(
                WatchlistItem.watchlist_id == watchlist_id
            )
            max_sort_result = await self.db.execute(max_sort_stmt)
            max_sort = max_sort_result.scalar() or 0
            
            # 创建自选股项目
            item = WatchlistItem(
                watchlist_id=watchlist_id,
                stock_code=stock_code,
                notes=notes,
                sort_order=max_sort + 1
            )
            
            self.db.add(item)
            await self.db.commit()
            
            logger.info(f"股票已添加到自选股: {stock_code}")
            return item
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"添加股票到自选股失败: {e}")
            raise
    
    async def remove_stock_from_watchlist(
        self,
        watchlist_id: UUID,
        user_id: UUID,
        stock_code: str
    ) -> bool:
        """从自选股列表移除股票"""
        try:
            # 验证自选股列表
            watchlist = await self.get_watchlist_by_id(watchlist_id, user_id)
            if not watchlist:
                return False
            
            # 查找并删除项目
            item = await self._get_watchlist_item(watchlist_id, stock_code)
            if not item:
                return False
            
            await self.db.delete(item)
            await self.db.commit()
            
            logger.info(f"股票已从自选股移除: {stock_code}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"从自选股移除股票失败: {e}")
            return False
    
    async def update_watchlist_item(
        self,
        watchlist_id: UUID,
        user_id: UUID,
        stock_code: str,
        **kwargs
    ) -> Optional[WatchlistItem]:
        """
        更新自选股项目
        
        Args:
            watchlist_id: 列表ID
            user_id: 用户ID
            stock_code: 股票代码
            **kwargs: 更新字段
            
        Returns:
            更新后的项目
        """
        try:
            # 验证自选股列表
            watchlist = await self.get_watchlist_by_id(watchlist_id, user_id)
            if not watchlist:
                return None
            
            # 查找项目
            item = await self._get_watchlist_item(watchlist_id, stock_code)
            if not item:
                return None
            
            # 更新字段
            for key, value in kwargs.items():
                if hasattr(item, key) and value is not None:
                    setattr(item, key, value)
            
            await self.db.commit()
            
            return item
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新自选股项目失败: {e}")
            raise
    
    async def get_watchlist_with_quotes(
        self,
        watchlist_id: UUID,
        user_id: UUID
    ) -> Optional[Dict[str, Any]]:
        """
        获取带行情的自选股列表
        
        Args:
            watchlist_id: 列表ID
            user_id: 用户ID
            
        Returns:
            包含行情的自选股数据
        """
        try:
            # 获取自选股列表
            watchlist = await self.get_watchlist_by_id(watchlist_id, user_id, include_items=True)
            if not watchlist:
                return None
            
            # 获取股票行情数据
            stock_service = StockService(self.db)
            items_with_quotes = []
            
            for item in watchlist.items:
                # 获取股票基本信息
                stock = await stock_service.get_stock_by_code(item.stock_code)
                if not stock:
                    continue
                
                # 获取最新实时行情
                realtime_quote = await stock_service.get_latest_realtime_quote(stock.id)
                
                # 获取最新日行情
                quotes = await stock_service.get_stock_quotes(stock.id, limit=1)
                latest_quote = quotes[0] if quotes else None
                
                # 使用实时行情或最新日行情
                quote = realtime_quote or latest_quote
                
                item_data = {
                    "id": str(item.id),
                    "stock_code": item.stock_code,
                    "stock_name": stock.name,
                    "market": stock.market,
                    "industry": stock.industry,
                    "notes": item.notes,
                    "sort_order": item.sort_order,
                    "added_at": item.added_at.isoformat() if item.added_at else None,
                    "current_price": float(quote.current_price or quote.close_price or 0) if quote else None,
                    "change": float(quote.change or 0) if quote else None,
                    "pct_change": float(quote.pct_change or 0) if quote else None,
                    "volume": float(quote.volume or 0) if quote else None,
                    "amount": float(quote.amount or 0) if quote else None,
                }
                
                items_with_quotes.append(item_data)
            
            # 按排序号排序
            items_with_quotes.sort(key=lambda x: x["sort_order"])
            
            return {
                "id": str(watchlist.id),
                "name": watchlist.name,
                "description": watchlist.description,
                "is_public": watchlist.is_public,
                "stock_count": len(items_with_quotes),
                "items": items_with_quotes,
                "created_at": watchlist.created_at.isoformat() if watchlist.created_at else None,
                "updated_at": watchlist.updated_at.isoformat() if watchlist.updated_at else None
            }
            
        except Exception as e:
            logger.error(f"获取带行情的自选股列表失败: {e}")
            raise
    
    async def reorder_watchlist_items(
        self,
        watchlist_id: UUID,
        user_id: UUID,
        item_orders: List[Dict[str, Any]]
    ) -> bool:
        """
        重新排序自选股项目
        
        Args:
            watchlist_id: 列表ID
            user_id: 用户ID
            item_orders: 排序列表 [{"stock_code": "000001", "sort_order": 1}, ...]
            
        Returns:
            是否成功
        """
        try:
            # 验证自选股列表
            watchlist = await self.get_watchlist_by_id(watchlist_id, user_id)
            if not watchlist:
                return False
            
            # 更新排序
            for item_order in item_orders:
                stock_code = item_order.get("stock_code")
                sort_order = item_order.get("sort_order")
                
                if not stock_code or sort_order is None:
                    continue
                
                item = await self._get_watchlist_item(watchlist_id, stock_code)
                if item:
                    item.sort_order = sort_order
            
            await self.db.commit()
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"重新排序自选股项目失败: {e}")
            return False
    
    async def get_public_watchlists(
        self,
        skip: int = 0,
        limit: int = 20
    ) -> Tuple[List[Watchlist], int]:
        """
        获取公开的自选股列表
        
        Args:
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            (公开列表, 总数)
        """
        try:
            # 查询总数
            count_stmt = select(func.count(Watchlist.id)).where(Watchlist.is_public == True)
            count_result = await self.db.execute(count_stmt)
            total = count_result.scalar()
            
            # 查询数据
            stmt = (
                select(Watchlist)
                .where(Watchlist.is_public == True)
                .order_by(desc(Watchlist.created_at))
                .offset(skip)
                .limit(limit)
            )
            
            result = await self.db.execute(stmt)
            watchlists = result.scalars().all()
            
            return list(watchlists), total
            
        except Exception as e:
            logger.error(f"获取公开自选股列表失败: {e}")
            raise
    
    async def copy_watchlist(
        self,
        source_watchlist_id: UUID,
        target_user_id: UUID,
        new_name: Optional[str] = None
    ) -> Optional[Watchlist]:
        """
        复制自选股列表
        
        Args:
            source_watchlist_id: 源列表ID
            target_user_id: 目标用户ID
            new_name: 新列表名称
            
        Returns:
            复制的列表
        """
        try:
            # 获取源列表
            stmt = select(Watchlist).where(
                and_(
                    Watchlist.id == source_watchlist_id,
                    or_(
                        Watchlist.is_public == True,
                        Watchlist.user_id == target_user_id
                    )
                )
            ).options(selectinload(Watchlist.items))
            
            result = await self.db.execute(stmt)
            source_watchlist = result.scalar_one_or_none()
            
            if not source_watchlist:
                return None
            
            # 创建新列表
            new_watchlist = Watchlist(
                user_id=target_user_id,
                name=new_name or f"{source_watchlist.name} (副本)",
                description=source_watchlist.description,
                is_public=False
            )
            
            self.db.add(new_watchlist)
            await self.db.flush()  # 获取ID
            
            # 复制股票项目
            for item in source_watchlist.items:
                new_item = WatchlistItem(
                    watchlist_id=new_watchlist.id,
                    stock_code=item.stock_code,
                    notes=item.notes,
                    sort_order=item.sort_order
                )
                self.db.add(new_item)
            
            await self.db.commit()
            
            logger.info(f"自选股列表复制成功: {source_watchlist.name} -> {new_watchlist.name}")
            return new_watchlist
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"复制自选股列表失败: {e}")
            raise
    
    async def _get_watchlist_item(
        self,
        watchlist_id: UUID,
        stock_code: str
    ) -> Optional[WatchlistItem]:
        """获取自选股项目"""
        try:
            stmt = select(WatchlistItem).where(
                and_(
                    WatchlistItem.watchlist_id == watchlist_id,
                    WatchlistItem.stock_code == stock_code
                )
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取自选股项目失败: {e}")
            return None
