/**
 * 简单的构建测试脚本
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始测试前端项目构建...\n');

// 检查必要文件
const requiredFiles = [
  'package.json',
  'tsconfig.json',
  'src/App.tsx',
  'src/store/index.ts',
  'public/index.html'
];

console.log('📋 检查必要文件...');
for (const file of requiredFiles) {
  if (fs.existsSync(path.join(__dirname, file))) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 文件不存在`);
  }
}

// 检查TypeScript配置
console.log('\n🔧 检查TypeScript配置...');
try {
  const tsconfig = JSON.parse(fs.readFileSync(path.join(__dirname, 'tsconfig.json'), 'utf8'));
  console.log('✅ tsconfig.json 格式正确');
  console.log(`   - 目标: ${tsconfig.compilerOptions.target}`);
  console.log(`   - 模块: ${tsconfig.compilerOptions.module}`);
  console.log(`   - JSX: ${tsconfig.compilerOptions.jsx}`);
} catch (error) {
  console.log('❌ tsconfig.json 格式错误:', error.message);
}

// 检查package.json
console.log('\n📦 检查package.json...');
try {
  const pkg = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
  console.log('✅ package.json 格式正确');
  console.log(`   - 项目名: ${pkg.name}`);
  console.log(`   - 版本: ${pkg.version}`);
  
  const requiredDeps = ['react', 'react-dom', '@reduxjs/toolkit', 'antd', 'axios'];
  console.log('\n   依赖检查:');
  for (const dep of requiredDeps) {
    if (pkg.dependencies[dep]) {
      console.log(`   ✅ ${dep}: ${pkg.dependencies[dep]}`);
    } else {
      console.log(`   ❌ ${dep}: 缺失`);
    }
  }
} catch (error) {
  console.log('❌ package.json 格式错误:', error.message);
}

// 检查源代码结构
console.log('\n📁 检查源代码结构...');
const srcDirs = [
  'src/components',
  'src/pages',
  'src/store',
  'src/services',
  'src/types',
  'src/styles'
];

for (const dir of srcDirs) {
  if (fs.existsSync(path.join(__dirname, dir))) {
    const files = fs.readdirSync(path.join(__dirname, dir));
    console.log(`✅ ${dir} (${files.length} 个文件)`);
  } else {
    console.log(`❌ ${dir} - 目录不存在`);
  }
}

// 检查关键页面文件
console.log('\n📄 检查关键页面文件...');
const pageFiles = [
  'src/pages/Home/index.tsx',
  'src/pages/Stocks/index.tsx',
  'src/pages/StockDetail/index.tsx',
  'src/pages/News/index.tsx',
  'src/pages/Watchlist/index.tsx'
];

for (const file of pageFiles) {
  if (fs.existsSync(path.join(__dirname, file))) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 文件不存在`);
  }
}

// 检查Redux store文件
console.log('\n🏪 检查Redux store文件...');
const storeFiles = [
  'src/store/index.ts',
  'src/store/slices/authSlice.ts',
  'src/store/slices/stockSlice.ts',
  'src/store/slices/newsSlice.ts',
  'src/store/slices/watchlistSlice.ts',
  'src/store/slices/alertSlice.ts',
  'src/store/slices/uiSlice.ts'
];

for (const file of storeFiles) {
  if (fs.existsSync(path.join(__dirname, file))) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 文件不存在`);
  }
}

console.log('\n🎯 测试总结:');
console.log('前端项目结构检查完成！');
console.log('如果所有文件都存在且格式正确，项目应该可以正常构建。');
console.log('\n💡 下一步:');
console.log('1. 运行 npm install 安装依赖');
console.log('2. 运行 npm start 启动开发服务器');
console.log('3. 运行 npm run build 构建生产版本');
