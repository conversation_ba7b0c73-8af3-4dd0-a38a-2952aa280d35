"""
新闻源配置和管理
定义各种新闻源的采集配置
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum


class NewsSourceType(Enum):
    """新闻源类型"""
    RSS = "rss"
    API = "api"
    WEB_SCRAPING = "web_scraping"
    SOCIAL_MEDIA = "social_media"


class NewsCategory(Enum):
    """新闻分类"""
    MARKET = "market"  # 市场资讯
    POLICY = "policy"  # 政策法规
    COMPANY = "company"  # 公司公告
    INDUSTRY = "industry"  # 行业动态
    MACRO = "macro"  # 宏观经济
    INTERNATIONAL = "international"  # 国际财经
    TECHNOLOGY = "technology"  # 科技创新
    ANALYSIS = "analysis"  # 分析评论
    GENERAL = "general"  # 综合资讯


@dataclass
class NewsSource:
    """新闻源配置"""
    name: str
    source_type: NewsSourceType
    url: str
    category: NewsCategory
    encoding: str = "utf-8"
    headers: Optional[Dict[str, str]] = None
    params: Optional[Dict[str, Any]] = None
    selectors: Optional[Dict[str, str]] = None  # CSS选择器
    rate_limit: int = 60  # 每分钟请求次数限制
    timeout: int = 30  # 超时时间(秒)
    is_active: bool = True
    priority: int = 1  # 优先级 1-5，5最高
    language: str = "zh-CN"
    description: str = ""


class NewsSourceManager:
    """新闻源管理器"""
    
    def __init__(self):
        self.sources = self._init_sources()
    
    def _init_sources(self) -> Dict[str, NewsSource]:
        """初始化新闻源配置"""
        sources = {}
        
        # 财经网站RSS源
        sources["sina_finance"] = NewsSource(
            name="新浪财经",
            source_type=NewsSourceType.RSS,
            url="https://feed.mix.sina.com.cn/api/roll/get?pageid=153&lid=1686&k=&num=50&page=1",
            category=NewsCategory.MARKET,
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            priority=5,
            description="新浪财经实时资讯"
        )
        
        sources["eastmoney"] = NewsSource(
            name="东方财富",
            source_type=NewsSourceType.API,
            url="https://np-anotice-stock.eastmoney.com/api/security/ann",
            category=NewsCategory.COMPANY,
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Referer": "https://data.eastmoney.com/"
            },
            params={
                "cb": "jQuery",
                "client_source": "web",
                "page_size": "50",
                "page_index": "0",
                "ann_type": "A",
                "f_node": "0",
                "s_node": "0"
            },
            priority=5,
            description="东方财富公司公告"
        )
        
        sources["cnstock"] = NewsSource(
            name="中国证券网",
            source_type=NewsSourceType.WEB_SCRAPING,
            url="https://news.cnstock.com/news,yw-202101-4851563.htm",
            category=NewsCategory.MARKET,
            selectors={
                "title": ".content h1",
                "content": ".content .allcontent",
                "time": ".content .timer",
                "source": ".content .source"
            },
            priority=4,
            description="中国证券网要闻"
        )
        
        sources["securities_times"] = NewsSource(
            name="证券时报",
            source_type=NewsSourceType.RSS,
            url="https://www.stcn.com/xw/sd.shtml",
            category=NewsCategory.MARKET,
            selectors={
                "list": ".news-item",
                "title": ".news-item h3 a",
                "link": ".news-item h3 a",
                "time": ".news-item .time",
                "summary": ".news-item .summary"
            },
            priority=4,
            description="证券时报深度报道"
        )
        
        sources["caixin"] = NewsSource(
            name="财新网",
            source_type=NewsSourceType.WEB_SCRAPING,
            url="https://www.caixin.com/",
            category=NewsCategory.MACRO,
            selectors={
                "list": ".news_item",
                "title": ".news_item h2 a",
                "link": ".news_item h2 a",
                "time": ".news_item .time",
                "summary": ".news_item .summary"
            },
            priority=3,
            description="财新网宏观经济"
        )
        
        sources["wallstreetcn"] = NewsSource(
            name="华尔街见闻",
            source_type=NewsSourceType.API,
            url="https://api-prod.wallstreetcn.com/apiv1/content/articles",
            category=NewsCategory.INTERNATIONAL,
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            params={
                "limit": "20",
                "cursor": "",
                "channel": "global-markets"
            },
            priority=4,
            description="华尔街见闻国际财经"
        )
        
        sources["jrj"] = NewsSource(
            name="金融界",
            source_type=NewsSourceType.WEB_SCRAPING,
            url="http://finance.jrj.com.cn/",
            category=NewsCategory.MARKET,
            selectors={
                "list": ".newslist li",
                "title": ".newslist li a",
                "link": ".newslist li a",
                "time": ".newslist li .time"
            },
            priority=3,
            description="金融界财经资讯"
        )
        
        sources["hexun"] = NewsSource(
            name="和讯网",
            source_type=NewsSourceType.RSS,
            url="http://rss.hexun.com/rss_stock.xml",
            category=NewsCategory.MARKET,
            priority=3,
            description="和讯网股票资讯"
        )
        
        sources["cls"] = NewsSource(
            name="财联社",
            source_type=NewsSourceType.WEB_SCRAPING,
            url="https://www.cls.cn/telegraph",
            category=NewsCategory.MARKET,
            selectors={
                "list": ".telegraph-item",
                "title": ".telegraph-item .telegraph-content",
                "time": ".telegraph-item .telegraph-time",
                "content": ".telegraph-item .telegraph-content"
            },
            priority=5,
            description="财联社电报"
        )
        
        sources["yicai"] = NewsSource(
            name="第一财经",
            source_type=NewsSourceType.WEB_SCRAPING,
            url="https://www.yicai.com/news/",
            category=NewsCategory.MACRO,
            selectors={
                "list": ".m-news-list li",
                "title": ".m-news-list li h2 a",
                "link": ".m-news-list li h2 a",
                "time": ".m-news-list li .m-news-time",
                "summary": ".m-news-list li .m-news-brief"
            },
            priority=4,
            description="第一财经新闻"
        )
        
        # 政府官方源
        sources["csrc"] = NewsSource(
            name="证监会",
            source_type=NewsSourceType.WEB_SCRAPING,
            url="http://www.csrc.gov.cn/csrc/c100028/common_list.shtml",
            category=NewsCategory.POLICY,
            selectors={
                "list": ".zx_ml_list li",
                "title": ".zx_ml_list li a",
                "link": ".zx_ml_list li a",
                "time": ".zx_ml_list li span"
            },
            priority=5,
            description="中国证监会公告"
        )
        
        sources["pbc"] = NewsSource(
            name="央行",
            source_type=NewsSourceType.WEB_SCRAPING,
            url="http://www.pbc.gov.cn/goutongjiaoliu/113456/113469/index.html",
            category=NewsCategory.POLICY,
            selectors={
                "list": ".list_content li",
                "title": ".list_content li a",
                "link": ".list_content li a",
                "time": ".list_content li span"
            },
            priority=5,
            description="中国人民银行公告"
        )
        
        # 交易所公告
        sources["sse"] = NewsSource(
            name="上交所",
            source_type=NewsSourceType.API,
            url="http://query.sse.com.cn/commonQuery.do",
            category=NewsCategory.COMPANY,
            headers={
                "Referer": "http://www.sse.com.cn/"
            },
            params={
                "jsonCallBack": "jsonpCallback",
                "isPagination": "true",
                "sqlId": "COMMON_SSE_CP_CPS_SSGS_GSZL_GSGG_L",
                "pageHelp.pageSize": "25",
                "pageHelp.pageNo": "1",
                "pageHelp.beginPage": "1",
                "pageHelp.endPage": "5"
            },
            priority=5,
            description="上海证券交易所公告"
        )
        
        sources["szse"] = NewsSource(
            name="深交所",
            source_type=NewsSourceType.API,
            url="http://www.szse.cn/api/report/ShowReport/data",
            category=NewsCategory.COMPANY,
            params={
                "SHOWTYPE": "JSON",
                "CATALOGID": "1426",
                "PAGENO": "1",
                "PAGESIZE": "30"
            },
            priority=5,
            description="深圳证券交易所公告"
        )
        
        return sources
    
    def get_active_sources(self) -> List[NewsSource]:
        """获取活跃的新闻源"""
        return [source for source in self.sources.values() if source.is_active]
    
    def get_sources_by_category(self, category: NewsCategory) -> List[NewsSource]:
        """根据分类获取新闻源"""
        return [
            source for source in self.sources.values()
            if source.category == category and source.is_active
        ]
    
    def get_sources_by_type(self, source_type: NewsSourceType) -> List[NewsSource]:
        """根据类型获取新闻源"""
        return [
            source for source in self.sources.values()
            if source.source_type == source_type and source.is_active
        ]
    
    def get_high_priority_sources(self, min_priority: int = 4) -> List[NewsSource]:
        """获取高优先级新闻源"""
        return [
            source for source in self.sources.values()
            if source.priority >= min_priority and source.is_active
        ]
    
    def get_source(self, name: str) -> Optional[NewsSource]:
        """根据名称获取新闻源"""
        return self.sources.get(name)
    
    def add_source(self, source: NewsSource) -> bool:
        """添加新闻源"""
        if source.name not in self.sources:
            self.sources[source.name] = source
            return True
        return False
    
    def update_source(self, name: str, **kwargs) -> bool:
        """更新新闻源配置"""
        if name in self.sources:
            source = self.sources[name]
            for key, value in kwargs.items():
                if hasattr(source, key):
                    setattr(source, key, value)
            return True
        return False
    
    def disable_source(self, name: str) -> bool:
        """禁用新闻源"""
        return self.update_source(name, is_active=False)
    
    def enable_source(self, name: str) -> bool:
        """启用新闻源"""
        return self.update_source(name, is_active=True)
    
    def get_source_stats(self) -> Dict[str, Any]:
        """获取新闻源统计信息"""
        total_sources = len(self.sources)
        active_sources = len(self.get_active_sources())
        
        category_stats = {}
        type_stats = {}
        priority_stats = {}
        
        for source in self.sources.values():
            # 分类统计
            category = source.category.value
            category_stats[category] = category_stats.get(category, 0) + 1
            
            # 类型统计
            source_type = source.source_type.value
            type_stats[source_type] = type_stats.get(source_type, 0) + 1
            
            # 优先级统计
            priority = source.priority
            priority_stats[priority] = priority_stats.get(priority, 0) + 1
        
        return {
            "total_sources": total_sources,
            "active_sources": active_sources,
            "inactive_sources": total_sources - active_sources,
            "category_distribution": category_stats,
            "type_distribution": type_stats,
            "priority_distribution": priority_stats
        }


# 全局新闻源管理器实例
news_source_manager = NewsSourceManager()
