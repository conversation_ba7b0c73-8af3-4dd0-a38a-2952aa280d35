  Activity android.app  
AuthScreen android.app.Activity  
AuthViewModel android.app.Activity  Bundle android.app.Activity  ContentViewModel android.app.Activity   ElementaryLearningCompanionTheme android.app.Activity  
EnglishScreen android.app.Activity  EnglishViewModel android.app.Activity  
HomeScreen android.app.Activity  LessonListScreen android.app.Activity  LessonScreen android.app.Activity  
MaterialTheme android.app.Activity  
MathScreen android.app.Activity  
MathViewModel android.app.Activity  Modifier android.app.Activity  OnboardingScreen android.app.Activity  
ProfileScreen android.app.Activity  ProfileViewModel android.app.Activity  Surface android.app.Activity  WrongQuestionScreen android.app.Activity  WrongQuestionViewModel android.app.Activity  fillMaxSize android.app.Activity  finish android.app.Activity  getValue android.app.Activity  	getWINDOW android.app.Activity  	getWindow android.app.Activity  onCreate android.app.Activity  provideDelegate android.app.Activity  
setContent android.app.Activity  	setWindow android.app.Activity  
viewModels android.app.Activity  window android.app.Activity  Context android.content  Intent android.content  
AuthScreen android.content.Context  
AuthViewModel android.content.Context  Bundle android.content.Context  ContentViewModel android.content.Context   ElementaryLearningCompanionTheme android.content.Context  
EnglishScreen android.content.Context  EnglishViewModel android.content.Context  
HomeScreen android.content.Context  LessonListScreen android.content.Context  LessonScreen android.content.Context  
MaterialTheme android.content.Context  
MathScreen android.content.Context  
MathViewModel android.content.Context  Modifier android.content.Context  OnboardingScreen android.content.Context  
ProfileScreen android.content.Context  ProfileViewModel android.content.Context  Surface android.content.Context  WrongQuestionScreen android.content.Context  WrongQuestionViewModel android.content.Context  fillMaxSize android.content.Context  finish android.content.Context  getValue android.content.Context  onCreate android.content.Context  provideDelegate android.content.Context  
setContent android.content.Context  
startActivity android.content.Context  
viewModels android.content.Context  
AuthScreen android.content.ContextWrapper  
AuthViewModel android.content.ContextWrapper  Bundle android.content.ContextWrapper  ContentViewModel android.content.ContextWrapper   ElementaryLearningCompanionTheme android.content.ContextWrapper  
EnglishScreen android.content.ContextWrapper  EnglishViewModel android.content.ContextWrapper  
HomeScreen android.content.ContextWrapper  LessonListScreen android.content.ContextWrapper  LessonScreen android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  
MathScreen android.content.ContextWrapper  
MathViewModel android.content.ContextWrapper  Modifier android.content.ContextWrapper  OnboardingScreen android.content.ContextWrapper  
ProfileScreen android.content.ContextWrapper  ProfileViewModel android.content.ContextWrapper  Surface android.content.ContextWrapper  WrongQuestionScreen android.content.ContextWrapper  WrongQuestionViewModel android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  finish android.content.ContextWrapper  getValue android.content.ContextWrapper  onCreate android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  
setContent android.content.ContextWrapper  
viewModels android.content.ContextWrapper  getIntExtra android.content.Intent  getLongExtra android.content.Intent  putExtra android.content.Intent  Build 
android.os  Bundle 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  View android.view  Window android.view  
AuthScreen  android.view.ContextThemeWrapper  
AuthViewModel  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  ContentViewModel  android.view.ContextThemeWrapper   ElementaryLearningCompanionTheme  android.view.ContextThemeWrapper  
EnglishScreen  android.view.ContextThemeWrapper  EnglishViewModel  android.view.ContextThemeWrapper  
HomeScreen  android.view.ContextThemeWrapper  LessonListScreen  android.view.ContextThemeWrapper  LessonScreen  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  
MathScreen  android.view.ContextThemeWrapper  
MathViewModel  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  OnboardingScreen  android.view.ContextThemeWrapper  
ProfileScreen  android.view.ContextThemeWrapper  ProfileViewModel  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  WrongQuestionScreen  android.view.ContextThemeWrapper  WrongQuestionViewModel  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  
viewModels  android.view.ContextThemeWrapper  context android.view.View  
getCONTEXT android.view.View  
getContext android.view.View  getISInEditMode android.view.View  getIsInEditMode android.view.View  isInEditMode android.view.View  
setContext android.view.View  
setInEditMode android.view.View  getSTATUSBarColor android.view.Window  getStatusBarColor android.view.Window  setStatusBarColor android.view.Window  statusBarColor android.view.Window  Toast android.widget  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  
viewModels androidx.activity  
AuthScreen #androidx.activity.ComponentActivity  
AuthViewModel #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  ContentViewModel #androidx.activity.ComponentActivity   ElementaryLearningCompanionTheme #androidx.activity.ComponentActivity  
EnglishScreen #androidx.activity.ComponentActivity  EnglishViewModel #androidx.activity.ComponentActivity  
HomeScreen #androidx.activity.ComponentActivity  LessonListScreen #androidx.activity.ComponentActivity  LessonScreen #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  
MathScreen #androidx.activity.ComponentActivity  
MathViewModel #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  OnboardingScreen #androidx.activity.ComponentActivity  
ProfileScreen #androidx.activity.ComponentActivity  ProfileViewModel #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  WrongQuestionScreen #androidx.activity.ComponentActivity  WrongQuestionViewModel #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
viewModels #androidx.activity.ComponentActivity  
AuthScreen -androidx.activity.ComponentActivity.Companion   ElementaryLearningCompanionTheme -androidx.activity.ComponentActivity.Companion  
EnglishScreen -androidx.activity.ComponentActivity.Companion  
HomeScreen -androidx.activity.ComponentActivity.Companion  LessonListScreen -androidx.activity.ComponentActivity.Companion  LessonScreen -androidx.activity.ComponentActivity.Companion  
MaterialTheme -androidx.activity.ComponentActivity.Companion  
MathScreen -androidx.activity.ComponentActivity.Companion  Modifier -androidx.activity.ComponentActivity.Companion  OnboardingScreen -androidx.activity.ComponentActivity.Companion  
ProfileScreen -androidx.activity.ComponentActivity.Companion  Surface -androidx.activity.ComponentActivity.Companion  WrongQuestionScreen -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  getFILLMaxSize -androidx.activity.ComponentActivity.Companion  getFillMaxSize -androidx.activity.ComponentActivity.Companion  getGETValue -androidx.activity.ComponentActivity.Companion  getGetValue -androidx.activity.ComponentActivity.Companion  getPROVIDEDelegate -androidx.activity.ComponentActivity.Companion  getProvideDelegate -androidx.activity.ComponentActivity.Companion  getValue -androidx.activity.ComponentActivity.Companion  provideDelegate -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
viewModels -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
AuthScreen "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  	Calculate "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  
CharacterItem "androidx.compose.foundation.layout  CharactersList "androidx.compose.foundation.layout  ChevronRight "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Class "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Divider "androidx.compose.foundation.layout  EditProfileDialog "androidx.compose.foundation.layout   ElementaryLearningCompanionTheme "androidx.compose.foundation.layout  EnglishActivity "androidx.compose.foundation.layout  
EnglishScreen "androidx.compose.foundation.layout  ErrorOutline "androidx.compose.foundation.layout  ExerciseScreen "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  
FilterChip "androidx.compose.foundation.layout  
FilterTabs "androidx.compose.foundation.layout  
FlashcardMode "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  HomeActivity "androidx.compose.foundation.layout  
HomeScreen "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Intent "androidx.compose.foundation.layout  Language "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LearningModeSelection "androidx.compose.foundation.layout  LessonActivity "androidx.compose.foundation.layout  LessonListActivity "androidx.compose.foundation.layout  LessonListItem "androidx.compose.foundation.layout  LessonListScreen "androidx.compose.foundation.layout  LessonScreen "androidx.compose.foundation.layout  
ListeningMode "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  MathActivity "androidx.compose.foundation.layout  
MathScreen "androidx.compose.foundation.layout  MenuBook "androidx.compose.foundation.layout  MenuItem "androidx.compose.foundation.layout  MenuItemCard "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OnboardingActivity "androidx.compose.foundation.layout  OnboardingScreen "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  PasswordVisualTransformation "androidx.compose.foundation.layout  Person "androidx.compose.foundation.layout  PracticeTypeSelection "androidx.compose.foundation.layout  ProfileActivity "androidx.compose.foundation.layout  ProfileCard "androidx.compose.foundation.layout  
ProfileScreen "androidx.compose.foundation.layout  QuickActionsCard "androidx.compose.foundation.layout  QuizMode "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  SentenceItem "androidx.compose.foundation.layout  
SentencesList "androidx.compose.foundation.layout  SettingsCard "androidx.compose.foundation.layout  SettingsDialog "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  SpellingMode "androidx.compose.foundation.layout  
StatisticItem "androidx.compose.foundation.layout  StatisticsCard "androidx.compose.foundation.layout  Surface "androidx.compose.foundation.layout  System "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TextbookItem "androidx.compose.foundation.layout  Toast "androidx.compose.foundation.layout  WordLearningScreen "androidx.compose.foundation.layout  WrongQuestionActivity "androidx.compose.foundation.layout  WrongQuestionItem "androidx.compose.foundation.layout  WrongQuestionScreen "androidx.compose.foundation.layout  WrongQuestionStatisticItem "androidx.compose.foundation.layout  WrongQuestionStatisticsCard "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  contains "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  emptyMap "androidx.compose.foundation.layout  equals "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  items "androidx.compose.foundation.layout  java "androidx.compose.foundation.layout  last "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  
setContent "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  to "androidx.compose.foundation.layout  
viewModels "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Start .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  CharactersList +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  OutlinedTextField +androidx.compose.foundation.layout.BoxScope  PasswordVisualTransformation +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  
SentencesList +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  
TextButton +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  	emptyList +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  getEMPTYList +androidx.compose.foundation.layout.BoxScope  getEmptyList +androidx.compose.foundation.layout.BoxScope  getFILLMaxSize +androidx.compose.foundation.layout.BoxScope  getFILLMaxWidth +androidx.compose.foundation.layout.BoxScope  getFillMaxSize +androidx.compose.foundation.layout.BoxScope  getFillMaxWidth +androidx.compose.foundation.layout.BoxScope  	getHEIGHT +androidx.compose.foundation.layout.BoxScope  	getHeight +androidx.compose.foundation.layout.BoxScope  getLET +androidx.compose.foundation.layout.BoxScope  getLet +androidx.compose.foundation.layout.BoxScope  
getPADDING +androidx.compose.foundation.layout.BoxScope  
getPadding +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  ArrowForward .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  	Calculate .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CharactersList .androidx.compose.foundation.layout.ColumnScope  ChevronRight .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  Divider .androidx.compose.foundation.layout.ColumnScope  Done .androidx.compose.foundation.layout.ColumnScope  Edit .androidx.compose.foundation.layout.ColumnScope  EditProfileDialog .androidx.compose.foundation.layout.ColumnScope  EnglishActivity .androidx.compose.foundation.layout.ColumnScope  ErrorOutline .androidx.compose.foundation.layout.ColumnScope  ExerciseScreen .androidx.compose.foundation.layout.ColumnScope  
FilterChip .androidx.compose.foundation.layout.ColumnScope  
FilterTabs .androidx.compose.foundation.layout.ColumnScope  
FlashcardMode .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  HomeActivity .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Intent .androidx.compose.foundation.layout.ColumnScope  Language .androidx.compose.foundation.layout.ColumnScope  LaunchedEffect .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LearningModeSelection .androidx.compose.foundation.layout.ColumnScope  LessonActivity .androidx.compose.foundation.layout.ColumnScope  LessonListActivity .androidx.compose.foundation.layout.ColumnScope  LessonListItem .androidx.compose.foundation.layout.ColumnScope  
ListeningMode .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  MathActivity .androidx.compose.foundation.layout.ColumnScope  MenuBook .androidx.compose.foundation.layout.ColumnScope  MenuItem .androidx.compose.foundation.layout.ColumnScope  MenuItemCard .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  PasswordVisualTransformation .androidx.compose.foundation.layout.ColumnScope  Person .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  PracticeTypeSelection .androidx.compose.foundation.layout.ColumnScope  ProfileActivity .androidx.compose.foundation.layout.ColumnScope  ProfileCard .androidx.compose.foundation.layout.ColumnScope  QuickActionsCard .androidx.compose.foundation.layout.ColumnScope  QuizMode .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  
SentencesList .androidx.compose.foundation.layout.ColumnScope  Settings .androidx.compose.foundation.layout.ColumnScope  SettingsCard .androidx.compose.foundation.layout.ColumnScope  SettingsDialog .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  SpellingMode .androidx.compose.foundation.layout.ColumnScope  
StatisticItem .androidx.compose.foundation.layout.ColumnScope  StatisticsCard .androidx.compose.foundation.layout.ColumnScope  System .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  TextbookItem .androidx.compose.foundation.layout.ColumnScope  Toast .androidx.compose.foundation.layout.ColumnScope  WordLearningScreen .androidx.compose.foundation.layout.ColumnScope  WrongQuestionActivity .androidx.compose.foundation.layout.ColumnScope  WrongQuestionItem .androidx.compose.foundation.layout.ColumnScope  WrongQuestionStatisticItem .androidx.compose.foundation.layout.ColumnScope  WrongQuestionStatisticsCard .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  	emptyList .androidx.compose.foundation.layout.ColumnScope  equals .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  getEMPTYList .androidx.compose.foundation.layout.ColumnScope  	getEQUALS .androidx.compose.foundation.layout.ColumnScope  getEmptyList .androidx.compose.foundation.layout.ColumnScope  	getEquals .androidx.compose.foundation.layout.ColumnScope  getFILLMaxSize .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFillMaxSize .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  
getISNotBlank .androidx.compose.foundation.layout.ColumnScope  
getISNotEmpty .androidx.compose.foundation.layout.ColumnScope  
getIsNotBlank .androidx.compose.foundation.layout.ColumnScope  
getIsNotEmpty .androidx.compose.foundation.layout.ColumnScope  getLAST .androidx.compose.foundation.layout.ColumnScope  getLET .androidx.compose.foundation.layout.ColumnScope  	getLISTOf .androidx.compose.foundation.layout.ColumnScope  getLast .androidx.compose.foundation.layout.ColumnScope  getLet .androidx.compose.foundation.layout.ColumnScope  	getListOf .androidx.compose.foundation.layout.ColumnScope  
getPADDING .androidx.compose.foundation.layout.ColumnScope  
getPadding .androidx.compose.foundation.layout.ColumnScope  getSIZE .androidx.compose.foundation.layout.ColumnScope  getSize .androidx.compose.foundation.layout.ColumnScope  getTO .androidx.compose.foundation.layout.ColumnScope  getTo .androidx.compose.foundation.layout.ColumnScope  getWIDTH .androidx.compose.foundation.layout.ColumnScope  getWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  java .androidx.compose.foundation.layout.ColumnScope  last .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  to .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  ArrowForward +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ChevronRight +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  Done +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  
FilterChip +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  HomeActivity +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  Intent +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Person +androidx.compose.foundation.layout.RowScope  	PlayArrow +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Settings +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  
StatisticItem +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  Toast +androidx.compose.foundation.layout.RowScope  WrongQuestionStatisticItem +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  	getHEIGHT +androidx.compose.foundation.layout.RowScope  	getHeight +androidx.compose.foundation.layout.RowScope  getSIZE +androidx.compose.foundation.layout.RowScope  getSize +androidx.compose.foundation.layout.RowScope  getWIDTH +androidx.compose.foundation.layout.RowScope  getWidth +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  java +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  Card .androidx.compose.foundation.lazy.LazyItemScope  
CharacterItem .androidx.compose.foundation.lazy.LazyItemScope  Column .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  Intent .androidx.compose.foundation.lazy.LazyItemScope  LessonActivity .androidx.compose.foundation.lazy.LazyItemScope  LessonListItem .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  MenuItemCard .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  ProfileCard .androidx.compose.foundation.lazy.LazyItemScope  QuickActionsCard .androidx.compose.foundation.lazy.LazyItemScope  SentenceItem .androidx.compose.foundation.lazy.LazyItemScope  SettingsCard .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  StatisticsCard .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  TextbookItem .androidx.compose.foundation.lazy.LazyItemScope  WrongQuestionItem .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  getFILLMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  getFillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  	getHEIGHT .androidx.compose.foundation.lazy.LazyItemScope  	getHeight .androidx.compose.foundation.lazy.LazyItemScope  
getISNotEmpty .androidx.compose.foundation.lazy.LazyItemScope  
getIsNotEmpty .androidx.compose.foundation.lazy.LazyItemScope  getLET .androidx.compose.foundation.lazy.LazyItemScope  getLet .androidx.compose.foundation.lazy.LazyItemScope  
getPADDING .androidx.compose.foundation.lazy.LazyItemScope  
getPadding .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyItemScope  java .androidx.compose.foundation.lazy.LazyItemScope  let .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  Card .androidx.compose.foundation.lazy.LazyListScope  
CharacterItem .androidx.compose.foundation.lazy.LazyListScope  Column .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  Intent .androidx.compose.foundation.lazy.LazyListScope  LessonActivity .androidx.compose.foundation.lazy.LazyListScope  LessonListItem .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  MenuItemCard .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  ProfileCard .androidx.compose.foundation.lazy.LazyListScope  QuickActionsCard .androidx.compose.foundation.lazy.LazyListScope  SentenceItem .androidx.compose.foundation.lazy.LazyListScope  SettingsCard .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  StatisticsCard .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  TextbookItem .androidx.compose.foundation.lazy.LazyListScope  WrongQuestionItem .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  getFILLMaxWidth .androidx.compose.foundation.lazy.LazyListScope  getFillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  	getHEIGHT .androidx.compose.foundation.lazy.LazyListScope  	getHeight .androidx.compose.foundation.lazy.LazyListScope  
getISNotEmpty .androidx.compose.foundation.lazy.LazyListScope  getITEMS .androidx.compose.foundation.lazy.LazyListScope  
getIsNotEmpty .androidx.compose.foundation.lazy.LazyListScope  getItems .androidx.compose.foundation.lazy.LazyListScope  getLET .androidx.compose.foundation.lazy.LazyListScope  getLet .androidx.compose.foundation.lazy.LazyListScope  
getPADDING .androidx.compose.foundation.lazy.LazyListScope  
getPadding .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  java .androidx.compose.foundation.lazy.LazyListScope  let .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  ArrowForward ,androidx.compose.material.icons.Icons.Filled  	Calculate ,androidx.compose.material.icons.Icons.Filled  ChevronRight ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  Done ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  ErrorOutline ,androidx.compose.material.icons.Icons.Filled  Language ,androidx.compose.material.icons.Icons.Filled  MenuBook ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  	Alignment &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  ArrowForward &androidx.compose.material.icons.filled  	Calculate &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  ChevronRight &androidx.compose.material.icons.filled  Class &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Done &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled   ElementaryLearningCompanionTheme &androidx.compose.material.icons.filled  EnglishActivity &androidx.compose.material.icons.filled  ErrorOutline &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  
HomeScreen &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  Intent &androidx.compose.material.icons.filled  Language &androidx.compose.material.icons.filled  
LazyColumn &androidx.compose.material.icons.filled  LessonActivity &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  MathActivity &androidx.compose.material.icons.filled  MenuBook &androidx.compose.material.icons.filled  MenuItem &androidx.compose.material.icons.filled  MenuItemCard &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  ProfileActivity &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  Surface &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  WrongQuestionActivity &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  items &androidx.compose.material.icons.filled  java &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  
setContent &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  
AuthScreen androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  	Calculate androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  
CharacterItem androidx.compose.material3  CharactersList androidx.compose.material3  ChevronRight androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Class androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  Divider androidx.compose.material3  EditProfileDialog androidx.compose.material3   ElementaryLearningCompanionTheme androidx.compose.material3  EnglishActivity androidx.compose.material3  
EnglishScreen androidx.compose.material3  ErrorOutline androidx.compose.material3  ExerciseScreen androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
FilterChip androidx.compose.material3  
FilterTabs androidx.compose.material3  
FlashcardMode androidx.compose.material3  
FontWeight androidx.compose.material3  HomeActivity androidx.compose.material3  
HomeScreen androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  Intent androidx.compose.material3  Language androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  LearningModeSelection androidx.compose.material3  LessonActivity androidx.compose.material3  LessonListActivity androidx.compose.material3  LessonListItem androidx.compose.material3  LessonListScreen androidx.compose.material3  LessonScreen androidx.compose.material3  
ListeningMode androidx.compose.material3  
MaterialTheme androidx.compose.material3  MathActivity androidx.compose.material3  
MathScreen androidx.compose.material3  MenuBook androidx.compose.material3  MenuItem androidx.compose.material3  MenuItemCard androidx.compose.material3  Modifier androidx.compose.material3  OnboardingActivity androidx.compose.material3  OnboardingScreen androidx.compose.material3  OutlinedTextField androidx.compose.material3  PasswordVisualTransformation androidx.compose.material3  Person androidx.compose.material3  PracticeTypeSelection androidx.compose.material3  ProfileActivity androidx.compose.material3  ProfileCard androidx.compose.material3  
ProfileScreen androidx.compose.material3  QuickActionsCard androidx.compose.material3  QuizMode androidx.compose.material3  Row androidx.compose.material3  SentenceItem androidx.compose.material3  
SentencesList androidx.compose.material3  SettingsCard androidx.compose.material3  SettingsDialog androidx.compose.material3  Spacer androidx.compose.material3  SpellingMode androidx.compose.material3  
StatisticItem androidx.compose.material3  StatisticsCard androidx.compose.material3  Surface androidx.compose.material3  System androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  TextbookItem androidx.compose.material3  Toast androidx.compose.material3  
Typography androidx.compose.material3  WordLearningScreen androidx.compose.material3  WrongQuestionActivity androidx.compose.material3  WrongQuestionItem androidx.compose.material3  WrongQuestionScreen androidx.compose.material3  WrongQuestionStatisticItem androidx.compose.material3  WrongQuestionStatisticsCard androidx.compose.material3  com androidx.compose.material3  contains androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  	emptyList androidx.compose.material3  emptyMap androidx.compose.material3  equals androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  
isNotBlank androidx.compose.material3  
isNotEmpty androidx.compose.material3  items androidx.compose.material3  java androidx.compose.material3  last androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  
setContent androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  to androidx.compose.material3  
viewModels androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  error &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  	secondary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  
headlineLarge %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  labelMedium %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  
titleSmall %androidx.compose.material3.Typography  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  
AuthScreen androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  
CharacterItem androidx.compose.runtime  CharactersList androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  Divider androidx.compose.runtime  EditProfileDialog androidx.compose.runtime   ElementaryLearningCompanionTheme androidx.compose.runtime  
EnglishScreen androidx.compose.runtime  ExerciseScreen androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  
FilterChip androidx.compose.runtime  
FilterTabs androidx.compose.runtime  
FlashcardMode androidx.compose.runtime  
FontWeight androidx.compose.runtime  HomeActivity androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Intent androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LearningModeSelection androidx.compose.runtime  LessonActivity androidx.compose.runtime  LessonListActivity androidx.compose.runtime  LessonListItem androidx.compose.runtime  LessonListScreen androidx.compose.runtime  LessonScreen androidx.compose.runtime  
ListeningMode androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  
MathScreen androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  OnboardingActivity androidx.compose.runtime  OnboardingScreen androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  PasswordVisualTransformation androidx.compose.runtime  PracticeTypeSelection androidx.compose.runtime  ProfileCard androidx.compose.runtime  
ProfileScreen androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  QuickActionsCard androidx.compose.runtime  QuizMode androidx.compose.runtime  Row androidx.compose.runtime  SentenceItem androidx.compose.runtime  
SentencesList androidx.compose.runtime  SettingsCard androidx.compose.runtime  SettingsDialog androidx.compose.runtime  
SideEffect androidx.compose.runtime  Spacer androidx.compose.runtime  SpellingMode androidx.compose.runtime  State androidx.compose.runtime  
StatisticItem androidx.compose.runtime  StatisticsCard androidx.compose.runtime  Surface androidx.compose.runtime  System androidx.compose.runtime  Text androidx.compose.runtime  
TextButton androidx.compose.runtime  TextbookItem androidx.compose.runtime  Toast androidx.compose.runtime  WordLearningScreen androidx.compose.runtime  WrongQuestionItem androidx.compose.runtime  WrongQuestionScreen androidx.compose.runtime  WrongQuestionStatisticItem androidx.compose.runtime  WrongQuestionStatisticsCard androidx.compose.runtime  com androidx.compose.runtime  contains androidx.compose.runtime  	emptyList androidx.compose.runtime  emptyMap androidx.compose.runtime  equals androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  
isNotBlank androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  items androidx.compose.runtime  java androidx.compose.runtime  last androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  
setContent androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  to androidx.compose.runtime  
viewModels androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getGETValue androidx.compose.runtime.State  getGetValue androidx.compose.runtime.State  getPROVIDEDelegate androidx.compose.runtime.State  getProvideDelegate androidx.compose.runtime.State  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  observeAsState !androidx.compose.runtime.livedata  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Top androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  Top 'androidx.compose.ui.Alignment.Companion  	clickable androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  getCLICKABLE androidx.compose.ui.Modifier  getClickable androidx.compose.ui.Modifier  	getHEIGHT androidx.compose.ui.Modifier  	getHeight androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  
getPADDING &androidx.compose.ui.Modifier.Companion  
getPadding &androidx.compose.ui.Modifier.Companion  getSIZE &androidx.compose.ui.Modifier.Companion  getSize &androidx.compose.ui.Modifier.Companion  	getWEIGHT &androidx.compose.ui.Modifier.Companion  getWIDTH &androidx.compose.ui.Modifier.Companion  	getWeight &androidx.compose.ui.Modifier.Companion  getWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  	getTOArgb "androidx.compose.ui.graphics.Color  	getToArgb "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  PasswordVisualTransformation androidx.compose.ui.text.input  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  
AuthScreen #androidx.core.app.ComponentActivity  
AuthViewModel #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  ContentViewModel #androidx.core.app.ComponentActivity   ElementaryLearningCompanionTheme #androidx.core.app.ComponentActivity  
EnglishScreen #androidx.core.app.ComponentActivity  EnglishViewModel #androidx.core.app.ComponentActivity  
HomeScreen #androidx.core.app.ComponentActivity  LessonListScreen #androidx.core.app.ComponentActivity  LessonScreen #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  
MathScreen #androidx.core.app.ComponentActivity  
MathViewModel #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  OnboardingScreen #androidx.core.app.ComponentActivity  
ProfileScreen #androidx.core.app.ComponentActivity  ProfileViewModel #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  WrongQuestionScreen #androidx.core.app.ComponentActivity  WrongQuestionViewModel #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  finish #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  
viewModels #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  getISAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  getIsAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  setAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  getOBSERVEAsState androidx.lifecycle.LiveData  getObserveAsState androidx.lifecycle.LiveData  observeAsState androidx.lifecycle.LiveData  clearOperationResult androidx.lifecycle.ViewModel  clearRecordResult androidx.lifecycle.ViewModel  clearSubmitResult androidx.lifecycle.ViewModel  clearUpdateResult androidx.lifecycle.ViewModel  deleteWrongQuestion androidx.lifecycle.ViewModel  fetchCharactersByLesson androidx.lifecycle.ViewModel  fetchLessonDetails androidx.lifecycle.ViewModel  fetchLessons androidx.lifecycle.ViewModel  fetchTextbooks androidx.lifecycle.ViewModel  generateExercises androidx.lifecycle.ViewModel  loadAvailableGrades androidx.lifecycle.ViewModel  loadDetailedWrongQuestions androidx.lifecycle.ViewModel  loadLearningModes androidx.lifecycle.ViewModel  loadPracticeTypes androidx.lifecycle.ViewModel  loadRandomWords androidx.lifecycle.ViewModel  loadSettings androidx.lifecycle.ViewModel  loadSpellingTest androidx.lifecycle.ViewModel  loadStatistics androidx.lifecycle.ViewModel  loadStudentProfile androidx.lifecycle.ViewModel  loadWrongQuestionsByType androidx.lifecycle.ViewModel  login androidx.lifecycle.ViewModel  markAsMastered androidx.lifecycle.ViewModel  recordLearning androidx.lifecycle.ViewModel  register androidx.lifecycle.ViewModel  submitAnswer androidx.lifecycle.ViewModel  updateSettings androidx.lifecycle.ViewModel  updateStudentProfile androidx.lifecycle.ViewModel  AlertDialog 'com.example.elementarylearningcompanion  	Alignment 'com.example.elementarylearningcompanion  Any 'com.example.elementarylearningcompanion  Arrangement 'com.example.elementarylearningcompanion  
AuthScreen 'com.example.elementarylearningcompanion  AuthScreenPreview 'com.example.elementarylearningcompanion  Boolean 'com.example.elementarylearningcompanion  Box 'com.example.elementarylearningcompanion  Button 'com.example.elementarylearningcompanion  ButtonDefaults 'com.example.elementarylearningcompanion  	Calculate 'com.example.elementarylearningcompanion  Card 'com.example.elementarylearningcompanion  CardDefaults 'com.example.elementarylearningcompanion  
CharacterItem 'com.example.elementarylearningcompanion  CharactersList 'com.example.elementarylearningcompanion  ChevronRight 'com.example.elementarylearningcompanion  CircularProgressIndicator 'com.example.elementarylearningcompanion  Class 'com.example.elementarylearningcompanion  Column 'com.example.elementarylearningcompanion  
Composable 'com.example.elementarylearningcompanion  Divider 'com.example.elementarylearningcompanion  Double 'com.example.elementarylearningcompanion  EditProfileDialog 'com.example.elementarylearningcompanion   ElementaryLearningCompanionTheme 'com.example.elementarylearningcompanion  EnglishActivity 'com.example.elementarylearningcompanion  
EnglishScreen 'com.example.elementarylearningcompanion  ErrorOutline 'com.example.elementarylearningcompanion  ExerciseScreen 'com.example.elementarylearningcompanion  ExperimentalMaterial3Api 'com.example.elementarylearningcompanion  
FilterChip 'com.example.elementarylearningcompanion  
FilterTabs 'com.example.elementarylearningcompanion  
FlashcardMode 'com.example.elementarylearningcompanion  
FontWeight 'com.example.elementarylearningcompanion  HomeActivity 'com.example.elementarylearningcompanion  
HomeScreen 'com.example.elementarylearningcompanion  Icon 'com.example.elementarylearningcompanion  
IconButton 'com.example.elementarylearningcompanion  Icons 'com.example.elementarylearningcompanion  Int 'com.example.elementarylearningcompanion  Intent 'com.example.elementarylearningcompanion  Language 'com.example.elementarylearningcompanion  LaunchedEffect 'com.example.elementarylearningcompanion  
LazyColumn 'com.example.elementarylearningcompanion  LearningModeSelection 'com.example.elementarylearningcompanion  LessonActivity 'com.example.elementarylearningcompanion  LessonListActivity 'com.example.elementarylearningcompanion  LessonListItem 'com.example.elementarylearningcompanion  LessonListScreen 'com.example.elementarylearningcompanion  LessonScreen 'com.example.elementarylearningcompanion  List 'com.example.elementarylearningcompanion  
ListeningMode 'com.example.elementarylearningcompanion  Long 'com.example.elementarylearningcompanion  MainActivity 'com.example.elementarylearningcompanion  Map 'com.example.elementarylearningcompanion  
MaterialTheme 'com.example.elementarylearningcompanion  MathActivity 'com.example.elementarylearningcompanion  
MathScreen 'com.example.elementarylearningcompanion  MenuBook 'com.example.elementarylearningcompanion  MenuItem 'com.example.elementarylearningcompanion  MenuItemCard 'com.example.elementarylearningcompanion  Modifier 'com.example.elementarylearningcompanion  OnboardingActivity 'com.example.elementarylearningcompanion  OnboardingScreen 'com.example.elementarylearningcompanion  OnboardingScreenPreview 'com.example.elementarylearningcompanion  OptIn 'com.example.elementarylearningcompanion  OutlinedTextField 'com.example.elementarylearningcompanion  PasswordVisualTransformation 'com.example.elementarylearningcompanion  Person 'com.example.elementarylearningcompanion  PracticeTypeSelection 'com.example.elementarylearningcompanion  ProfileActivity 'com.example.elementarylearningcompanion  ProfileCard 'com.example.elementarylearningcompanion  
ProfileScreen 'com.example.elementarylearningcompanion  QuickActionsCard 'com.example.elementarylearningcompanion  QuizMode 'com.example.elementarylearningcompanion  Row 'com.example.elementarylearningcompanion  SentenceItem 'com.example.elementarylearningcompanion  
SentencesList 'com.example.elementarylearningcompanion  SettingsCard 'com.example.elementarylearningcompanion  SettingsDialog 'com.example.elementarylearningcompanion  Spacer 'com.example.elementarylearningcompanion  SpellingMode 'com.example.elementarylearningcompanion  
StatisticItem 'com.example.elementarylearningcompanion  StatisticsCard 'com.example.elementarylearningcompanion  String 'com.example.elementarylearningcompanion  Surface 'com.example.elementarylearningcompanion  System 'com.example.elementarylearningcompanion  Text 'com.example.elementarylearningcompanion  
TextButton 'com.example.elementarylearningcompanion  TextbookItem 'com.example.elementarylearningcompanion  Toast 'com.example.elementarylearningcompanion  Unit 'com.example.elementarylearningcompanion  WordLearningScreen 'com.example.elementarylearningcompanion  WrongQuestionActivity 'com.example.elementarylearningcompanion  WrongQuestionItem 'com.example.elementarylearningcompanion  WrongQuestionScreen 'com.example.elementarylearningcompanion  WrongQuestionStatisticItem 'com.example.elementarylearningcompanion  WrongQuestionStatisticsCard 'com.example.elementarylearningcompanion  com 'com.example.elementarylearningcompanion  contains 'com.example.elementarylearningcompanion  	emptyList 'com.example.elementarylearningcompanion  emptyMap 'com.example.elementarylearningcompanion  equals 'com.example.elementarylearningcompanion  fillMaxSize 'com.example.elementarylearningcompanion  fillMaxWidth 'com.example.elementarylearningcompanion  forEach 'com.example.elementarylearningcompanion  getValue 'com.example.elementarylearningcompanion  height 'com.example.elementarylearningcompanion  
isNotBlank 'com.example.elementarylearningcompanion  
isNotEmpty 'com.example.elementarylearningcompanion  items 'com.example.elementarylearningcompanion  java 'com.example.elementarylearningcompanion  last 'com.example.elementarylearningcompanion  let 'com.example.elementarylearningcompanion  listOf 'com.example.elementarylearningcompanion  mutableStateOf 'com.example.elementarylearningcompanion  padding 'com.example.elementarylearningcompanion  provideDelegate 'com.example.elementarylearningcompanion  remember 'com.example.elementarylearningcompanion  
setContent 'com.example.elementarylearningcompanion  setValue 'com.example.elementarylearningcompanion  size 'com.example.elementarylearningcompanion  to 'com.example.elementarylearningcompanion  
viewModels 'com.example.elementarylearningcompanion  width 'com.example.elementarylearningcompanion  Bundle 7com.example.elementarylearningcompanion.EnglishActivity   ElementaryLearningCompanionTheme 7com.example.elementarylearningcompanion.EnglishActivity  
EnglishScreen 7com.example.elementarylearningcompanion.EnglishActivity  EnglishViewModel 7com.example.elementarylearningcompanion.EnglishActivity  
MaterialTheme 7com.example.elementarylearningcompanion.EnglishActivity  Modifier 7com.example.elementarylearningcompanion.EnglishActivity  Surface 7com.example.elementarylearningcompanion.EnglishActivity  fillMaxSize 7com.example.elementarylearningcompanion.EnglishActivity  getFILLMaxSize 7com.example.elementarylearningcompanion.EnglishActivity  getFillMaxSize 7com.example.elementarylearningcompanion.EnglishActivity  getGETValue 7com.example.elementarylearningcompanion.EnglishActivity  getGetValue 7com.example.elementarylearningcompanion.EnglishActivity  getPROVIDEDelegate 7com.example.elementarylearningcompanion.EnglishActivity  getProvideDelegate 7com.example.elementarylearningcompanion.EnglishActivity  
getSETContent 7com.example.elementarylearningcompanion.EnglishActivity  
getSetContent 7com.example.elementarylearningcompanion.EnglishActivity  
getVIEWModels 7com.example.elementarylearningcompanion.EnglishActivity  getValue 7com.example.elementarylearningcompanion.EnglishActivity  
getViewModels 7com.example.elementarylearningcompanion.EnglishActivity  provideDelegate 7com.example.elementarylearningcompanion.EnglishActivity  
setContent 7com.example.elementarylearningcompanion.EnglishActivity  	viewModel 7com.example.elementarylearningcompanion.EnglishActivity  
viewModels 7com.example.elementarylearningcompanion.EnglishActivity  Bundle 4com.example.elementarylearningcompanion.HomeActivity   ElementaryLearningCompanionTheme 4com.example.elementarylearningcompanion.HomeActivity  
HomeScreen 4com.example.elementarylearningcompanion.HomeActivity  
MaterialTheme 4com.example.elementarylearningcompanion.HomeActivity  Modifier 4com.example.elementarylearningcompanion.HomeActivity  Surface 4com.example.elementarylearningcompanion.HomeActivity  fillMaxSize 4com.example.elementarylearningcompanion.HomeActivity  getFILLMaxSize 4com.example.elementarylearningcompanion.HomeActivity  getFillMaxSize 4com.example.elementarylearningcompanion.HomeActivity  
getSETContent 4com.example.elementarylearningcompanion.HomeActivity  
getSetContent 4com.example.elementarylearningcompanion.HomeActivity  
setContent 4com.example.elementarylearningcompanion.HomeActivity  Bundle 6com.example.elementarylearningcompanion.LessonActivity  ContentViewModel 6com.example.elementarylearningcompanion.LessonActivity   ElementaryLearningCompanionTheme 6com.example.elementarylearningcompanion.LessonActivity  LessonScreen 6com.example.elementarylearningcompanion.LessonActivity  
MaterialTheme 6com.example.elementarylearningcompanion.LessonActivity  Modifier 6com.example.elementarylearningcompanion.LessonActivity  Surface 6com.example.elementarylearningcompanion.LessonActivity  fillMaxSize 6com.example.elementarylearningcompanion.LessonActivity  getFILLMaxSize 6com.example.elementarylearningcompanion.LessonActivity  getFillMaxSize 6com.example.elementarylearningcompanion.LessonActivity  getGETValue 6com.example.elementarylearningcompanion.LessonActivity  getGetValue 6com.example.elementarylearningcompanion.LessonActivity  	getINTENT 6com.example.elementarylearningcompanion.LessonActivity  	getIntent 6com.example.elementarylearningcompanion.LessonActivity  getPROVIDEDelegate 6com.example.elementarylearningcompanion.LessonActivity  getProvideDelegate 6com.example.elementarylearningcompanion.LessonActivity  
getSETContent 6com.example.elementarylearningcompanion.LessonActivity  
getSetContent 6com.example.elementarylearningcompanion.LessonActivity  
getVIEWModels 6com.example.elementarylearningcompanion.LessonActivity  getValue 6com.example.elementarylearningcompanion.LessonActivity  
getViewModels 6com.example.elementarylearningcompanion.LessonActivity  intent 6com.example.elementarylearningcompanion.LessonActivity  provideDelegate 6com.example.elementarylearningcompanion.LessonActivity  
setContent 6com.example.elementarylearningcompanion.LessonActivity  	setIntent 6com.example.elementarylearningcompanion.LessonActivity  	viewModel 6com.example.elementarylearningcompanion.LessonActivity  
viewModels 6com.example.elementarylearningcompanion.LessonActivity  Bundle :com.example.elementarylearningcompanion.LessonListActivity  ContentViewModel :com.example.elementarylearningcompanion.LessonListActivity   ElementaryLearningCompanionTheme :com.example.elementarylearningcompanion.LessonListActivity  LessonListScreen :com.example.elementarylearningcompanion.LessonListActivity  
MaterialTheme :com.example.elementarylearningcompanion.LessonListActivity  Modifier :com.example.elementarylearningcompanion.LessonListActivity  Surface :com.example.elementarylearningcompanion.LessonListActivity  fillMaxSize :com.example.elementarylearningcompanion.LessonListActivity  getFILLMaxSize :com.example.elementarylearningcompanion.LessonListActivity  getFillMaxSize :com.example.elementarylearningcompanion.LessonListActivity  getGETValue :com.example.elementarylearningcompanion.LessonListActivity  getGetValue :com.example.elementarylearningcompanion.LessonListActivity  	getINTENT :com.example.elementarylearningcompanion.LessonListActivity  	getIntent :com.example.elementarylearningcompanion.LessonListActivity  getPROVIDEDelegate :com.example.elementarylearningcompanion.LessonListActivity  getProvideDelegate :com.example.elementarylearningcompanion.LessonListActivity  
getSETContent :com.example.elementarylearningcompanion.LessonListActivity  
getSetContent :com.example.elementarylearningcompanion.LessonListActivity  
getVIEWModels :com.example.elementarylearningcompanion.LessonListActivity  getValue :com.example.elementarylearningcompanion.LessonListActivity  
getViewModels :com.example.elementarylearningcompanion.LessonListActivity  intent :com.example.elementarylearningcompanion.LessonListActivity  provideDelegate :com.example.elementarylearningcompanion.LessonListActivity  
setContent :com.example.elementarylearningcompanion.LessonListActivity  	setIntent :com.example.elementarylearningcompanion.LessonListActivity  	viewModel :com.example.elementarylearningcompanion.LessonListActivity  
viewModels :com.example.elementarylearningcompanion.LessonListActivity  
AuthScreen 4com.example.elementarylearningcompanion.MainActivity  
AuthViewModel 4com.example.elementarylearningcompanion.MainActivity  Bundle 4com.example.elementarylearningcompanion.MainActivity   ElementaryLearningCompanionTheme 4com.example.elementarylearningcompanion.MainActivity  
MaterialTheme 4com.example.elementarylearningcompanion.MainActivity  Modifier 4com.example.elementarylearningcompanion.MainActivity  Surface 4com.example.elementarylearningcompanion.MainActivity  fillMaxSize 4com.example.elementarylearningcompanion.MainActivity  getFILLMaxSize 4com.example.elementarylearningcompanion.MainActivity  getFillMaxSize 4com.example.elementarylearningcompanion.MainActivity  getGETValue 4com.example.elementarylearningcompanion.MainActivity  getGetValue 4com.example.elementarylearningcompanion.MainActivity  getPROVIDEDelegate 4com.example.elementarylearningcompanion.MainActivity  getProvideDelegate 4com.example.elementarylearningcompanion.MainActivity  
getSETContent 4com.example.elementarylearningcompanion.MainActivity  
getSetContent 4com.example.elementarylearningcompanion.MainActivity  
getVIEWModels 4com.example.elementarylearningcompanion.MainActivity  getValue 4com.example.elementarylearningcompanion.MainActivity  
getViewModels 4com.example.elementarylearningcompanion.MainActivity  provideDelegate 4com.example.elementarylearningcompanion.MainActivity  
setContent 4com.example.elementarylearningcompanion.MainActivity  	viewModel 4com.example.elementarylearningcompanion.MainActivity  
viewModels 4com.example.elementarylearningcompanion.MainActivity  Bundle 4com.example.elementarylearningcompanion.MathActivity   ElementaryLearningCompanionTheme 4com.example.elementarylearningcompanion.MathActivity  
MaterialTheme 4com.example.elementarylearningcompanion.MathActivity  
MathScreen 4com.example.elementarylearningcompanion.MathActivity  
MathViewModel 4com.example.elementarylearningcompanion.MathActivity  Modifier 4com.example.elementarylearningcompanion.MathActivity  Surface 4com.example.elementarylearningcompanion.MathActivity  fillMaxSize 4com.example.elementarylearningcompanion.MathActivity  getFILLMaxSize 4com.example.elementarylearningcompanion.MathActivity  getFillMaxSize 4com.example.elementarylearningcompanion.MathActivity  getGETValue 4com.example.elementarylearningcompanion.MathActivity  getGetValue 4com.example.elementarylearningcompanion.MathActivity  getPROVIDEDelegate 4com.example.elementarylearningcompanion.MathActivity  getProvideDelegate 4com.example.elementarylearningcompanion.MathActivity  
getSETContent 4com.example.elementarylearningcompanion.MathActivity  
getSetContent 4com.example.elementarylearningcompanion.MathActivity  
getVIEWModels 4com.example.elementarylearningcompanion.MathActivity  getValue 4com.example.elementarylearningcompanion.MathActivity  
getViewModels 4com.example.elementarylearningcompanion.MathActivity  provideDelegate 4com.example.elementarylearningcompanion.MathActivity  
setContent 4com.example.elementarylearningcompanion.MathActivity  	viewModel 4com.example.elementarylearningcompanion.MathActivity  
viewModels 4com.example.elementarylearningcompanion.MathActivity  Class 0com.example.elementarylearningcompanion.MenuItem  ImageVector 0com.example.elementarylearningcompanion.MenuItem  String 0com.example.elementarylearningcompanion.MenuItem  activity 0com.example.elementarylearningcompanion.MenuItem  description 0com.example.elementarylearningcompanion.MenuItem  icon 0com.example.elementarylearningcompanion.MenuItem  title 0com.example.elementarylearningcompanion.MenuItem  Bundle :com.example.elementarylearningcompanion.OnboardingActivity  ContentViewModel :com.example.elementarylearningcompanion.OnboardingActivity   ElementaryLearningCompanionTheme :com.example.elementarylearningcompanion.OnboardingActivity  
MaterialTheme :com.example.elementarylearningcompanion.OnboardingActivity  Modifier :com.example.elementarylearningcompanion.OnboardingActivity  OnboardingScreen :com.example.elementarylearningcompanion.OnboardingActivity  Surface :com.example.elementarylearningcompanion.OnboardingActivity  fillMaxSize :com.example.elementarylearningcompanion.OnboardingActivity  getFILLMaxSize :com.example.elementarylearningcompanion.OnboardingActivity  getFillMaxSize :com.example.elementarylearningcompanion.OnboardingActivity  getGETValue :com.example.elementarylearningcompanion.OnboardingActivity  getGetValue :com.example.elementarylearningcompanion.OnboardingActivity  getPROVIDEDelegate :com.example.elementarylearningcompanion.OnboardingActivity  getProvideDelegate :com.example.elementarylearningcompanion.OnboardingActivity  
getSETContent :com.example.elementarylearningcompanion.OnboardingActivity  
getSetContent :com.example.elementarylearningcompanion.OnboardingActivity  
getVIEWModels :com.example.elementarylearningcompanion.OnboardingActivity  getValue :com.example.elementarylearningcompanion.OnboardingActivity  
getViewModels :com.example.elementarylearningcompanion.OnboardingActivity  provideDelegate :com.example.elementarylearningcompanion.OnboardingActivity  
setContent :com.example.elementarylearningcompanion.OnboardingActivity  	viewModel :com.example.elementarylearningcompanion.OnboardingActivity  
viewModels :com.example.elementarylearningcompanion.OnboardingActivity  Bundle 7com.example.elementarylearningcompanion.ProfileActivity   ElementaryLearningCompanionTheme 7com.example.elementarylearningcompanion.ProfileActivity  
MaterialTheme 7com.example.elementarylearningcompanion.ProfileActivity  Modifier 7com.example.elementarylearningcompanion.ProfileActivity  
ProfileScreen 7com.example.elementarylearningcompanion.ProfileActivity  ProfileViewModel 7com.example.elementarylearningcompanion.ProfileActivity  Surface 7com.example.elementarylearningcompanion.ProfileActivity  fillMaxSize 7com.example.elementarylearningcompanion.ProfileActivity  getFILLMaxSize 7com.example.elementarylearningcompanion.ProfileActivity  getFillMaxSize 7com.example.elementarylearningcompanion.ProfileActivity  getGETValue 7com.example.elementarylearningcompanion.ProfileActivity  getGetValue 7com.example.elementarylearningcompanion.ProfileActivity  getPROVIDEDelegate 7com.example.elementarylearningcompanion.ProfileActivity  getProvideDelegate 7com.example.elementarylearningcompanion.ProfileActivity  
getSETContent 7com.example.elementarylearningcompanion.ProfileActivity  
getSetContent 7com.example.elementarylearningcompanion.ProfileActivity  
getVIEWModels 7com.example.elementarylearningcompanion.ProfileActivity  getValue 7com.example.elementarylearningcompanion.ProfileActivity  
getViewModels 7com.example.elementarylearningcompanion.ProfileActivity  provideDelegate 7com.example.elementarylearningcompanion.ProfileActivity  
setContent 7com.example.elementarylearningcompanion.ProfileActivity  	viewModel 7com.example.elementarylearningcompanion.ProfileActivity  
viewModels 7com.example.elementarylearningcompanion.ProfileActivity  Bundle =com.example.elementarylearningcompanion.WrongQuestionActivity   ElementaryLearningCompanionTheme =com.example.elementarylearningcompanion.WrongQuestionActivity  
MaterialTheme =com.example.elementarylearningcompanion.WrongQuestionActivity  Modifier =com.example.elementarylearningcompanion.WrongQuestionActivity  Surface =com.example.elementarylearningcompanion.WrongQuestionActivity  WrongQuestionScreen =com.example.elementarylearningcompanion.WrongQuestionActivity  WrongQuestionViewModel =com.example.elementarylearningcompanion.WrongQuestionActivity  fillMaxSize =com.example.elementarylearningcompanion.WrongQuestionActivity  getFILLMaxSize =com.example.elementarylearningcompanion.WrongQuestionActivity  getFillMaxSize =com.example.elementarylearningcompanion.WrongQuestionActivity  getGETValue =com.example.elementarylearningcompanion.WrongQuestionActivity  getGetValue =com.example.elementarylearningcompanion.WrongQuestionActivity  getPROVIDEDelegate =com.example.elementarylearningcompanion.WrongQuestionActivity  getProvideDelegate =com.example.elementarylearningcompanion.WrongQuestionActivity  
getSETContent =com.example.elementarylearningcompanion.WrongQuestionActivity  
getSetContent =com.example.elementarylearningcompanion.WrongQuestionActivity  
getVIEWModels =com.example.elementarylearningcompanion.WrongQuestionActivity  getValue =com.example.elementarylearningcompanion.WrongQuestionActivity  
getViewModels =com.example.elementarylearningcompanion.WrongQuestionActivity  provideDelegate =com.example.elementarylearningcompanion.WrongQuestionActivity  
setContent =com.example.elementarylearningcompanion.WrongQuestionActivity  	viewModel =com.example.elementarylearningcompanion.WrongQuestionActivity  
viewModels =com.example.elementarylearningcompanion.WrongQuestionActivity  ApiResponse +com.example.elementarylearningcompanion.dto  	Character +com.example.elementarylearningcompanion.dto  EnglishWord +com.example.elementarylearningcompanion.dto  Lesson +com.example.elementarylearningcompanion.dto  MathExercise +com.example.elementarylearningcompanion.dto  Sentence +com.example.elementarylearningcompanion.dto  Student +com.example.elementarylearningcompanion.dto  TextbookVersion +com.example.elementarylearningcompanion.dto  getLET 7com.example.elementarylearningcompanion.dto.ApiResponse  getLet 7com.example.elementarylearningcompanion.dto.ApiResponse  
getMessage 7com.example.elementarylearningcompanion.dto.ApiResponse  	isSuccess 7com.example.elementarylearningcompanion.dto.ApiResponse  let 7com.example.elementarylearningcompanion.dto.ApiResponse  getCharacterText 5com.example.elementarylearningcompanion.dto.Character  	getPinyin 5com.example.elementarylearningcompanion.dto.Character  getExampleSentence 7com.example.elementarylearningcompanion.dto.EnglishWord  getExampleTranslation 7com.example.elementarylearningcompanion.dto.EnglishWord  getId 7com.example.elementarylearningcompanion.dto.EnglishWord  getMeaningChinese 7com.example.elementarylearningcompanion.dto.EnglishWord  getPhonetic 7com.example.elementarylearningcompanion.dto.EnglishWord  getWord 7com.example.elementarylearningcompanion.dto.EnglishWord  equals 2com.example.elementarylearningcompanion.dto.Lesson  getId 2com.example.elementarylearningcompanion.dto.Lesson  getLET 2com.example.elementarylearningcompanion.dto.Lesson  getLessonNumber 2com.example.elementarylearningcompanion.dto.Lesson  getLet 2com.example.elementarylearningcompanion.dto.Lesson  getSentences 2com.example.elementarylearningcompanion.dto.Lesson  getTitle 2com.example.elementarylearningcompanion.dto.Lesson  let 2com.example.elementarylearningcompanion.dto.Lesson  getCorrectAnswer 8com.example.elementarylearningcompanion.dto.MathExercise  getExplanation 8com.example.elementarylearningcompanion.dto.MathExercise  getId 8com.example.elementarylearningcompanion.dto.MathExercise  getQuestion 8com.example.elementarylearningcompanion.dto.MathExercise  getId 4com.example.elementarylearningcompanion.dto.Sentence  getTextContent 4com.example.elementarylearningcompanion.dto.Sentence  equals 3com.example.elementarylearningcompanion.dto.Student  getGRADE 3com.example.elementarylearningcompanion.dto.Student  getGrade 3com.example.elementarylearningcompanion.dto.Student  getLET 3com.example.elementarylearningcompanion.dto.Student  getLet 3com.example.elementarylearningcompanion.dto.Student  getNAME 3com.example.elementarylearningcompanion.dto.Student  getName 3com.example.elementarylearningcompanion.dto.Student  getTEXTBOOKVersionId 3com.example.elementarylearningcompanion.dto.Student  getTextbookVersionId 3com.example.elementarylearningcompanion.dto.Student  grade 3com.example.elementarylearningcompanion.dto.Student  let 3com.example.elementarylearningcompanion.dto.Student  name 3com.example.elementarylearningcompanion.dto.Student  setGrade 3com.example.elementarylearningcompanion.dto.Student  setName 3com.example.elementarylearningcompanion.dto.Student  setTextbookVersionId 3com.example.elementarylearningcompanion.dto.Student  textbookVersionId 3com.example.elementarylearningcompanion.dto.Student  getId ;com.example.elementarylearningcompanion.dto.TextbookVersion  getName ;com.example.elementarylearningcompanion.dto.TextbookVersion  
getSubject ;com.example.elementarylearningcompanion.dto.TextbookVersion  Boolean 0com.example.elementarylearningcompanion.ui.theme  Build 0com.example.elementarylearningcompanion.ui.theme  DarkColorScheme 0com.example.elementarylearningcompanion.ui.theme   ElementaryLearningCompanionTheme 0com.example.elementarylearningcompanion.ui.theme  LightColorScheme 0com.example.elementarylearningcompanion.ui.theme  Pink40 0com.example.elementarylearningcompanion.ui.theme  Pink80 0com.example.elementarylearningcompanion.ui.theme  Purple40 0com.example.elementarylearningcompanion.ui.theme  Purple80 0com.example.elementarylearningcompanion.ui.theme  PurpleGrey40 0com.example.elementarylearningcompanion.ui.theme  PurpleGrey80 0com.example.elementarylearningcompanion.ui.theme  
Typography 0com.example.elementarylearningcompanion.ui.theme  Unit 0com.example.elementarylearningcompanion.ui.theme  WindowCompat 0com.example.elementarylearningcompanion.ui.theme  
AuthViewModel 1com.example.elementarylearningcompanion.viewmodel  ContentViewModel 1com.example.elementarylearningcompanion.viewmodel  EnglishViewModel 1com.example.elementarylearningcompanion.viewmodel  
MathViewModel 1com.example.elementarylearningcompanion.viewmodel  ProfileViewModel 1com.example.elementarylearningcompanion.viewmodel  WrongQuestionViewModel 1com.example.elementarylearningcompanion.viewmodel  
authResult ?com.example.elementarylearningcompanion.viewmodel.AuthViewModel  	isLoading ?com.example.elementarylearningcompanion.viewmodel.AuthViewModel  login ?com.example.elementarylearningcompanion.viewmodel.AuthViewModel  register ?com.example.elementarylearningcompanion.viewmodel.AuthViewModel  
characters Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  error Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  fetchCharactersByLesson Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  fetchLessonDetails Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  fetchLessons Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  fetchTextbooks Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  	isLoading Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  
lessonDetails Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  lessons Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  	textbooks Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  clearRecordResult Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  errorMessage Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  getERRORMessage Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  getErrorMessage Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  getISLoading Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  getIsLoading Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  getLEARNINGModes Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  getLearningModes Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  getRECORDResult Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  getRecordResult Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  getWORDS Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  getWords Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  	isLoading Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  
learningModes Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  loadLearningModes Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  loadRandomWords Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  loadSpellingTest Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  recordLearning Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  recordResult Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  setErrorMessage Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  setIsLoading Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  setLearningModes Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  setRecordResult Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  setWords Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  words Bcom.example.elementarylearningcompanion.viewmodel.EnglishViewModel  clearSubmitResult ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  errorMessage ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  	exercises ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  generateExercises ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  getERRORMessage ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  getEXERCISES ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  getErrorMessage ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  getExercises ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  getISLoading ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  getIsLoading ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  getPRACTICETypes ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  getPracticeTypes ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  getSUBMITResult ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  getSubmitResult ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  	isLoading ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  loadPracticeTypes ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  
practiceTypes ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  setErrorMessage ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  setExercises ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  setIsLoading ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  setPracticeTypes ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  setSubmitResult ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  submitAnswer ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  submitResult ?com.example.elementarylearningcompanion.viewmodel.MathViewModel  availableGrades Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  clearUpdateResult Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  errorMessage Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  getAVAILABLEGrades Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  getAvailableGrades Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  getERRORMessage Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  getErrorMessage Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  getISLoading Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  getIsLoading Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  getSETTINGS Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  
getSTATISTICS Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  getSTUDENTProfile Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  getSettings Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  
getStatistics Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  getStudentProfile Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  getUPDATEResult Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  getUpdateResult Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  	isLoading Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  loadAvailableGrades Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  loadSettings Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  loadStatistics Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  loadStudentProfile Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  setAvailableGrades Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  setErrorMessage Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  setIsLoading Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  setSettings Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  
setStatistics Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  setStudentProfile Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  setUpdateResult Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  settings Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  
statistics Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  studentProfile Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  updateResult Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  updateSettings Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  updateStudentProfile Bcom.example.elementarylearningcompanion.viewmodel.ProfileViewModel  clearOperationResult Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  deleteWrongQuestion Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  detailedWrongQuestions Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  errorMessage Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  getDETAILEDWrongQuestions Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  getDetailedWrongQuestions Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  getERRORMessage Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  getErrorMessage Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  getISLoading Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  getIsLoading Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  getOPERATIONResult Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  getOperationResult Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  
getSTATISTICS Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  
getStatistics Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  	isLoading Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  loadDetailedWrongQuestions Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  loadStatistics Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  loadWrongQuestionsByType Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  markAsMastered Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  operationResult Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  setDetailedWrongQuestions Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  setErrorMessage Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  setIsLoading Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  setOperationResult Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  
setStatistics Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  
statistics Hcom.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel  	Alignment 	java.lang  Arrangement 	java.lang  
AuthScreen 	java.lang  Box 	java.lang  Build 	java.lang  Button 	java.lang  ButtonDefaults 	java.lang  Card 	java.lang  CardDefaults 	java.lang  
CharacterItem 	java.lang  CharactersList 	java.lang  CircularProgressIndicator 	java.lang  Class 	java.lang  Column 	java.lang  Divider 	java.lang  EditProfileDialog 	java.lang   ElementaryLearningCompanionTheme 	java.lang  EnglishActivity 	java.lang  
EnglishScreen 	java.lang  ExerciseScreen 	java.lang  ExperimentalMaterial3Api 	java.lang  
FilterChip 	java.lang  
FilterTabs 	java.lang  
FlashcardMode 	java.lang  
FontWeight 	java.lang  HomeActivity 	java.lang  
HomeScreen 	java.lang  Icon 	java.lang  
IconButton 	java.lang  Icons 	java.lang  Intent 	java.lang  LaunchedEffect 	java.lang  
LazyColumn 	java.lang  LearningModeSelection 	java.lang  LessonActivity 	java.lang  LessonListActivity 	java.lang  LessonListItem 	java.lang  LessonListScreen 	java.lang  LessonScreen 	java.lang  
ListeningMode 	java.lang  
MaterialTheme 	java.lang  MathActivity 	java.lang  
MathScreen 	java.lang  MenuItem 	java.lang  MenuItemCard 	java.lang  Modifier 	java.lang  OnboardingActivity 	java.lang  OnboardingScreen 	java.lang  OutlinedTextField 	java.lang  PasswordVisualTransformation 	java.lang  PracticeTypeSelection 	java.lang  ProfileActivity 	java.lang  ProfileCard 	java.lang  
ProfileScreen 	java.lang  QuickActionsCard 	java.lang  QuizMode 	java.lang  Row 	java.lang  SentenceItem 	java.lang  
SentencesList 	java.lang  SettingsCard 	java.lang  SettingsDialog 	java.lang  Spacer 	java.lang  SpellingMode 	java.lang  
StatisticItem 	java.lang  StatisticsCard 	java.lang  Surface 	java.lang  System 	java.lang  Text 	java.lang  
TextButton 	java.lang  TextbookItem 	java.lang  Toast 	java.lang  WindowCompat 	java.lang  WordLearningScreen 	java.lang  WrongQuestionActivity 	java.lang  WrongQuestionItem 	java.lang  WrongQuestionScreen 	java.lang  WrongQuestionStatisticItem 	java.lang  WrongQuestionStatisticsCard 	java.lang  com 	java.lang  contains 	java.lang  	emptyList 	java.lang  emptyMap 	java.lang  equals 	java.lang  fillMaxSize 	java.lang  fillMaxWidth 	java.lang  forEach 	java.lang  getValue 	java.lang  height 	java.lang  
isNotBlank 	java.lang  
isNotEmpty 	java.lang  java 	java.lang  last 	java.lang  let 	java.lang  listOf 	java.lang  padding 	java.lang  provideDelegate 	java.lang  size 	java.lang  to 	java.lang  width 	java.lang  currentTimeMillis java.lang.System  	Alignment kotlin  Any kotlin  Arrangement kotlin  
AuthScreen kotlin  Boolean kotlin  Box kotlin  Build kotlin  Button kotlin  ButtonDefaults kotlin  Card kotlin  CardDefaults kotlin  
CharacterItem kotlin  CharactersList kotlin  CircularProgressIndicator kotlin  Class kotlin  Column kotlin  Divider kotlin  Double kotlin  EditProfileDialog kotlin   ElementaryLearningCompanionTheme kotlin  EnglishActivity kotlin  
EnglishScreen kotlin  ExerciseScreen kotlin  ExperimentalMaterial3Api kotlin  
FilterChip kotlin  
FilterTabs kotlin  
FlashcardMode kotlin  Float kotlin  
FontWeight kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function3 kotlin  HomeActivity kotlin  
HomeScreen kotlin  Icon kotlin  
IconButton kotlin  Icons kotlin  Int kotlin  Intent kotlin  LaunchedEffect kotlin  Lazy kotlin  
LazyColumn kotlin  LearningModeSelection kotlin  LessonActivity kotlin  LessonListActivity kotlin  LessonListItem kotlin  LessonListScreen kotlin  LessonScreen kotlin  
ListeningMode kotlin  Long kotlin  
MaterialTheme kotlin  MathActivity kotlin  
MathScreen kotlin  MenuItem kotlin  MenuItemCard kotlin  Modifier kotlin  Nothing kotlin  OnboardingActivity kotlin  OnboardingScreen kotlin  OptIn kotlin  OutlinedTextField kotlin  Pair kotlin  PasswordVisualTransformation kotlin  PracticeTypeSelection kotlin  ProfileActivity kotlin  ProfileCard kotlin  
ProfileScreen kotlin  QuickActionsCard kotlin  QuizMode kotlin  Row kotlin  SentenceItem kotlin  
SentencesList kotlin  SettingsCard kotlin  SettingsDialog kotlin  Spacer kotlin  SpellingMode kotlin  
StatisticItem kotlin  StatisticsCard kotlin  String kotlin  Surface kotlin  System kotlin  Text kotlin  
TextButton kotlin  TextbookItem kotlin  Toast kotlin  Unit kotlin  WindowCompat kotlin  WordLearningScreen kotlin  WrongQuestionActivity kotlin  WrongQuestionItem kotlin  WrongQuestionScreen kotlin  WrongQuestionStatisticItem kotlin  WrongQuestionStatisticsCard kotlin  com kotlin  contains kotlin  	emptyList kotlin  emptyMap kotlin  equals kotlin  fillMaxSize kotlin  fillMaxWidth kotlin  forEach kotlin  getValue kotlin  height kotlin  
isNotBlank kotlin  
isNotEmpty kotlin  java kotlin  last kotlin  let kotlin  listOf kotlin  padding kotlin  provideDelegate kotlin  size kotlin  to kotlin  width kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getDP 
kotlin.Int  getDp 
kotlin.Int  getLET 
kotlin.Int  getLet 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  getLET kotlin.Long  getLet kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  equals kotlin.Pair  getCONTAINS 
kotlin.String  getContains 
kotlin.String  	getEQUALS 
kotlin.String  	getEquals 
kotlin.String  
getISNotBlank 
kotlin.String  
getIsNotBlank 
kotlin.String  getLET 
kotlin.String  getLet 
kotlin.String  getTO 
kotlin.String  getTo 
kotlin.String  
isNotBlank 
kotlin.String  	Alignment kotlin.annotation  Arrangement kotlin.annotation  
AuthScreen kotlin.annotation  Box kotlin.annotation  Build kotlin.annotation  Button kotlin.annotation  ButtonDefaults kotlin.annotation  Card kotlin.annotation  CardDefaults kotlin.annotation  
CharacterItem kotlin.annotation  CharactersList kotlin.annotation  CircularProgressIndicator kotlin.annotation  Class kotlin.annotation  Column kotlin.annotation  Divider kotlin.annotation  EditProfileDialog kotlin.annotation   ElementaryLearningCompanionTheme kotlin.annotation  EnglishActivity kotlin.annotation  
EnglishScreen kotlin.annotation  ExerciseScreen kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  
FilterChip kotlin.annotation  
FilterTabs kotlin.annotation  
FlashcardMode kotlin.annotation  
FontWeight kotlin.annotation  HomeActivity kotlin.annotation  
HomeScreen kotlin.annotation  Icon kotlin.annotation  
IconButton kotlin.annotation  Icons kotlin.annotation  Intent kotlin.annotation  LaunchedEffect kotlin.annotation  
LazyColumn kotlin.annotation  LearningModeSelection kotlin.annotation  LessonActivity kotlin.annotation  LessonListActivity kotlin.annotation  LessonListItem kotlin.annotation  LessonListScreen kotlin.annotation  LessonScreen kotlin.annotation  
ListeningMode kotlin.annotation  
MaterialTheme kotlin.annotation  MathActivity kotlin.annotation  
MathScreen kotlin.annotation  MenuItem kotlin.annotation  MenuItemCard kotlin.annotation  Modifier kotlin.annotation  OnboardingActivity kotlin.annotation  OnboardingScreen kotlin.annotation  OutlinedTextField kotlin.annotation  PasswordVisualTransformation kotlin.annotation  PracticeTypeSelection kotlin.annotation  ProfileActivity kotlin.annotation  ProfileCard kotlin.annotation  
ProfileScreen kotlin.annotation  QuickActionsCard kotlin.annotation  QuizMode kotlin.annotation  Row kotlin.annotation  SentenceItem kotlin.annotation  
SentencesList kotlin.annotation  SettingsCard kotlin.annotation  SettingsDialog kotlin.annotation  Spacer kotlin.annotation  SpellingMode kotlin.annotation  
StatisticItem kotlin.annotation  StatisticsCard kotlin.annotation  Surface kotlin.annotation  System kotlin.annotation  Text kotlin.annotation  
TextButton kotlin.annotation  TextbookItem kotlin.annotation  Toast kotlin.annotation  WindowCompat kotlin.annotation  WordLearningScreen kotlin.annotation  WrongQuestionActivity kotlin.annotation  WrongQuestionItem kotlin.annotation  WrongQuestionScreen kotlin.annotation  WrongQuestionStatisticItem kotlin.annotation  WrongQuestionStatisticsCard kotlin.annotation  com kotlin.annotation  contains kotlin.annotation  	emptyList kotlin.annotation  emptyMap kotlin.annotation  equals kotlin.annotation  fillMaxSize kotlin.annotation  fillMaxWidth kotlin.annotation  forEach kotlin.annotation  getValue kotlin.annotation  height kotlin.annotation  
isNotBlank kotlin.annotation  
isNotEmpty kotlin.annotation  java kotlin.annotation  last kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  padding kotlin.annotation  provideDelegate kotlin.annotation  size kotlin.annotation  to kotlin.annotation  width kotlin.annotation  	Alignment kotlin.collections  Arrangement kotlin.collections  
AuthScreen kotlin.collections  Box kotlin.collections  Build kotlin.collections  Button kotlin.collections  ButtonDefaults kotlin.collections  Card kotlin.collections  CardDefaults kotlin.collections  
CharacterItem kotlin.collections  CharactersList kotlin.collections  CircularProgressIndicator kotlin.collections  Class kotlin.collections  Column kotlin.collections  Divider kotlin.collections  EditProfileDialog kotlin.collections   ElementaryLearningCompanionTheme kotlin.collections  EnglishActivity kotlin.collections  
EnglishScreen kotlin.collections  ExerciseScreen kotlin.collections  ExperimentalMaterial3Api kotlin.collections  
FilterChip kotlin.collections  
FilterTabs kotlin.collections  
FlashcardMode kotlin.collections  
FontWeight kotlin.collections  HomeActivity kotlin.collections  
HomeScreen kotlin.collections  Icon kotlin.collections  
IconButton kotlin.collections  Icons kotlin.collections  Intent kotlin.collections  LaunchedEffect kotlin.collections  
LazyColumn kotlin.collections  LearningModeSelection kotlin.collections  LessonActivity kotlin.collections  LessonListActivity kotlin.collections  LessonListItem kotlin.collections  LessonListScreen kotlin.collections  LessonScreen kotlin.collections  List kotlin.collections  
ListeningMode kotlin.collections  Map kotlin.collections  
MaterialTheme kotlin.collections  MathActivity kotlin.collections  
MathScreen kotlin.collections  MenuItem kotlin.collections  MenuItemCard kotlin.collections  Modifier kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  OnboardingActivity kotlin.collections  OnboardingScreen kotlin.collections  OutlinedTextField kotlin.collections  PasswordVisualTransformation kotlin.collections  PracticeTypeSelection kotlin.collections  ProfileActivity kotlin.collections  ProfileCard kotlin.collections  
ProfileScreen kotlin.collections  QuickActionsCard kotlin.collections  QuizMode kotlin.collections  Row kotlin.collections  SentenceItem kotlin.collections  
SentencesList kotlin.collections  SettingsCard kotlin.collections  SettingsDialog kotlin.collections  Spacer kotlin.collections  SpellingMode kotlin.collections  
StatisticItem kotlin.collections  StatisticsCard kotlin.collections  Surface kotlin.collections  System kotlin.collections  Text kotlin.collections  
TextButton kotlin.collections  TextbookItem kotlin.collections  Toast kotlin.collections  WindowCompat kotlin.collections  WordLearningScreen kotlin.collections  WrongQuestionActivity kotlin.collections  WrongQuestionItem kotlin.collections  WrongQuestionScreen kotlin.collections  WrongQuestionStatisticItem kotlin.collections  WrongQuestionStatisticsCard kotlin.collections  com kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  equals kotlin.collections  fillMaxSize kotlin.collections  fillMaxWidth kotlin.collections  forEach kotlin.collections  getValue kotlin.collections  height kotlin.collections  
isNotBlank kotlin.collections  
isNotEmpty kotlin.collections  java kotlin.collections  last kotlin.collections  let kotlin.collections  listOf kotlin.collections  padding kotlin.collections  provideDelegate kotlin.collections  size kotlin.collections  to kotlin.collections  width kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  
getISNotEmpty kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  getLAST kotlin.collections.List  getLast kotlin.collections.List  
isNotEmpty kotlin.collections.List  
getISNotEmpty kotlin.collections.Map  
getIsNotEmpty kotlin.collections.Map  
isNotEmpty kotlin.collections.Map  	Alignment kotlin.comparisons  Arrangement kotlin.comparisons  
AuthScreen kotlin.comparisons  Box kotlin.comparisons  Build kotlin.comparisons  Button kotlin.comparisons  ButtonDefaults kotlin.comparisons  Card kotlin.comparisons  CardDefaults kotlin.comparisons  
CharacterItem kotlin.comparisons  CharactersList kotlin.comparisons  CircularProgressIndicator kotlin.comparisons  Class kotlin.comparisons  Column kotlin.comparisons  Divider kotlin.comparisons  EditProfileDialog kotlin.comparisons   ElementaryLearningCompanionTheme kotlin.comparisons  EnglishActivity kotlin.comparisons  
EnglishScreen kotlin.comparisons  ExerciseScreen kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  
FilterChip kotlin.comparisons  
FilterTabs kotlin.comparisons  
FlashcardMode kotlin.comparisons  
FontWeight kotlin.comparisons  HomeActivity kotlin.comparisons  
HomeScreen kotlin.comparisons  Icon kotlin.comparisons  
IconButton kotlin.comparisons  Icons kotlin.comparisons  Intent kotlin.comparisons  LaunchedEffect kotlin.comparisons  
LazyColumn kotlin.comparisons  LearningModeSelection kotlin.comparisons  LessonActivity kotlin.comparisons  LessonListActivity kotlin.comparisons  LessonListItem kotlin.comparisons  LessonListScreen kotlin.comparisons  LessonScreen kotlin.comparisons  
ListeningMode kotlin.comparisons  
MaterialTheme kotlin.comparisons  MathActivity kotlin.comparisons  
MathScreen kotlin.comparisons  MenuItem kotlin.comparisons  MenuItemCard kotlin.comparisons  Modifier kotlin.comparisons  OnboardingActivity kotlin.comparisons  OnboardingScreen kotlin.comparisons  OutlinedTextField kotlin.comparisons  PasswordVisualTransformation kotlin.comparisons  PracticeTypeSelection kotlin.comparisons  ProfileActivity kotlin.comparisons  ProfileCard kotlin.comparisons  
ProfileScreen kotlin.comparisons  QuickActionsCard kotlin.comparisons  QuizMode kotlin.comparisons  Row kotlin.comparisons  SentenceItem kotlin.comparisons  
SentencesList kotlin.comparisons  SettingsCard kotlin.comparisons  SettingsDialog kotlin.comparisons  Spacer kotlin.comparisons  SpellingMode kotlin.comparisons  
StatisticItem kotlin.comparisons  StatisticsCard kotlin.comparisons  Surface kotlin.comparisons  System kotlin.comparisons  Text kotlin.comparisons  
TextButton kotlin.comparisons  TextbookItem kotlin.comparisons  Toast kotlin.comparisons  WindowCompat kotlin.comparisons  WordLearningScreen kotlin.comparisons  WrongQuestionActivity kotlin.comparisons  WrongQuestionItem kotlin.comparisons  WrongQuestionScreen kotlin.comparisons  WrongQuestionStatisticItem kotlin.comparisons  WrongQuestionStatisticsCard kotlin.comparisons  com kotlin.comparisons  contains kotlin.comparisons  	emptyList kotlin.comparisons  emptyMap kotlin.comparisons  equals kotlin.comparisons  fillMaxSize kotlin.comparisons  fillMaxWidth kotlin.comparisons  forEach kotlin.comparisons  getValue kotlin.comparisons  height kotlin.comparisons  
isNotBlank kotlin.comparisons  
isNotEmpty kotlin.comparisons  java kotlin.comparisons  last kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  padding kotlin.comparisons  provideDelegate kotlin.comparisons  size kotlin.comparisons  to kotlin.comparisons  width kotlin.comparisons  SuspendFunction1 kotlin.coroutines  	Alignment 	kotlin.io  Arrangement 	kotlin.io  
AuthScreen 	kotlin.io  Box 	kotlin.io  Build 	kotlin.io  Button 	kotlin.io  ButtonDefaults 	kotlin.io  Card 	kotlin.io  CardDefaults 	kotlin.io  
CharacterItem 	kotlin.io  CharactersList 	kotlin.io  CircularProgressIndicator 	kotlin.io  Class 	kotlin.io  Column 	kotlin.io  Divider 	kotlin.io  EditProfileDialog 	kotlin.io   ElementaryLearningCompanionTheme 	kotlin.io  EnglishActivity 	kotlin.io  
EnglishScreen 	kotlin.io  ExerciseScreen 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  
FilterChip 	kotlin.io  
FilterTabs 	kotlin.io  
FlashcardMode 	kotlin.io  
FontWeight 	kotlin.io  HomeActivity 	kotlin.io  
HomeScreen 	kotlin.io  Icon 	kotlin.io  
IconButton 	kotlin.io  Icons 	kotlin.io  Intent 	kotlin.io  LaunchedEffect 	kotlin.io  
LazyColumn 	kotlin.io  LearningModeSelection 	kotlin.io  LessonActivity 	kotlin.io  LessonListActivity 	kotlin.io  LessonListItem 	kotlin.io  LessonListScreen 	kotlin.io  LessonScreen 	kotlin.io  
ListeningMode 	kotlin.io  
MaterialTheme 	kotlin.io  MathActivity 	kotlin.io  
MathScreen 	kotlin.io  MenuItem 	kotlin.io  MenuItemCard 	kotlin.io  Modifier 	kotlin.io  OnboardingActivity 	kotlin.io  OnboardingScreen 	kotlin.io  OutlinedTextField 	kotlin.io  PasswordVisualTransformation 	kotlin.io  PracticeTypeSelection 	kotlin.io  ProfileActivity 	kotlin.io  ProfileCard 	kotlin.io  
ProfileScreen 	kotlin.io  QuickActionsCard 	kotlin.io  QuizMode 	kotlin.io  Row 	kotlin.io  SentenceItem 	kotlin.io  
SentencesList 	kotlin.io  SettingsCard 	kotlin.io  SettingsDialog 	kotlin.io  Spacer 	kotlin.io  SpellingMode 	kotlin.io  
StatisticItem 	kotlin.io  StatisticsCard 	kotlin.io  Surface 	kotlin.io  System 	kotlin.io  Text 	kotlin.io  
TextButton 	kotlin.io  TextbookItem 	kotlin.io  Toast 	kotlin.io  WindowCompat 	kotlin.io  WordLearningScreen 	kotlin.io  WrongQuestionActivity 	kotlin.io  WrongQuestionItem 	kotlin.io  WrongQuestionScreen 	kotlin.io  WrongQuestionStatisticItem 	kotlin.io  WrongQuestionStatisticsCard 	kotlin.io  com 	kotlin.io  contains 	kotlin.io  	emptyList 	kotlin.io  emptyMap 	kotlin.io  equals 	kotlin.io  fillMaxSize 	kotlin.io  fillMaxWidth 	kotlin.io  forEach 	kotlin.io  getValue 	kotlin.io  height 	kotlin.io  
isNotBlank 	kotlin.io  
isNotEmpty 	kotlin.io  java 	kotlin.io  last 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  padding 	kotlin.io  provideDelegate 	kotlin.io  size 	kotlin.io  to 	kotlin.io  width 	kotlin.io  	Alignment 
kotlin.jvm  Arrangement 
kotlin.jvm  
AuthScreen 
kotlin.jvm  Box 
kotlin.jvm  Build 
kotlin.jvm  Button 
kotlin.jvm  ButtonDefaults 
kotlin.jvm  Card 
kotlin.jvm  CardDefaults 
kotlin.jvm  
CharacterItem 
kotlin.jvm  CharactersList 
kotlin.jvm  CircularProgressIndicator 
kotlin.jvm  Class 
kotlin.jvm  Column 
kotlin.jvm  Divider 
kotlin.jvm  EditProfileDialog 
kotlin.jvm   ElementaryLearningCompanionTheme 
kotlin.jvm  EnglishActivity 
kotlin.jvm  
EnglishScreen 
kotlin.jvm  ExerciseScreen 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  
FilterChip 
kotlin.jvm  
FilterTabs 
kotlin.jvm  
FlashcardMode 
kotlin.jvm  
FontWeight 
kotlin.jvm  HomeActivity 
kotlin.jvm  
HomeScreen 
kotlin.jvm  Icon 
kotlin.jvm  
IconButton 
kotlin.jvm  Icons 
kotlin.jvm  Intent 
kotlin.jvm  LaunchedEffect 
kotlin.jvm  
LazyColumn 
kotlin.jvm  LearningModeSelection 
kotlin.jvm  LessonActivity 
kotlin.jvm  LessonListActivity 
kotlin.jvm  LessonListItem 
kotlin.jvm  LessonListScreen 
kotlin.jvm  LessonScreen 
kotlin.jvm  
ListeningMode 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  MathActivity 
kotlin.jvm  
MathScreen 
kotlin.jvm  MenuItem 
kotlin.jvm  MenuItemCard 
kotlin.jvm  Modifier 
kotlin.jvm  OnboardingActivity 
kotlin.jvm  OnboardingScreen 
kotlin.jvm  OutlinedTextField 
kotlin.jvm  PasswordVisualTransformation 
kotlin.jvm  PracticeTypeSelection 
kotlin.jvm  ProfileActivity 
kotlin.jvm  ProfileCard 
kotlin.jvm  
ProfileScreen 
kotlin.jvm  QuickActionsCard 
kotlin.jvm  QuizMode 
kotlin.jvm  Row 
kotlin.jvm  SentenceItem 
kotlin.jvm  
SentencesList 
kotlin.jvm  SettingsCard 
kotlin.jvm  SettingsDialog 
kotlin.jvm  Spacer 
kotlin.jvm  SpellingMode 
kotlin.jvm  
StatisticItem 
kotlin.jvm  StatisticsCard 
kotlin.jvm  Surface 
kotlin.jvm  System 
kotlin.jvm  Text 
kotlin.jvm  
TextButton 
kotlin.jvm  TextbookItem 
kotlin.jvm  Toast 
kotlin.jvm  WindowCompat 
kotlin.jvm  WordLearningScreen 
kotlin.jvm  WrongQuestionActivity 
kotlin.jvm  WrongQuestionItem 
kotlin.jvm  WrongQuestionScreen 
kotlin.jvm  WrongQuestionStatisticItem 
kotlin.jvm  WrongQuestionStatisticsCard 
kotlin.jvm  com 
kotlin.jvm  contains 
kotlin.jvm  	emptyList 
kotlin.jvm  emptyMap 
kotlin.jvm  equals 
kotlin.jvm  fillMaxSize 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  forEach 
kotlin.jvm  getValue 
kotlin.jvm  height 
kotlin.jvm  
isNotBlank 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  java 
kotlin.jvm  last 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  padding 
kotlin.jvm  provideDelegate 
kotlin.jvm  size 
kotlin.jvm  to 
kotlin.jvm  width 
kotlin.jvm  	Alignment 
kotlin.ranges  Arrangement 
kotlin.ranges  
AuthScreen 
kotlin.ranges  Box 
kotlin.ranges  Build 
kotlin.ranges  Button 
kotlin.ranges  ButtonDefaults 
kotlin.ranges  Card 
kotlin.ranges  CardDefaults 
kotlin.ranges  
CharacterItem 
kotlin.ranges  CharactersList 
kotlin.ranges  CircularProgressIndicator 
kotlin.ranges  Class 
kotlin.ranges  Column 
kotlin.ranges  Divider 
kotlin.ranges  EditProfileDialog 
kotlin.ranges   ElementaryLearningCompanionTheme 
kotlin.ranges  EnglishActivity 
kotlin.ranges  
EnglishScreen 
kotlin.ranges  ExerciseScreen 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  
FilterChip 
kotlin.ranges  
FilterTabs 
kotlin.ranges  
FlashcardMode 
kotlin.ranges  
FontWeight 
kotlin.ranges  HomeActivity 
kotlin.ranges  
HomeScreen 
kotlin.ranges  Icon 
kotlin.ranges  
IconButton 
kotlin.ranges  Icons 
kotlin.ranges  IntRange 
kotlin.ranges  Intent 
kotlin.ranges  LaunchedEffect 
kotlin.ranges  
LazyColumn 
kotlin.ranges  LearningModeSelection 
kotlin.ranges  LessonActivity 
kotlin.ranges  LessonListActivity 
kotlin.ranges  LessonListItem 
kotlin.ranges  LessonListScreen 
kotlin.ranges  LessonScreen 
kotlin.ranges  
ListeningMode 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  MathActivity 
kotlin.ranges  
MathScreen 
kotlin.ranges  MenuItem 
kotlin.ranges  MenuItemCard 
kotlin.ranges  Modifier 
kotlin.ranges  OnboardingActivity 
kotlin.ranges  OnboardingScreen 
kotlin.ranges  OutlinedTextField 
kotlin.ranges  PasswordVisualTransformation 
kotlin.ranges  PracticeTypeSelection 
kotlin.ranges  ProfileActivity 
kotlin.ranges  ProfileCard 
kotlin.ranges  
ProfileScreen 
kotlin.ranges  QuickActionsCard 
kotlin.ranges  QuizMode 
kotlin.ranges  Row 
kotlin.ranges  SentenceItem 
kotlin.ranges  
SentencesList 
kotlin.ranges  SettingsCard 
kotlin.ranges  SettingsDialog 
kotlin.ranges  Spacer 
kotlin.ranges  SpellingMode 
kotlin.ranges  
StatisticItem 
kotlin.ranges  StatisticsCard 
kotlin.ranges  Surface 
kotlin.ranges  System 
kotlin.ranges  Text 
kotlin.ranges  
TextButton 
kotlin.ranges  TextbookItem 
kotlin.ranges  Toast 
kotlin.ranges  WindowCompat 
kotlin.ranges  WordLearningScreen 
kotlin.ranges  WrongQuestionActivity 
kotlin.ranges  WrongQuestionItem 
kotlin.ranges  WrongQuestionScreen 
kotlin.ranges  WrongQuestionStatisticItem 
kotlin.ranges  WrongQuestionStatisticsCard 
kotlin.ranges  com 
kotlin.ranges  contains 
kotlin.ranges  	emptyList 
kotlin.ranges  emptyMap 
kotlin.ranges  equals 
kotlin.ranges  fillMaxSize 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  forEach 
kotlin.ranges  getValue 
kotlin.ranges  height 
kotlin.ranges  
isNotBlank 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  java 
kotlin.ranges  last 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  padding 
kotlin.ranges  provideDelegate 
kotlin.ranges  size 
kotlin.ranges  to 
kotlin.ranges  width 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  	Alignment kotlin.sequences  Arrangement kotlin.sequences  
AuthScreen kotlin.sequences  Box kotlin.sequences  Build kotlin.sequences  Button kotlin.sequences  ButtonDefaults kotlin.sequences  Card kotlin.sequences  CardDefaults kotlin.sequences  
CharacterItem kotlin.sequences  CharactersList kotlin.sequences  CircularProgressIndicator kotlin.sequences  Class kotlin.sequences  Column kotlin.sequences  Divider kotlin.sequences  EditProfileDialog kotlin.sequences   ElementaryLearningCompanionTheme kotlin.sequences  EnglishActivity kotlin.sequences  
EnglishScreen kotlin.sequences  ExerciseScreen kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  
FilterChip kotlin.sequences  
FilterTabs kotlin.sequences  
FlashcardMode kotlin.sequences  
FontWeight kotlin.sequences  HomeActivity kotlin.sequences  
HomeScreen kotlin.sequences  Icon kotlin.sequences  
IconButton kotlin.sequences  Icons kotlin.sequences  Intent kotlin.sequences  LaunchedEffect kotlin.sequences  
LazyColumn kotlin.sequences  LearningModeSelection kotlin.sequences  LessonActivity kotlin.sequences  LessonListActivity kotlin.sequences  LessonListItem kotlin.sequences  LessonListScreen kotlin.sequences  LessonScreen kotlin.sequences  
ListeningMode kotlin.sequences  
MaterialTheme kotlin.sequences  MathActivity kotlin.sequences  
MathScreen kotlin.sequences  MenuItem kotlin.sequences  MenuItemCard kotlin.sequences  Modifier kotlin.sequences  OnboardingActivity kotlin.sequences  OnboardingScreen kotlin.sequences  OutlinedTextField kotlin.sequences  PasswordVisualTransformation kotlin.sequences  PracticeTypeSelection kotlin.sequences  ProfileActivity kotlin.sequences  ProfileCard kotlin.sequences  
ProfileScreen kotlin.sequences  QuickActionsCard kotlin.sequences  QuizMode kotlin.sequences  Row kotlin.sequences  SentenceItem kotlin.sequences  
SentencesList kotlin.sequences  SettingsCard kotlin.sequences  SettingsDialog kotlin.sequences  Spacer kotlin.sequences  SpellingMode kotlin.sequences  
StatisticItem kotlin.sequences  StatisticsCard kotlin.sequences  Surface kotlin.sequences  System kotlin.sequences  Text kotlin.sequences  
TextButton kotlin.sequences  TextbookItem kotlin.sequences  Toast kotlin.sequences  WindowCompat kotlin.sequences  WordLearningScreen kotlin.sequences  WrongQuestionActivity kotlin.sequences  WrongQuestionItem kotlin.sequences  WrongQuestionScreen kotlin.sequences  WrongQuestionStatisticItem kotlin.sequences  WrongQuestionStatisticsCard kotlin.sequences  com kotlin.sequences  contains kotlin.sequences  	emptyList kotlin.sequences  emptyMap kotlin.sequences  equals kotlin.sequences  fillMaxSize kotlin.sequences  fillMaxWidth kotlin.sequences  forEach kotlin.sequences  getValue kotlin.sequences  height kotlin.sequences  
isNotBlank kotlin.sequences  
isNotEmpty kotlin.sequences  java kotlin.sequences  last kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  padding kotlin.sequences  provideDelegate kotlin.sequences  size kotlin.sequences  to kotlin.sequences  width kotlin.sequences  	Alignment kotlin.text  Arrangement kotlin.text  
AuthScreen kotlin.text  Box kotlin.text  Build kotlin.text  Button kotlin.text  ButtonDefaults kotlin.text  Card kotlin.text  CardDefaults kotlin.text  
CharacterItem kotlin.text  CharactersList kotlin.text  CircularProgressIndicator kotlin.text  Class kotlin.text  Column kotlin.text  Divider kotlin.text  EditProfileDialog kotlin.text   ElementaryLearningCompanionTheme kotlin.text  EnglishActivity kotlin.text  
EnglishScreen kotlin.text  ExerciseScreen kotlin.text  ExperimentalMaterial3Api kotlin.text  
FilterChip kotlin.text  
FilterTabs kotlin.text  
FlashcardMode kotlin.text  
FontWeight kotlin.text  HomeActivity kotlin.text  
HomeScreen kotlin.text  Icon kotlin.text  
IconButton kotlin.text  Icons kotlin.text  Intent kotlin.text  LaunchedEffect kotlin.text  
LazyColumn kotlin.text  LearningModeSelection kotlin.text  LessonActivity kotlin.text  LessonListActivity kotlin.text  LessonListItem kotlin.text  LessonListScreen kotlin.text  LessonScreen kotlin.text  
ListeningMode kotlin.text  
MaterialTheme kotlin.text  MathActivity kotlin.text  
MathScreen kotlin.text  MenuItem kotlin.text  MenuItemCard kotlin.text  Modifier kotlin.text  OnboardingActivity kotlin.text  OnboardingScreen kotlin.text  OutlinedTextField kotlin.text  PasswordVisualTransformation kotlin.text  PracticeTypeSelection kotlin.text  ProfileActivity kotlin.text  ProfileCard kotlin.text  
ProfileScreen kotlin.text  QuickActionsCard kotlin.text  QuizMode kotlin.text  Row kotlin.text  SentenceItem kotlin.text  
SentencesList kotlin.text  SettingsCard kotlin.text  SettingsDialog kotlin.text  Spacer kotlin.text  SpellingMode kotlin.text  
StatisticItem kotlin.text  StatisticsCard kotlin.text  Surface kotlin.text  System kotlin.text  Text kotlin.text  
TextButton kotlin.text  TextbookItem kotlin.text  Toast kotlin.text  WindowCompat kotlin.text  WordLearningScreen kotlin.text  WrongQuestionActivity kotlin.text  WrongQuestionItem kotlin.text  WrongQuestionScreen kotlin.text  WrongQuestionStatisticItem kotlin.text  WrongQuestionStatisticsCard kotlin.text  com kotlin.text  contains kotlin.text  	emptyList kotlin.text  emptyMap kotlin.text  equals kotlin.text  fillMaxSize kotlin.text  fillMaxWidth kotlin.text  forEach kotlin.text  getValue kotlin.text  height kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  java kotlin.text  last kotlin.text  let kotlin.text  listOf kotlin.text  padding kotlin.text  provideDelegate kotlin.text  size kotlin.text  to kotlin.text  width kotlin.text  CoroutineScope kotlinx.coroutines  Intent !kotlinx.coroutines.CoroutineScope  OnboardingActivity !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  Toast !kotlinx.coroutines.CoroutineScope  contains !kotlinx.coroutines.CoroutineScope  getCONTAINS !kotlinx.coroutines.CoroutineScope  getContains !kotlinx.coroutines.CoroutineScope  getLET !kotlinx.coroutines.CoroutineScope  getLet !kotlinx.coroutines.CoroutineScope  java !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         