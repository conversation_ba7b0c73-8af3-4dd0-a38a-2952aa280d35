"""
数据库连接和会话管理
"""

from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import NullPool
import redis.asyncio as redis
from influxdb_client.client.influxdb_client_async import InfluxDBClientAsync
from motor.motor_asyncio import AsyncIOMotorClient
from elasticsearch import AsyncElasticsearch

from app.core.config import settings, DATABASE_CONFIG, REDIS_CONFIG


# SQLAlchemy 基础类
Base = declarative_base()

# 创建异步数据库引擎
engine = create_async_engine(
    DATABASE_CONFIG["url"],
    echo=DATABASE_CONFIG["echo"],
    pool_size=DATABASE_CONFIG["pool_size"],
    max_overflow=DATABASE_CONFIG["max_overflow"],
    pool_pre_ping=DATABASE_CONFIG["pool_pre_ping"],
    pool_recycle=DATABASE_CONFIG["pool_recycle"],
    poolclass=NullPool if settings.TESTING else None,
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话
    用于依赖注入
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


# Redis 连接
class RedisManager:
    """Redis 连接管理器"""
    
    def __init__(self):
        self.redis_client: redis.Redis = None
    
    async def connect(self):
        """连接 Redis"""
        self.redis_client = redis.from_url(
            REDIS_CONFIG["url"],
            encoding=REDIS_CONFIG["encoding"],
            decode_responses=REDIS_CONFIG["decode_responses"],
            socket_timeout=REDIS_CONFIG["socket_timeout"],
            socket_connect_timeout=REDIS_CONFIG["socket_connect_timeout"],
            retry_on_timeout=REDIS_CONFIG["retry_on_timeout"],
        )
        
        # 测试连接
        await self.redis_client.ping()
    
    async def disconnect(self):
        """断开 Redis 连接"""
        if self.redis_client:
            await self.redis_client.close()
    
    async def get_client(self) -> redis.Redis:
        """获取 Redis 客户端"""
        if not self.redis_client:
            await self.connect()
        return self.redis_client


# 全局 Redis 管理器实例
redis_manager = RedisManager()


async def get_redis() -> redis.Redis:
    """
    获取 Redis 客户端
    用于依赖注入
    """
    return await redis_manager.get_client()


# InfluxDB 连接
class InfluxDBManager:
    """InfluxDB 连接管理器"""
    
    def __init__(self):
        self.client: InfluxDBClientAsync = None
    
    async def connect(self):
        """连接 InfluxDB"""
        self.client = InfluxDBClientAsync(
            url=settings.INFLUXDB_URL,
            token=settings.INFLUXDB_TOKEN,
            org=settings.INFLUXDB_ORG,
        )
        
        # 测试连接
        health = await self.client.health()
        if health.status != "pass":
            raise ConnectionError("InfluxDB connection failed")
    
    async def disconnect(self):
        """断开 InfluxDB 连接"""
        if self.client:
            await self.client.close()
    
    async def get_client(self) -> InfluxDBClientAsync:
        """获取 InfluxDB 客户端"""
        if not self.client:
            await self.connect()
        return self.client


# 全局 InfluxDB 管理器实例
influxdb_manager = InfluxDBManager()


async def get_influxdb() -> InfluxDBClientAsync:
    """
    获取 InfluxDB 客户端
    用于依赖注入
    """
    return await influxdb_manager.get_client()


# MongoDB 连接
class MongoDBManager:
    """MongoDB 连接管理器"""
    
    def __init__(self):
        self.client: AsyncIOMotorClient = None
        self.database = None
    
    async def connect(self):
        """连接 MongoDB"""
        self.client = AsyncIOMotorClient(settings.MONGODB_URL)
        self.database = self.client[settings.MONGODB_DB]
        
        # 测试连接
        await self.client.admin.command('ping')
    
    async def disconnect(self):
        """断开 MongoDB 连接"""
        if self.client:
            self.client.close()
    
    async def get_database(self):
        """获取 MongoDB 数据库"""
        if not self.client:
            await self.connect()
        return self.database


# 全局 MongoDB 管理器实例
mongodb_manager = MongoDBManager()


async def get_mongodb():
    """
    获取 MongoDB 数据库
    用于依赖注入
    """
    return await mongodb_manager.get_database()


# Elasticsearch 连接
class ElasticsearchManager:
    """Elasticsearch 连接管理器"""
    
    def __init__(self):
        self.client: AsyncElasticsearch = None
    
    async def connect(self):
        """连接 Elasticsearch"""
        self.client = AsyncElasticsearch([settings.ELASTICSEARCH_URL])
        
        # 测试连接
        if not await self.client.ping():
            raise ConnectionError("Elasticsearch connection failed")
    
    async def disconnect(self):
        """断开 Elasticsearch 连接"""
        if self.client:
            await self.client.close()
    
    async def get_client(self) -> AsyncElasticsearch:
        """获取 Elasticsearch 客户端"""
        if not self.client:
            await self.connect()
        return self.client


# 全局 Elasticsearch 管理器实例
elasticsearch_manager = ElasticsearchManager()


async def get_elasticsearch() -> AsyncElasticsearch:
    """
    获取 Elasticsearch 客户端
    用于依赖注入
    """
    return await elasticsearch_manager.get_client()


# 数据库初始化
async def init_db():
    """初始化所有数据库连接"""
    try:
        # 连接 Redis
        await redis_manager.connect()
        print("✅ Redis connected successfully")
        
        # 连接 InfluxDB
        await influxdb_manager.connect()
        print("✅ InfluxDB connected successfully")
        
        # 连接 MongoDB
        await mongodb_manager.connect()
        print("✅ MongoDB connected successfully")
        
        # 连接 Elasticsearch
        await elasticsearch_manager.connect()
        print("✅ Elasticsearch connected successfully")
        
        print("🎉 All databases initialized successfully")
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        raise


async def close_db():
    """关闭所有数据库连接"""
    try:
        # 关闭 Redis
        await redis_manager.disconnect()
        print("✅ Redis disconnected")
        
        # 关闭 InfluxDB
        await influxdb_manager.disconnect()
        print("✅ InfluxDB disconnected")
        
        # 关闭 MongoDB
        await mongodb_manager.disconnect()
        print("✅ MongoDB disconnected")
        
        # 关闭 Elasticsearch
        await elasticsearch_manager.disconnect()
        print("✅ Elasticsearch disconnected")
        
        # 关闭 SQLAlchemy 引擎
        await engine.dispose()
        print("✅ PostgreSQL disconnected")
        
        print("🎉 All databases closed successfully")
        
    except Exception as e:
        print(f"❌ Database cleanup failed: {e}")


# 数据库健康检查
async def check_db_health() -> dict:
    """检查所有数据库的健康状态"""
    health_status = {
        "postgresql": False,
        "redis": False,
        "influxdb": False,
        "mongodb": False,
        "elasticsearch": False,
    }
    
    # 检查 PostgreSQL
    try:
        async with AsyncSessionLocal() as session:
            await session.execute("SELECT 1")
        health_status["postgresql"] = True
    except Exception:
        pass
    
    # 检查 Redis
    try:
        redis_client = await get_redis()
        await redis_client.ping()
        health_status["redis"] = True
    except Exception:
        pass
    
    # 检查 InfluxDB
    try:
        influx_client = await get_influxdb()
        health = await influx_client.health()
        health_status["influxdb"] = health.status == "pass"
    except Exception:
        pass
    
    # 检查 MongoDB
    try:
        mongo_db = await get_mongodb()
        await mongo_db.command('ping')
        health_status["mongodb"] = True
    except Exception:
        pass
    
    # 检查 Elasticsearch
    try:
        es_client = await get_elasticsearch()
        health_status["elasticsearch"] = await es_client.ping()
    except Exception:
        pass
    
    return health_status
