<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票智能体 - 简单测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 90%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .title {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.1rem;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border-left: 4px solid #007bff;
        }
        
        .status-card.success {
            border-left-color: #28a745;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
        }
        
        .status-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }
        
        .status-value {
            font-size: 1.2rem;
            font-weight: 700;
        }
        
        .status-value.success {
            color: #28a745;
        }
        
        .status-value.error {
            color: #dc3545;
        }
        
        .api-test {
            margin-top: 30px;
        }
        
        .api-test h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .test-result {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }
        
        .features {
            margin-top: 30px;
        }
        
        .features h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .feature-item {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 8px;
        }
        
        .feature-name {
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🚀 股票智能体</h1>
            <p class="subtitle">专业级股票分析平台 - 系统测试</p>
        </div>
        
        <div class="status-grid">
            <div class="status-card success">
                <div class="status-title">后端服务</div>
                <div class="status-value success" id="backend-status">检测中...</div>
            </div>
            
            <div class="status-card">
                <div class="status-title">前端架构</div>
                <div class="status-value success">✅ 完成</div>
            </div>
            
            <div class="status-card">
                <div class="status-title">API接口</div>
                <div class="status-value" id="api-count">20+ 接口</div>
            </div>
            
            <div class="status-card">
                <div class="status-title">页面组件</div>
                <div class="status-value success">5 个页面</div>
            </div>
        </div>
        
        <div class="api-test">
            <h3>🔧 API 接口测试</h3>
            <button class="test-button" onclick="testAPI('/health')">健康检查</button>
            <button class="test-button" onclick="testAPI('/api/v1/stocks')">股票列表</button>
            <button class="test-button" onclick="testAPI('/api/v1/stocks/hot')">热门股票</button>
            <button class="test-button" onclick="testAPI('/api/v1/market/overview')">市场概览</button>
            <button class="test-button" onclick="testAPI('/api/v1/news')">新闻列表</button>
            
            <div class="test-result" id="test-result">
                点击上方按钮测试API接口...
            </div>
        </div>
        
        <div class="features">
            <h3>🎯 核心功能特性</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <div class="feature-icon">📈</div>
                    <div class="feature-name">技术分析</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">💰</div>
                    <div class="feature-name">资金分析</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📊</div>
                    <div class="feature-name">市场分析</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎯</div>
                    <div class="feature-name">投资组合</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-name">实时数据</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔔</div>
                    <div class="feature-name">智能预警</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <div class="feature-name">响应式设计</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔒</div>
                    <div class="feature-name">安全认证</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 检查后端服务状态
        async function checkBackendStatus() {
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    document.getElementById('backend-status').textContent = '✅ 运行中';
                    document.getElementById('backend-status').className = 'status-value success';
                } else {
                    throw new Error('服务异常');
                }
            } catch (error) {
                document.getElementById('backend-status').textContent = '❌ 离线';
                document.getElementById('backend-status').className = 'status-value error';
            }
        }
        
        // 测试API接口
        async function testAPI(endpoint) {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = `<div style="color: #007bff;">正在测试: ${endpoint}</div>`;
            
            try {
                const response = await fetch(`http://localhost:8000${endpoint}`);
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div style="color: #28a745; margin-bottom: 10px;">✅ 测试成功: ${endpoint}</div>
                    <div style="color: #666;">响应状态: ${response.status}</div>
                    <div style="color: #666; margin-top: 10px;">响应数据:</div>
                    <pre style="background: #fff; padding: 10px; border-radius: 4px; margin-top: 5px; white-space: pre-wrap;">${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div style="color: #dc3545; margin-bottom: 10px;">❌ 测试失败: ${endpoint}</div>
                    <div style="color: #666;">错误信息: ${error.message}</div>
                `;
            }
        }
        
        // 页面加载时检查后端状态
        window.addEventListener('load', checkBackendStatus);
    </script>
</body>
</html>
