package com.example.elementarylearningcompanion.viewmodel;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.example.elementarylearningcompanion.dto.ApiResponse;
import com.example.elementarylearningcompanion.dto.EnglishWord;
import com.example.elementarylearningcompanion.network.RetrofitClient;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class EnglishViewModel extends ViewModel {

    private MutableLiveData<List<EnglishWord>> words = new MutableLiveData<>();
    private MutableLiveData<List<Map<String, Object>>> learningModes = new MutableLiveData<>();
    private MutableLiveData<Boolean> isLoading = new MutableLiveData<>();
    private MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private MutableLiveData<ApiResponse> recordResult = new MutableLiveData<>();

    public LiveData<List<EnglishWord>> getWords() {
        return words;
    }

    public LiveData<List<Map<String, Object>>> getLearningModes() {
        return learningModes;
    }

    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public LiveData<ApiResponse> getRecordResult() {
        return recordResult;
    }

    public void loadLearningModes() {
        isLoading.setValue(true);
        RetrofitClient.getApiService().getEnglishLearningModes().enqueue(new Callback<List<Map<String, Object>>>() {
            @Override
            public void onResponse(Call<List<Map<String, Object>>> call, Response<List<Map<String, Object>>> response) {
                isLoading.setValue(false);
                if (response.isSuccessful() && response.body() != null) {
                    learningModes.setValue(response.body());
                } else {
                    errorMessage.setValue("加载学习模式失败");
                }
            }

            @Override
            public void onFailure(Call<List<Map<String, Object>>> call, Throwable t) {
                isLoading.setValue(false);
                errorMessage.setValue("网络错误：" + t.getMessage());
            }
        });
    }

    public void loadRandomWords(int textbookVersionId, int grade, int count, Integer difficultyLevel) {
        isLoading.setValue(true);
        RetrofitClient.getApiService().getRandomEnglishWords(textbookVersionId, grade, count, difficultyLevel)
                .enqueue(new Callback<List<EnglishWord>>() {
                    @Override
                    public void onResponse(Call<List<EnglishWord>> call, Response<List<EnglishWord>> response) {
                        isLoading.setValue(false);
                        if (response.isSuccessful() && response.body() != null) {
                            words.setValue(response.body());
                        } else {
                            errorMessage.setValue("加载单词失败");
                        }
                    }

                    @Override
                    public void onFailure(Call<List<EnglishWord>> call, Throwable t) {
                        isLoading.setValue(false);
                        errorMessage.setValue("网络错误：" + t.getMessage());
                    }
                });
    }

    public void loadSpellingTest(int textbookVersionId, int grade, int count) {
        isLoading.setValue(true);
        RetrofitClient.getApiService().getSpellingTest(textbookVersionId, grade, count)
                .enqueue(new Callback<List<EnglishWord>>() {
                    @Override
                    public void onResponse(Call<List<EnglishWord>> call, Response<List<EnglishWord>> response) {
                        isLoading.setValue(false);
                        if (response.isSuccessful() && response.body() != null) {
                            words.setValue(response.body());
                        } else {
                            errorMessage.setValue("生成拼写测试失败");
                        }
                    }

                    @Override
                    public void onFailure(Call<List<EnglishWord>> call, Throwable t) {
                        isLoading.setValue(false);
                        errorMessage.setValue("网络错误：" + t.getMessage());
                    }
                });
    }

    public void recordLearning(long studentId, long wordId, String learningType, Boolean isCorrect) {
        Map<String, Object> request = new HashMap<>();
        request.put("studentId", studentId);
        request.put("wordId", wordId);
        request.put("learningType", learningType);
        if (isCorrect != null) {
            request.put("isCorrect", isCorrect);
        }

        RetrofitClient.getApiService().recordEnglishLearning(request).enqueue(new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    recordResult.setValue(response.body());
                } else {
                    recordResult.setValue(new ApiResponse(false, "记录学习失败"));
                }
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                recordResult.setValue(new ApiResponse(false, "网络错误：" + t.getMessage()));
            }
        });
    }

    public void clearRecordResult() {
        recordResult.setValue(null);
    }

    public void clearErrorMessage() {
        errorMessage.setValue(null);
    }
}
