package com.example.elementarylearningcompanion.repository;

import com.example.elementarylearningcompanion.model.MathExercise;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MathExerciseRepository extends JpaRepository<MathExercise, Long> {
    
    List<MathExercise> findByTextbookVersionIdAndGrade(Integer textbookVersionId, Integer grade);
    
    List<MathExercise> findByTextbookVersionIdAndGradeAndExerciseType(
        Integer textbookVersionId, Integer grade, String exerciseType);
    
    List<MathExercise> findByTextbookVersionIdAndGradeAndDifficultyLevel(
        Integer textbookVersionId, Integer grade, Integer difficultyLevel);
    
    @Query("SELECT m FROM MathExercise m WHERE m.textbookVersionId = :textbookVersionId " +
           "AND m.grade = :grade AND m.exerciseType = :exerciseType " +
           "AND m.difficultyLevel = :difficultyLevel ORDER BY RAND() LIMIT :limit")
    List<MathExercise> findRandomExercises(@Param("textbookVersionId") Integer textbookVersionId,
                                          @Param("grade") Integer grade,
                                          @Param("exerciseType") String exerciseType,
                                          @Param("difficultyLevel") Integer difficultyLevel,
                                          @Param("limit") Integer limit);
    
    @Query("SELECT DISTINCT m.exerciseType FROM MathExercise m WHERE m.textbookVersionId = :textbookVersionId AND m.grade = :grade")
    List<String> findDistinctExerciseTypes(@Param("textbookVersionId") Integer textbookVersionId,
                                          @Param("grade") Integer grade);
}
