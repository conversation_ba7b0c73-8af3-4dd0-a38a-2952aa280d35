/**
 * 自选股页面
 */

import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Row,
  Col,
  Card,
  Table,
  Typography,
  Space,
  Button,
  Input,
  Select,
  Tag,
  Modal,
  message,
  Empty,
  Popconfirm,
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  SearchOutlined,
  StarFilled,
  BellOutlined,
  ReloadOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '@/store';
import {
  fetchWatchlistAsync,
  addToWatchlistAsync,
  removeFromWatchlistAsync,
  selectWatchlist,
  selectWatchlistLoading,
} from '@/store/slices/watchlistSlice';
import StockPriceDisplay from '@/components/StockPriceDisplay';
import SearchBar from '@/components/SearchBar';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

interface WatchlistItem {
  id: string;
  stock_code: string;
  stock_name: string;
  market: string;
  industry: string;
  close_price: number;
  change: number;
  pct_change: number;
  volume: number;
  amount: number;
  turnover_rate: number;
  pe_ratio: number;
  pb_ratio: number;
  added_time: string;
  alert_enabled: boolean;
  alert_price_high?: number;
  alert_price_low?: number;
}

const WatchlistPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  // 状态
  const watchlist = useAppSelector(selectWatchlist);
  const watchlistLoading = useAppSelector(selectWatchlistLoading);
  
  // 本地状态
  const [searchText, setSearchText] = useState('');
  const [sortBy, setSortBy] = useState<string>('added_time');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  // 初始化数据
  useEffect(() => {
    dispatch(fetchWatchlistAsync());
  }, [dispatch]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  // 处理排序
  const handleSortChange = (field: string) => {
    if (field === sortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  // 处理添加股票
  const handleAddStock = (stockCode: string) => {
    dispatch(addToWatchlistAsync(stockCode)).then(() => {
      message.success('已添加到自选股');
      setAddModalVisible(false);
    });
  };

  // 处理移除股票
  const handleRemoveStock = (id: string) => {
    dispatch(removeFromWatchlistAsync(id)).then(() => {
      message.success('已移除自选股');
    });
  };

  // 批量移除
  const handleBatchRemove = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要移除的股票');
      return;
    }
    
    Modal.confirm({
      title: '确认移除',
      content: `确定要移除选中的 ${selectedRowKeys.length} 只股票吗？`,
      onOk: () => {
        Promise.all(
          selectedRowKeys.map(id => dispatch(removeFromWatchlistAsync(id)))
        ).then(() => {
          message.success('批量移除成功');
          setSelectedRowKeys([]);
        });
      },
    });
  };

  // 刷新数据
  const handleRefresh = () => {
    dispatch(fetchWatchlistAsync());
  };

  // 过滤数据
  const filteredData = watchlist.filter(item => {
    if (!searchText) return true;
    return (
      item.stock_code.toLowerCase().includes(searchText.toLowerCase()) ||
      item.stock_name.toLowerCase().includes(searchText.toLowerCase())
    );
  });

  // 排序数据
  const sortedData = [...filteredData].sort((a, b) => {
    const aValue = a[sortBy as keyof WatchlistItem];
    const bValue = b[sortBy as keyof WatchlistItem];
    
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    }
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortOrder === 'asc' 
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }
    
    return 0;
  });

  // 表格列配置
  const columns = [
    {
      title: '股票',
      key: 'stock',
      fixed: 'left' as const,
      width: 200,
      render: (record: WatchlistItem) => (
        <div>
          <div className="flex-center">
            <StarFilled style={{ color: '#faad14', marginRight: 8 }} />
            <Text strong className="stock-code">
              {record.stock_code}
            </Text>
          </div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.stock_name}
          </Text>
          <br />
          <Tag size="small" color="blue">
            {record.market}
          </Tag>
        </div>
      ),
    },
    {
      title: '行业',
      dataIndex: 'industry',
      key: 'industry',
      width: 120,
      render: (industry: string) => (
        <Text style={{ fontSize: 12 }}>{industry || '-'}</Text>
      ),
    },
    {
      title: '价格',
      key: 'price',
      width: 120,
      sorter: true,
      sortOrder: sortBy === 'close_price' ? sortOrder : null,
      render: (record: WatchlistItem) => (
        <StockPriceDisplay
          price={record.close_price}
          change={record.change}
          pctChange={record.pct_change}
          size="small"
        />
      ),
    },
    {
      title: '成交量',
      dataIndex: 'volume',
      key: 'volume',
      width: 100,
      sorter: true,
      sortOrder: sortBy === 'volume' ? sortOrder : null,
      render: (volume: number) => (
        <Text style={{ fontSize: 12 }}>
          {volume ? (volume / 10000).toFixed(1) + '万' : '-'}
        </Text>
      ),
    },
    {
      title: '成交额',
      dataIndex: 'amount',
      key: 'amount',
      width: 100,
      sorter: true,
      sortOrder: sortBy === 'amount' ? sortOrder : null,
      render: (amount: number) => (
        <Text style={{ fontSize: 12 }}>
          {amount ? (amount / 100000000).toFixed(2) + '亿' : '-'}
        </Text>
      ),
    },
    {
      title: '换手率',
      dataIndex: 'turnover_rate',
      key: 'turnover_rate',
      width: 80,
      sorter: true,
      sortOrder: sortBy === 'turnover_rate' ? sortOrder : null,
      render: (rate: number) => (
        <Text style={{ fontSize: 12 }}>
          {rate ? rate.toFixed(2) + '%' : '-'}
        </Text>
      ),
    },
    {
      title: 'PE',
      dataIndex: 'pe_ratio',
      key: 'pe_ratio',
      width: 80,
      render: (pe: number) => (
        <Text style={{ fontSize: 12 }}>
          {pe ? pe.toFixed(1) : '-'}
        </Text>
      ),
    },
    {
      title: '预警',
      key: 'alert',
      width: 80,
      render: (record: WatchlistItem) => (
        <Button
          type="text"
          size="small"
          icon={<BellOutlined />}
          style={{ 
            color: record.alert_enabled ? '#1890ff' : '#d9d9d9' 
          }}
          onClick={(e) => {
            e.stopPropagation();
            navigate(`/alerts/create?stock=${record.stock_code}`);
          }}
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (record: WatchlistItem) => (
        <Popconfirm
          title="确定要移除这只股票吗？"
          onConfirm={(e) => {
            e?.stopPropagation();
            handleRemoveStock(record.id);
          }}
          onCancel={(e) => e?.stopPropagation()}
        >
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={(e) => e.stopPropagation()}
          />
        </Popconfirm>
      ),
    },
  ];

  return (
    <div>
      {/* 页面标题 */}
      <div className="flex-between" style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          我的自选股
        </Title>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setAddModalVisible(true)}
          >
            添加股票
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={watchlistLoading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 工具栏 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索股票代码或名称"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              value={sortBy}
              onChange={(value) => setSortBy(value)}
              style={{ width: '100%' }}
            >
              <Option value="added_time">添加时间</Option>
              <Option value="pct_change">涨跌幅</Option>
              <Option value="volume">成交量</Option>
              <Option value="amount">成交额</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Button
              icon={sortOrder === 'asc' ? <SortAscendingOutlined /> : <SortDescendingOutlined />}
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            >
              {sortOrder === 'asc' ? '升序' : '降序'}
            </Button>
          </Col>
          {selectedRowKeys.length > 0 && (
            <Col>
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={handleBatchRemove}
              >
                批量移除 ({selectedRowKeys.length})
              </Button>
            </Col>
          )}
        </Row>
      </Card>

      {/* 自选股列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={sortedData}
          loading={watchlistLoading}
          rowKey="id"
          scroll={{ x: 1000 }}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          locale={{
            emptyText: (
              <Empty
                description="暂无自选股"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              >
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setAddModalVisible(true)}
                >
                  添加股票
                </Button>
              </Empty>
            ),
          }}
          pagination={{
            total: sortedData.length,
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 只股票`,
          }}
          onRow={(record) => ({
            onClick: () => navigate(`/stocks/${record.stock_code}`),
            style: { cursor: 'pointer' },
          })}
          onChange={(pagination, filters, sorter: any) => {
            if (sorter.field) {
              handleSortChange(sorter.field);
            }
          }}
        />
      </Card>

      {/* 添加股票弹窗 */}
      <Modal
        title="添加自选股"
        open={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ padding: '20px 0' }}>
          <SearchBar
            placeholder="搜索股票代码或名称"
            onSelect={(stock) => handleAddStock(stock.code)}
            showAddButton
          />
        </div>
      </Modal>
    </div>
  );
};

export default WatchlistPage;
