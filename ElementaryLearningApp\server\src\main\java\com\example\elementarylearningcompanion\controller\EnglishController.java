package com.example.elementarylearningcompanion.controller;

import com.example.elementarylearningcompanion.dto.ApiResponse;
import com.example.elementarylearningcompanion.model.EnglishWord;
import com.example.elementarylearningcompanion.model.WrongQuestion;
import com.example.elementarylearningcompanion.service.EnglishService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/english")
public class EnglishController {

    @Autowired
    private EnglishService englishService;

    @GetMapping("/words")
    public ResponseEntity<List<EnglishWord>> getWords(
            @RequestParam Integer textbookVersionId,
            @RequestParam Integer grade,
            @RequestParam(required = false) String unit) {
        List<EnglishWord> words;
        if (unit != null && !unit.isEmpty()) {
            words = englishService.getWordsByUnit(textbookVersionId, grade, unit);
        } else {
            words = englishService.getWordsByGrade(textbookVersionId, grade);
        }
        return ResponseEntity.ok(words);
    }

    @GetMapping("/words/random")
    public ResponseEntity<List<EnglishWord>> getRandomWords(
            @RequestParam Integer textbookVersionId,
            @RequestParam Integer grade,
            @RequestParam(defaultValue = "10") Integer count,
            @RequestParam(required = false) Integer difficultyLevel) {
        List<EnglishWord> words;
        if (difficultyLevel != null) {
            words = englishService.getRandomWordsByDifficulty(textbookVersionId, grade, difficultyLevel, count);
        } else {
            words = englishService.getRandomWords(textbookVersionId, grade, count);
        }
        return ResponseEntity.ok(words);
    }

    @GetMapping("/units")
    public ResponseEntity<List<String>> getUnits(
            @RequestParam Integer textbookVersionId,
            @RequestParam Integer grade) {
        List<String> units = englishService.getAvailableUnits(textbookVersionId, grade);
        return ResponseEntity.ok(units);
    }

    @PostMapping("/record-learning")
    public ResponseEntity<ApiResponse> recordLearning(@RequestBody Map<String, Object> request) {
        try {
            Long studentId = Long.valueOf(request.get("studentId").toString());
            Long wordId = Long.valueOf(request.get("wordId").toString());
            String learningType = request.get("learningType").toString();
            Boolean isCorrect = request.get("isCorrect") != null ? 
                Boolean.valueOf(request.get("isCorrect").toString()) : null;

            englishService.recordWordLearning(studentId, wordId, learningType, isCorrect);
            
            return ResponseEntity.ok(new ApiResponse(true, "学习记录保存成功"));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "保存学习记录失败：" + e.getMessage()));
        }
    }

    @GetMapping("/spelling-test")
    public ResponseEntity<List<EnglishWord>> generateSpellingTest(
            @RequestParam Integer textbookVersionId,
            @RequestParam Integer grade,
            @RequestParam(defaultValue = "10") Integer count) {
        List<EnglishWord> words = englishService.generateSpellingTest(textbookVersionId, grade, count);
        return ResponseEntity.ok(words);
    }

    @GetMapping("/vocabulary-quiz")
    public ResponseEntity<List<EnglishService.VocabularyQuiz>> generateVocabularyQuiz(
            @RequestParam Integer textbookVersionId,
            @RequestParam Integer grade,
            @RequestParam(defaultValue = "10") Integer count) {
        List<EnglishService.VocabularyQuiz> quizzes = englishService.generateVocabularyQuiz(textbookVersionId, grade, count);
        return ResponseEntity.ok(quizzes);
    }

    @GetMapping("/wrong-words/{studentId}")
    public ResponseEntity<List<WrongQuestion>> getWrongWords(@PathVariable Long studentId) {
        List<WrongQuestion> wrongWords = englishService.getWrongWords(studentId);
        return ResponseEntity.ok(wrongWords);
    }

    @GetMapping("/mastery-level/{studentId}")
    public ResponseEntity<Map<String, Object>> getMasteryLevel(@PathVariable Long studentId) {
        Double masteryLevel = englishService.getStudentMasteryLevel(studentId);
        return ResponseEntity.ok(Map.of(
            "studentId", studentId,
            "masteryLevel", masteryLevel != null ? masteryLevel : 0.0
        ));
    }

    @GetMapping("/learning-modes")
    public ResponseEntity<List<Map<String, Object>>> getLearningModes() {
        List<Map<String, Object>> modes = List.of(
            Map.of("mode", "flashcard", "name", "单词卡片", "description", "浏览单词和释义"),
            Map.of("mode", "listen", "name", "听力练习", "description", "听发音学单词"),
            Map.of("mode", "spell", "name", "拼写练习", "description", "练习单词拼写"),
            Map.of("mode", "quiz", "name", "词汇测试", "description", "选择题测试词汇")
        );
        return ResponseEntity.ok(modes);
    }
}
