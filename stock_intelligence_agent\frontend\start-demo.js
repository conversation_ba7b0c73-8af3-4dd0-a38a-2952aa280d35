/**
 * 前端演示启动脚本
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 启动股票智能体前端演示...\n');

// 检查必要文件
const requiredFiles = [
    'package.json',
    'src/App.tsx',
    'src/store/index.ts',
    'public/index.html'
];

console.log('📋 检查项目文件...');
let allFilesExist = true;
for (const file of requiredFiles) {
    if (fs.existsSync(path.join(__dirname, file))) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - 文件不存在`);
        allFilesExist = false;
    }
}

if (!allFilesExist) {
    console.log('\n❌ 项目文件不完整，无法启动演示');
    process.exit(1);
}

// 检查node_modules
if (!fs.existsSync(path.join(__dirname, 'node_modules'))) {
    console.log('\n📦 正在安装依赖...');
    try {
        execSync('npm install', { 
            stdio: 'inherit',
            cwd: __dirname 
        });
        console.log('✅ 依赖安装完成');
    } catch (error) {
        console.log('❌ 依赖安装失败:', error.message);
        console.log('\n💡 请手动运行: npm install');
        process.exit(1);
    }
} else {
    console.log('✅ 依赖已安装');
}

// 创建环境变量文件
const envContent = `
REACT_APP_API_BASE_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000/ws
REACT_APP_VERSION=1.0.0
GENERATE_SOURCEMAP=false
`;

fs.writeFileSync(path.join(__dirname, '.env'), envContent.trim());
console.log('✅ 环境变量配置完成');

// 启动开发服务器
console.log('\n🌐 启动开发服务器...');
console.log('📍 前端地址: http://localhost:3000');
console.log('📖 详细测试: file://' + path.join(__dirname, 'detailed-test.html'));
console.log('🔧 简单测试: file://' + path.join(__dirname, 'simple-test.html'));

try {
    // 启动React开发服务器
    const reactProcess = spawn('npm', ['start'], {
        stdio: 'inherit',
        cwd: __dirname,
        shell: true
    });

    // 处理进程退出
    reactProcess.on('close', (code) => {
        console.log(`\n开发服务器已停止 (退出码: ${code})`);
    });

    // 处理Ctrl+C
    process.on('SIGINT', () => {
        console.log('\n正在停止开发服务器...');
        reactProcess.kill('SIGINT');
        process.exit(0);
    });

} catch (error) {
    console.log('❌ 启动失败:', error.message);
    console.log('\n💡 请检查:');
    console.log('1. Node.js 版本是否 >= 16');
    console.log('2. npm 是否正常工作');
    console.log('3. 端口 3000 是否被占用');
    process.exit(1);
}

// 延迟打开浏览器
setTimeout(() => {
    console.log('\n🌐 正在打开浏览器...');
    
    const { exec } = require('child_process');
    const platform = process.platform;
    
    let command;
    if (platform === 'win32') {
        command = 'start http://localhost:3000';
    } else if (platform === 'darwin') {
        command = 'open http://localhost:3000';
    } else {
        command = 'xdg-open http://localhost:3000';
    }
    
    exec(command, (error) => {
        if (error) {
            console.log('⚠️ 无法自动打开浏览器，请手动访问: http://localhost:3000');
        }
    });
}, 5000);
