1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.elementarylearningcompanion"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:5:5-67
11-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:5:22-64
12
13    <permission
13-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
14        android:name="com.example.elementarylearningcompanion.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
14-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
15        android:protectionLevel="signature" />
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
16
17    <uses-permission android:name="com.example.elementarylearningcompanion.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
18
19    <application
19-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:7:5-62:19
20        android:allowBackup="true"
20-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:8:9-35
21        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
22        android:dataExtractionRules="@xml/data_extraction_rules"
22-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:9:9-65
23        android:debuggable="true"
24        android:extractNativeLibs="false"
25        android:fullBackupContent="@xml/backup_rules"
25-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:10:9-54
26        android:label="@string/app_name"
26-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:11:9-41
27        android:supportsRtl="true"
27-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.ElementaryLearningCompanion"
29-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:13:9-65
30        android:usesCleartextTraffic="true" >
30-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:14:9-44
31        <activity
31-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:16:9-26:20
32            android:name="com.example.elementarylearningcompanion.MainActivity"
32-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:17:13-41
33            android:exported="true"
33-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:18:13-36
34            android:label="@string/app_name"
34-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:19:13-45
35            android:theme="@style/Theme.ElementaryLearningCompanion" >
35-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:20:13-69
36            <intent-filter>
36-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:21:13-25:29
37                <action android:name="android.intent.action.MAIN" />
37-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:22:17-69
37-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:22:25-66
38
39                <category android:name="android.intent.category.LAUNCHER" />
39-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:24:17-77
39-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:24:27-74
40            </intent-filter>
41        </activity>
42        <activity
42-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:27:9-31:72
43            android:name="com.example.elementarylearningcompanion.OnboardingActivity"
43-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:28:13-47
44            android:exported="false"
44-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:29:13-37
45            android:label="初始设置"
45-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:30:13-33
46            android:theme="@style/Theme.ElementaryLearningCompanion" />
46-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:31:13-69
47        <activity
47-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:32:9-36:72
48            android:name="com.example.elementarylearningcompanion.LessonActivity"
48-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:33:13-43
49            android:exported="false"
49-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:34:13-37
50            android:label="课文学习"
50-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:35:13-33
51            android:theme="@style/Theme.ElementaryLearningCompanion" />
51-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:36:13-69
52        <activity
52-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:37:9-41:72
53            android:name="com.example.elementarylearningcompanion.MathActivity"
53-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:38:13-41
54            android:exported="false"
54-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:39:13-37
55            android:label="数学练习"
55-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:40:13-33
56            android:theme="@style/Theme.ElementaryLearningCompanion" />
56-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:41:13-69
57        <activity
57-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:42:9-46:72
58            android:name="com.example.elementarylearningcompanion.EnglishActivity"
58-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:43:13-44
59            android:exported="false"
59-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:44:13-37
60            android:label="英语学习"
60-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:45:13-33
61            android:theme="@style/Theme.ElementaryLearningCompanion" />
61-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:46:13-69
62        <activity
62-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:47:9-51:72
63            android:name="com.example.elementarylearningcompanion.WrongQuestionActivity"
63-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:48:13-50
64            android:exported="false"
64-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:49:13-37
65            android:label="错题本"
65-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:50:13-32
66            android:theme="@style/Theme.ElementaryLearningCompanion" />
66-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:51:13-69
67        <activity
67-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:52:9-56:72
68            android:name="com.example.elementarylearningcompanion.ProfileActivity"
68-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:53:13-44
69            android:exported="false"
69-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:54:13-37
70            android:label="个人中心"
70-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:55:13-33
71            android:theme="@style/Theme.ElementaryLearningCompanion" />
71-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:56:13-69
72        <activity
72-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:57:9-61:72
73            android:name="com.example.elementarylearningcompanion.HomeActivity"
73-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:58:13-41
74            android:exported="false"
74-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:59:13-37
75            android:label="主页"
75-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:60:13-31
76            android:theme="@style/Theme.ElementaryLearningCompanion" />
76-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:61:13-69
77        <activity
77-->[androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\c159823cd1d5b89649bf0a3c97706e19\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:23:9-25:39
78            android:name="androidx.activity.ComponentActivity"
78-->[androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\c159823cd1d5b89649bf0a3c97706e19\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:24:13-63
79            android:exported="true" />
79-->[androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\c159823cd1d5b89649bf0a3c97706e19\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:25:13-36
80        <activity
80-->[androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\81e6a57d5e25883e08f3f201e44bac3c\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
81            android:name="androidx.compose.ui.tooling.PreviewActivity"
81-->[androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\81e6a57d5e25883e08f3f201e44bac3c\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
82            android:exported="true" />
82-->[androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\81e6a57d5e25883e08f3f201e44bac3c\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
83
84        <provider
84-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
85            android:name="androidx.startup.InitializationProvider"
85-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
86            android:authorities="com.example.elementarylearningcompanion.androidx-startup"
86-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
87            android:exported="false" >
87-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
88            <meta-data
88-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
89                android:name="androidx.emoji2.text.EmojiCompatInitializer"
89-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
90                android:value="androidx.startup" />
90-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
91            <meta-data
91-->[androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:29:13-31:52
92                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
92-->[androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:30:17-78
93                android:value="androidx.startup" />
93-->[androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:31:17-49
94            <meta-data
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
95                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
96                android:value="androidx.startup" />
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
97        </provider>
98
99        <receiver
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
100            android:name="androidx.profileinstaller.ProfileInstallReceiver"
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
101            android:directBootAware="false"
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
102            android:enabled="true"
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
103            android:exported="true"
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
104            android:permission="android.permission.DUMP" >
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
105            <intent-filter>
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
106                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
107            </intent-filter>
108            <intent-filter>
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
109                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
110            </intent-filter>
111            <intent-filter>
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
112                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
113            </intent-filter>
114            <intent-filter>
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
115                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
116            </intent-filter>
117        </receiver>
118    </application>
119
120</manifest>
