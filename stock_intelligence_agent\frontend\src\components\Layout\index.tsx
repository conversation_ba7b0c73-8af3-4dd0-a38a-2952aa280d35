/**
 * 主布局组件
 */

import React, { useState, useEffect } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout as AntLayout,
  Menu,
  Avatar,
  Dropdown,
  Button,
  Badge,
  Space,
  Drawer,
  Typography,
  Divider,
} from 'antd';
import {
  HomeOutlined,
  StockOutlined,
  FileTextOutlined,
  HeartOutlined,
  BellOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SearchOutlined,
  GlobalOutlined,
  SunOutlined,
  MoonOutlined,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '@/store';
import { logoutAsync } from '@/store/slices/authSlice';
import {
  toggleSidebar,
  toggleMobileSidebar,
  toggleTheme,
  selectSidebarCollapsed,
  selectMobileSidebarVisible,
  selectTheme,
} from '@/store/slices/uiSlice';
import SearchBar from '@/components/SearchBar';
import NotificationCenter from '@/components/NotificationCenter';

const { Header, Sider, Content } = AntLayout;
const { Text } = Typography;

// 菜单项配置
const menuItems = [
  {
    key: '/',
    icon: <HomeOutlined />,
    label: '首页',
  },
  {
    key: '/stocks',
    icon: <StockOutlined />,
    label: '股票',
  },
  {
    key: '/news',
    icon: <FileTextOutlined />,
    label: '新闻',
  },
  {
    key: '/watchlist',
    icon: <HeartOutlined />,
    label: '自选股',
  },
  {
    key: '/alerts',
    icon: <BellOutlined />,
    label: '预警',
  },
];

const Layout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  
  // 状态
  const user = useAppSelector(state => state.auth.user);
  const sidebarCollapsed = useAppSelector(selectSidebarCollapsed);
  const mobileSidebarVisible = useAppSelector(selectMobileSidebarVisible);
  const theme = useAppSelector(selectTheme);
  
  // 本地状态
  const [isMobile, setIsMobile] = useState(false);
  const [notificationVisible, setNotificationVisible] = useState(false);

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
    
    // 移动端点击菜单后关闭侧边栏
    if (isMobile) {
      dispatch(toggleMobileSidebar());
    }
  };

  // 处理登出
  const handleLogout = () => {
    dispatch(logoutAsync());
  };

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  // 侧边栏内容
  const sidebarContent = (
    <div className="h-full flex flex-column">
      {/* Logo */}
      <div className="p-3 text-center">
        <div className="flex-center">
          <StockOutlined style={{ fontSize: 24, color: '#1890ff' }} />
          {!sidebarCollapsed && (
            <Text strong className="ml-2" style={{ fontSize: 16 }}>
              股票智能体
            </Text>
          )}
        </div>
      </div>
      
      <Divider style={{ margin: '8px 0' }} />
      
      {/* 菜单 */}
      <Menu
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        style={{ border: 'none', flex: 1 }}
      />
      
      {/* 底部信息 */}
      {!sidebarCollapsed && (
        <div className="p-3 text-center">
          <Text type="secondary" style={{ fontSize: 12 }}>
            版本 1.0.0
          </Text>
        </div>
      )}
    </div>
  );

  return (
    <AntLayout className="full-height">
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={sidebarCollapsed}
          width={256}
          collapsedWidth={80}
          theme="light"
          style={{
            overflow: 'auto',
            height: '100vh',
            position: 'fixed',
            left: 0,
            top: 0,
            bottom: 0,
          }}
        >
          {sidebarContent}
        </Sider>
      )}

      {/* 移动端侧边栏 */}
      {isMobile && (
        <Drawer
          title={
            <div className="flex-center">
              <StockOutlined style={{ fontSize: 20, color: '#1890ff' }} />
              <Text strong className="ml-2">
                股票智能体
              </Text>
            </div>
          }
          placement="left"
          onClose={() => dispatch(toggleMobileSidebar())}
          open={mobileSidebarVisible}
          bodyStyle={{ padding: 0 }}
          width={256}
        >
          {sidebarContent}
        </Drawer>
      )}

      <AntLayout
        style={{
          marginLeft: isMobile ? 0 : sidebarCollapsed ? 80 : 256,
          transition: 'margin-left 0.2s',
        }}
      >
        {/* 头部 */}
        <Header
          style={{
            padding: '0 24px',
            background: '#fff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            position: 'sticky',
            top: 0,
            zIndex: 100,
          }}
        >
          {/* 左侧 */}
          <div className="flex-center">
            <Button
              type="text"
              icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => {
                if (isMobile) {
                  dispatch(toggleMobileSidebar());
                } else {
                  dispatch(toggleSidebar());
                }
              }}
              style={{ fontSize: 16 }}
            />
            
            {/* 搜索框 */}
            <div className="ml-3" style={{ width: 300 }}>
              <SearchBar />
            </div>
          </div>

          {/* 右侧 */}
          <Space size="middle">
            {/* 主题切换 */}
            <Button
              type="text"
              icon={theme === 'light' ? <MoonOutlined /> : <SunOutlined />}
              onClick={() => dispatch(toggleTheme())}
              title={theme === 'light' ? '切换到深色主题' : '切换到浅色主题'}
            />

            {/* 通知中心 */}
            <Badge count={5} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                onClick={() => setNotificationVisible(true)}
                title="通知中心"
              />
            </Badge>

            {/* 用户菜单 */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <div className="flex-center cursor-pointer">
                <Avatar
                  size="small"
                  icon={<UserOutlined />}
                  src={user?.avatar_url}
                />
                <Text className="ml-2" style={{ maxWidth: 100 }}>
                  {user?.full_name || user?.username}
                </Text>
              </div>
            </Dropdown>
          </Space>
        </Header>

        {/* 内容区域 */}
        <Content
          style={{
            margin: 0,
            padding: 24,
            minHeight: 'calc(100vh - 64px)',
            background: '#f5f5f5',
            overflow: 'auto',
          }}
        >
          <Outlet />
        </Content>
      </AntLayout>

      {/* 通知中心 */}
      <NotificationCenter
        visible={notificationVisible}
        onClose={() => setNotificationVisible(false)}
      />
    </AntLayout>
  );
};

export default Layout;
