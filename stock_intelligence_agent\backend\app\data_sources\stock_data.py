"""
股票数据源提供者
支持多个数据源：新浪财经、腾讯财经、东方财富等
"""

import asyncio
import aiohttp
import json
import re
from typing import List, Dict, Any, Optional
from datetime import datetime, date
import logging

logger = logging.getLogger(__name__)

class StockDataProvider:
    """股票数据提供者"""
    
    def __init__(self):
        self.session = None
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://finance.sina.com.cn/',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            headers=self.headers,
            timeout=aiohttp.ClientTimeout(total=10)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_stock_list(self, market: str = "all") -> List[Dict[str, Any]]:
        """获取股票列表"""
        try:
            # 使用新浪财经API获取股票列表
            if market.upper() == "SH" or market == "all":
                sh_stocks = await self._get_sh_stocks()
            else:
                sh_stocks = []
                
            if market.upper() == "SZ" or market == "all":
                sz_stocks = await self._get_sz_stocks()
            else:
                sz_stocks = []
            
            all_stocks = sh_stocks + sz_stocks
            
            # 获取实时行情数据
            if all_stocks:
                stock_codes = [stock['code'] for stock in all_stocks[:100]]  # 限制数量避免请求过大
                quotes = await self.get_realtime_quotes(stock_codes)
                
                # 合并数据
                for stock in all_stocks:
                    quote = quotes.get(stock['code'], {})
                    stock.update(quote)
            
            return all_stocks
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return self._get_mock_stock_list()
    
    async def _get_sh_stocks(self) -> List[Dict[str, Any]]:
        """获取上海股票列表"""
        try:
            # 上证主板股票
            url = "http://query.sse.com.cn/security/stock/getStockListData2.do?&jsonCallBack=jsonpCallback&isPagination=true&stockCode=&csrcCode=&areaName=&stockType=1"
            
            async with self.session.get(url) as response:
                text = await response.text()
                # 解析JSONP响应
                json_str = re.search(r'jsonpCallback\((.*)\)', text)
                if json_str:
                    data = json.loads(json_str.group(1))
                    stocks = []
                    for item in data.get('pageHelp', {}).get('data', []):
                        stocks.append({
                            'code': item.get('SECURITY_CODE_A', ''),
                            'name': item.get('SECURITY_ABBR_A', ''),
                            'market': 'SH',
                            'industry': item.get('CSRC_CODE', ''),
                            'list_date': item.get('LIST_DATE', ''),
                        })
                    return stocks[:50]  # 限制数量
        except Exception as e:
            logger.error(f"获取上海股票列表失败: {e}")
        
        return []
    
    async def _get_sz_stocks(self) -> List[Dict[str, Any]]:
        """获取深圳股票列表"""
        try:
            # 深证主板股票
            url = "http://www.szse.cn/api/report/ShowReport?SHOWTYPE=JSON&CATALOGID=1110&TABKEY=tab1"
            
            async with self.session.get(url) as response:
                data = await response.json()
                stocks = []
                for item in data.get('data', []):
                    stocks.append({
                        'code': item.get('zqdm', ''),
                        'name': item.get('zqjc', ''),
                        'market': 'SZ',
                        'industry': item.get('sshy', ''),
                        'list_date': item.get('ssrq', ''),
                    })
                return stocks[:50]  # 限制数量
        except Exception as e:
            logger.error(f"获取深圳股票列表失败: {e}")
        
        return []
    
    async def get_realtime_quotes(self, stock_codes: List[str]) -> Dict[str, Dict[str, Any]]:
        """获取实时行情数据"""
        try:
            # 构建新浪财经实时行情API请求
            formatted_codes = []
            for code in stock_codes:
                if code.startswith('6'):
                    formatted_codes.append(f'sh{code}')
                else:
                    formatted_codes.append(f'sz{code}')
            
            # 分批请求，每次最多50只股票
            all_quotes = {}
            batch_size = 50
            
            for i in range(0, len(formatted_codes), batch_size):
                batch_codes = formatted_codes[i:i + batch_size]
                codes_str = ','.join(batch_codes)
                
                url = f"http://hq.sinajs.cn/list={codes_str}"
                
                async with self.session.get(url) as response:
                    text = await response.text(encoding='gbk')
                    quotes = self._parse_sina_quotes(text)
                    all_quotes.update(quotes)
            
            return all_quotes
            
        except Exception as e:
            logger.error(f"获取实时行情失败: {e}")
            return {}
    
    def _parse_sina_quotes(self, text: str) -> Dict[str, Dict[str, Any]]:
        """解析新浪财经行情数据"""
        quotes = {}
        
        for line in text.strip().split('\n'):
            if 'var hq_str_' in line:
                try:
                    # 提取股票代码和数据
                    code_match = re.search(r'hq_str_([a-z]{2}\d{6})', line)
                    data_match = re.search(r'"([^"]*)"', line)
                    
                    if code_match and data_match:
                        full_code = code_match.group(1)
                        code = full_code[2:]  # 去掉sh/sz前缀
                        data_str = data_match.group(1)
                        
                        if data_str:
                            parts = data_str.split(',')
                            if len(parts) >= 32:
                                quotes[code] = {
                                    'name': parts[0],
                                    'open_price': float(parts[1]) if parts[1] else 0,
                                    'close_price': float(parts[3]) if parts[3] else 0,
                                    'current_price': float(parts[3]) if parts[3] else 0,
                                    'high_price': float(parts[4]) if parts[4] else 0,
                                    'low_price': float(parts[5]) if parts[5] else 0,
                                    'volume': int(parts[8]) if parts[8] else 0,
                                    'amount': float(parts[9]) if parts[9] else 0,
                                    'change': float(parts[3]) - float(parts[2]) if parts[3] and parts[2] else 0,
                                    'pct_change': ((float(parts[3]) - float(parts[2])) / float(parts[2]) * 100) if parts[3] and parts[2] and float(parts[2]) > 0 else 0,
                                    'turnover_rate': 0,  # 需要额外计算
                                    'pe_ratio': 0,  # 需要额外获取
                                    'pb_ratio': 0,  # 需要额外获取
                                    'update_time': parts[31] if len(parts) > 31 else '',
                                }
                except Exception as e:
                    logger.error(f"解析行情数据失败: {e}")
                    continue
        
        return quotes
    
    async def get_hot_stocks(self, sort_type: str = "pct_change", limit: int = 100) -> List[Dict[str, Any]]:
        """获取热门股票

        Args:
            sort_type: 排序类型 ('pct_change' 涨幅榜, 'amount' 成交金额榜)
            limit: 返回数量 (涨幅榜最多100, 成交金额榜最多300)
        """
        try:
            # 根据排序类型设置不同的参数
            if sort_type == "pct_change":
                # 涨幅榜
                fid = 'f3'  # 按涨跌幅排序
                max_limit = min(limit, 100)  # 涨幅榜最多100
            else:
                # 成交金额榜
                fid = 'f6'  # 按成交额排序
                max_limit = min(limit, 300)  # 成交金额榜最多300

            url = "http://push2.eastmoney.com/api/qt/clist/get"
            params = {
                'pn': 1,
                'pz': max_limit,
                'po': 1,
                'np': 1,
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': 2,
                'invt': 2,
                'fid': fid,
                'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',
                'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152'
            }

            async with self.session.get(url, params=params) as response:
                data = await response.json()

                hot_stocks = []
                for item in data.get('data', {}).get('diff', []):
                    hot_stocks.append({
                        'code': item.get('f12', ''),
                        'name': item.get('f14', ''),
                        'market': 'SH' if item.get('f13', 0) == 1 else 'SZ',
                        'current_price': item.get('f2', 0) / 100 if item.get('f2') else 0,
                        'change': item.get('f4', 0) / 100 if item.get('f4') else 0,
                        'pct_change': item.get('f3', 0) / 100 if item.get('f3') else 0,
                        'volume': item.get('f5', 0),
                        'amount': item.get('f6', 0),
                        'turnover_rate': item.get('f8', 0) / 100 if item.get('f8') else 0,
                        'pe_ratio': item.get('f9', 0) / 100 if item.get('f9') else 0,
                        'pb_ratio': item.get('f23', 0) / 100 if item.get('f23') else 0,
                        'sort_type': sort_type,
                        'rank': len(hot_stocks) + 1
                    })

                return hot_stocks

        except Exception as e:
            logger.error(f"获取热门股票失败: {e}")
            return self._get_mock_hot_stocks(sort_type, limit)
    
    async def get_stock_detail(self, code: str) -> Dict[str, Any]:
        """获取股票详情"""
        try:
            # 获取实时行情
            quotes = await self.get_realtime_quotes([code])
            stock_data = quotes.get(code, {})
            
            if not stock_data:
                return self._get_mock_stock_detail(code)
            
            # 补充基本信息
            stock_data.update({
                'code': code,
                'market': 'SH' if code.startswith('6') else 'SZ',
                'industry': '待获取',
                'list_date': '待获取',
                'market_cap': 0,  # 需要额外计算
                'circulation_cap': 0,  # 需要额外获取
            })
            
            return stock_data
            
        except Exception as e:
            logger.error(f"获取股票详情失败: {e}")
            return self._get_mock_stock_detail(code)
    
    def _get_mock_stock_list(self) -> List[Dict[str, Any]]:
        """模拟股票列表数据"""
        return [
            {
                'code': '000001',
                'name': '平安银行',
                'market': 'SZ',
                'industry': '银行',
                'current_price': 12.50,
                'change': 0.15,
                'pct_change': 1.22,
                'volume': 1500000,
                'amount': 18750000,
                'turnover_rate': 0.85,
                'pe_ratio': 5.2,
                'pb_ratio': 0.8,
            },
            {
                'code': '000002',
                'name': '万科A',
                'market': 'SZ',
                'industry': '房地产',
                'current_price': 18.30,
                'change': -0.25,
                'pct_change': -1.35,
                'volume': 2100000,
                'amount': 38430000,
                'turnover_rate': 1.12,
                'pe_ratio': 8.5,
                'pb_ratio': 1.2,
            },
            {
                'code': '600000',
                'name': '浦发银行',
                'market': 'SH',
                'industry': '银行',
                'current_price': 8.95,
                'change': 0.08,
                'pct_change': 0.90,
                'volume': 1800000,
                'amount': 16110000,
                'turnover_rate': 0.65,
                'pe_ratio': 4.8,
                'pb_ratio': 0.6,
            }
        ]
    
    def _get_mock_hot_stocks(self) -> List[Dict[str, Any]]:
        """模拟热门股票数据"""
        return [
            {
                'code': '000001',
                'name': '平安银行',
                'market': 'SZ',
                'current_price': 12.50,
                'change': 0.15,
                'pct_change': 1.22,
                'volume': 1500000,
                'amount': 18750000,
                'turnover_rate': 0.85,
            }
        ]
    
    def _get_mock_stock_detail(self, code: str) -> Dict[str, Any]:
        """模拟股票详情数据"""
        return {
            'code': code,
            'name': f'股票{code}',
            'market': 'SH' if code.startswith('6') else 'SZ',
            'industry': '待获取',
            'current_price': 10.00,
            'change': 0.10,
            'pct_change': 1.00,
            'volume': 1000000,
            'amount': 10000000,
            'turnover_rate': 1.0,
            'pe_ratio': 15.0,
            'pb_ratio': 1.5,
        }
