"""
股票数据源提供者
支持多个数据源：新浪财经、腾讯财经、东方财富等
"""

import asyncio
import aiohttp
import json
import re
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
import logging

logger = logging.getLogger(__name__)

class StockDataProvider:
    """股票数据提供者"""
    
    def __init__(self):
        self.session = None
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://finance.sina.com.cn/',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            headers=self.headers,
            timeout=aiohttp.ClientTimeout(total=10)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_stock_list(self, market: str = "all") -> List[Dict[str, Any]]:
        """获取股票列表"""
        try:
            # 使用新浪财经API获取股票列表
            if market.upper() == "SH" or market == "all":
                sh_stocks = await self._get_sh_stocks()
            else:
                sh_stocks = []
                
            if market.upper() == "SZ" or market == "all":
                sz_stocks = await self._get_sz_stocks()
            else:
                sz_stocks = []
            
            all_stocks = sh_stocks + sz_stocks
            
            # 获取实时行情数据
            if all_stocks:
                stock_codes = [stock['code'] for stock in all_stocks[:100]]  # 限制数量避免请求过大
                quotes = await self.get_realtime_quotes(stock_codes)
                
                # 合并数据
                for stock in all_stocks:
                    quote = quotes.get(stock['code'], {})
                    stock.update(quote)
            
            return all_stocks
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return self._get_mock_stock_list()
    
    async def _get_sh_stocks(self) -> List[Dict[str, Any]]:
        """获取上海股票列表"""
        try:
            # 上证主板股票
            url = "http://query.sse.com.cn/security/stock/getStockListData2.do?&jsonCallBack=jsonpCallback&isPagination=true&stockCode=&csrcCode=&areaName=&stockType=1"
            
            async with self.session.get(url) as response:
                text = await response.text()
                # 解析JSONP响应
                json_str = re.search(r'jsonpCallback\((.*)\)', text)
                if json_str:
                    data = json.loads(json_str.group(1))
                    stocks = []
                    for item in data.get('pageHelp', {}).get('data', []):
                        stocks.append({
                            'code': item.get('SECURITY_CODE_A', ''),
                            'name': item.get('SECURITY_ABBR_A', ''),
                            'market': 'SH',
                            'industry': item.get('CSRC_CODE', ''),
                            'list_date': item.get('LIST_DATE', ''),
                        })
                    return stocks[:50]  # 限制数量
        except Exception as e:
            logger.error(f"获取上海股票列表失败: {e}")
        
        return []
    
    async def _get_sz_stocks(self) -> List[Dict[str, Any]]:
        """获取深圳股票列表"""
        try:
            # 深证主板股票
            url = "http://www.szse.cn/api/report/ShowReport?SHOWTYPE=JSON&CATALOGID=1110&TABKEY=tab1"
            
            async with self.session.get(url) as response:
                data = await response.json()
                stocks = []
                for item in data.get('data', []):
                    stocks.append({
                        'code': item.get('zqdm', ''),
                        'name': item.get('zqjc', ''),
                        'market': 'SZ',
                        'industry': item.get('sshy', ''),
                        'list_date': item.get('ssrq', ''),
                    })
                return stocks[:50]  # 限制数量
        except Exception as e:
            logger.error(f"获取深圳股票列表失败: {e}")
        
        return []
    
    async def get_realtime_quotes(self, stock_codes: List[str]) -> Dict[str, Dict[str, Any]]:
        """获取实时行情数据"""
        try:
            # 构建新浪财经实时行情API请求
            formatted_codes = []
            for code in stock_codes:
                if code.startswith('6'):
                    formatted_codes.append(f'sh{code}')
                else:
                    formatted_codes.append(f'sz{code}')
            
            # 分批请求，每次最多50只股票
            all_quotes = {}
            batch_size = 50
            
            for i in range(0, len(formatted_codes), batch_size):
                batch_codes = formatted_codes[i:i + batch_size]
                codes_str = ','.join(batch_codes)
                
                url = f"http://hq.sinajs.cn/list={codes_str}"
                
                async with self.session.get(url) as response:
                    text = await response.text(encoding='gbk')
                    quotes = self._parse_sina_quotes(text)
                    all_quotes.update(quotes)
            
            return all_quotes
            
        except Exception as e:
            logger.error(f"获取实时行情失败: {e}")
            return {}
    
    def _parse_sina_quotes(self, text: str) -> Dict[str, Dict[str, Any]]:
        """解析新浪财经行情数据"""
        quotes = {}
        
        for line in text.strip().split('\n'):
            if 'var hq_str_' in line:
                try:
                    # 提取股票代码和数据
                    code_match = re.search(r'hq_str_([a-z]{2}\d{6})', line)
                    data_match = re.search(r'"([^"]*)"', line)
                    
                    if code_match and data_match:
                        full_code = code_match.group(1)
                        code = full_code[2:]  # 去掉sh/sz前缀
                        data_str = data_match.group(1)
                        
                        if data_str:
                            parts = data_str.split(',')
                            if len(parts) >= 32:
                                quotes[code] = {
                                    'name': parts[0],
                                    'open_price': float(parts[1]) if parts[1] else 0,
                                    'close_price': float(parts[3]) if parts[3] else 0,
                                    'current_price': float(parts[3]) if parts[3] else 0,
                                    'high_price': float(parts[4]) if parts[4] else 0,
                                    'low_price': float(parts[5]) if parts[5] else 0,
                                    'volume': int(parts[8]) if parts[8] else 0,
                                    'amount': float(parts[9]) if parts[9] else 0,
                                    'change': float(parts[3]) - float(parts[2]) if parts[3] and parts[2] else 0,
                                    'pct_change': ((float(parts[3]) - float(parts[2])) / float(parts[2]) * 100) if parts[3] and parts[2] and float(parts[2]) > 0 else 0,
                                    'turnover_rate': 0,  # 需要额外计算
                                    'pe_ratio': 0,  # 需要额外获取
                                    'pb_ratio': 0,  # 需要额外获取
                                    'update_time': parts[31] if len(parts) > 31 else '',
                                }
                except Exception as e:
                    logger.error(f"解析行情数据失败: {e}")
                    continue
        
        return quotes
    
    async def get_hot_stocks(self, sort_type: str = "pct_change", limit: int = 100) -> List[Dict[str, Any]]:
        """获取热门股票

        Args:
            sort_type: 排序类型 ('pct_change' 涨幅榜, 'amount' 成交金额榜)
            limit: 返回数量 (涨幅榜最多100, 成交金额榜最多300)
        """
        try:
            # 根据排序类型设置不同的参数
            if sort_type == "pct_change":
                # 涨幅榜
                fid = 'f3'  # 按涨跌幅排序
                max_limit = min(limit, 100)  # 涨幅榜最多100
            else:
                # 成交金额榜
                fid = 'f6'  # 按成交额排序
                max_limit = min(limit, 300)  # 成交金额榜最多300

            url = "http://push2.eastmoney.com/api/qt/clist/get"
            params = {
                'pn': 1,
                'pz': max_limit,
                'po': 1,
                'np': 1,
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': 2,
                'invt': 2,
                'fid': fid,
                'fs': 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23',
                'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152'
            }

            async with self.session.get(url, params=params) as response:
                data = await response.json()

                hot_stocks = []
                for item in data.get('data', {}).get('diff', []):
                    hot_stocks.append({
                        'code': item.get('f12', ''),
                        'name': item.get('f14', ''),
                        'market': 'SH' if item.get('f13', 0) == 1 else 'SZ',
                        'current_price': item.get('f2', 0) / 100 if item.get('f2') else 0,
                        'change': item.get('f4', 0) / 100 if item.get('f4') else 0,
                        'pct_change': item.get('f3', 0) / 100 if item.get('f3') else 0,
                        'volume': item.get('f5', 0),
                        'amount': item.get('f6', 0),
                        'turnover_rate': item.get('f8', 0) / 100 if item.get('f8') else 0,
                        'pe_ratio': item.get('f9', 0) / 100 if item.get('f9') else 0,
                        'pb_ratio': item.get('f23', 0) / 100 if item.get('f23') else 0,
                        'sort_type': sort_type,
                        'rank': len(hot_stocks) + 1
                    })

                return hot_stocks

        except Exception as e:
            logger.error(f"获取热门股票失败: {e}")
            return self._get_mock_hot_stocks(sort_type, limit)
    
    async def get_stock_detail(self, code: str) -> Dict[str, Any]:
        """获取股票详情（东方财富数据源）"""
        try:
            # 使用东方财富API获取股票详情
            secid = f"1.{code}" if code.startswith('6') else f"0.{code}"
            url = "http://push2.eastmoney.com/api/qt/stock/get"
            params = {
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'invt': 2,
                'fltt': 1,
                'fields': 'f43,f57,f58,f169,f170,f46,f44,f51,f168,f47,f164,f163,f116,f60,f45,f52,f50,f48,f167,f117,f71,f161,f49,f530,f135,f136,f137,f138,f139,f141,f142,f144,f145,f147,f148,f140,f143,f146,f149,f55,f62,f162,f92,f173,f104,f105,f84,f85,f183,f184,f185,f186,f187,f188,f189,f190,f191,f192,f107,f111,f86,f177,f78,f110,f262,f263,f264,f267,f268,f250,f251,f252,f253,f254,f255,f256,f257,f258,f266,f269,f270,f271,f273,f274,f275,f127,f199,f128,f193,f196,f194,f195,f197,f80,f280,f281,f282,f284,f285,f286,f287,f292',
                'secid': secid
            }

            async with self.session.get(url, params=params) as response:
                data = await response.json()

                if not data.get('data'):
                    return self._get_mock_stock_detail(code)

                item = data['data']

                # 获取概念板块信息
                concept_sectors = await self._get_stock_concept_sectors(code)

                stock_detail = {
                    'code': code,
                    'name': item.get('f58', ''),
                    'market': 'SH' if code.startswith('6') else 'SZ',
                    'current_price': item.get('f43', 0) / 100 if item.get('f43') else 0,
                    'change': item.get('f169', 0) / 100 if item.get('f169') else 0,
                    'pct_change': item.get('f170', 0) / 100 if item.get('f170') else 0,
                    'open_price': item.get('f46', 0) / 100 if item.get('f46') else 0,
                    'high_price': item.get('f44', 0) / 100 if item.get('f44') else 0,
                    'low_price': item.get('f45', 0) / 100 if item.get('f45') else 0,
                    'volume': item.get('f47', 0),
                    'amount': item.get('f48', 0),
                    'turnover_rate': item.get('f168', 0) / 100 if item.get('f168') else 0,
                    'pe_ratio': item.get('f162', 0) / 100 if item.get('f162') else 0,
                    'pb_ratio': item.get('f173', 0) / 100 if item.get('f173') else 0,
                    'market_cap': item.get('f116', 0),
                    'circulation_cap': item.get('f117', 0),
                    'concept_sectors': concept_sectors,  # 概念板块信息
                    'update_time': datetime.now().isoformat()
                }

                return stock_detail

        except Exception as e:
            logger.error(f"获取股票详情失败: {e}")
            return self._get_mock_stock_detail(code)

    async def _get_stock_concept_sectors(self, code: str) -> List[Dict[str, Any]]:
        """获取股票所属概念板块"""
        try:
            # 使用东方财富API获取股票概念板块
            secid = f"1.{code}" if code.startswith('6') else f"0.{code}"
            url = "http://push2.eastmoney.com/api/qt/slist/get"
            params = {
                'spt': 3,
                'secid': secid,
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'fields': 'f1,f2,f3,f4,f12,f13,f14'
            }

            async with self.session.get(url, params=params) as response:
                data = await response.json()

                concept_sectors = []
                for item in data.get('data', {}).get('diff', []):
                    concept_sectors.append({
                        'sector_code': item.get('f12', ''),
                        'sector_name': item.get('f14', ''),
                        'pct_change': item.get('f3', 0) / 100 if item.get('f3') else 0,
                        'rank': len(concept_sectors) + 1
                    })

                return concept_sectors[:10]  # 返回前10个概念板块
        except Exception as e:
            logger.error(f"获取股票概念板块失败: {e}")
            return []

    async def get_research_ratings(self, code: str) -> Dict[str, Any]:
        """获取股票研报评级

        从东方财富网爬取股票研报评级数据
        """
        try:
            # 尝试从东方财富研报页面爬取数据
            # 注意：这里需要模拟浏览器请求，因为页面使用JavaScript加载数据
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://data.eastmoney.com/report/stock.jshtml',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
            }

            # 使用有效的东方财富研报API
            api_url = f"https://reportapi.eastmoney.com/report/list?cb=datatable&pageSize=50&pageNo=1&code={code}&industryCode=*&pageSize=50&pageNo=1&reportType=1&fields=&qType=0&orgCode=&ratingChange=&beginTime=&endTime=&_=1640000000000"

            research_reports = []
            success = False

            try:
                async with self.session.get(api_url, headers=headers, timeout=10) as response:
                    if response.status == 200:
                        text = await response.text()
                        # 处理JSONP格式
                        if text.startswith('datatable(') and text.endswith(')'):
                            json_str = text[10:-1]  # 移除 datatable( 和 )
                            data = json.loads(json_str)

                            # 解析东方财富研报数据
                            if self._parse_eastmoney_research_data(data, research_reports, code):
                                success = True

            except Exception as api_error:
                logger.warning(f"API {api_url} 请求失败: {api_error}")

            if success and research_reports:
                # 统计评级分布
                rating_distribution = {}
                for report in research_reports:
                    rating = report.get("rating", "")
                    if rating:
                        rating_distribution[rating] = rating_distribution.get(rating, 0) + 1

                # 计算平均目标价
                valid_prices = [r["target_price"] for r in research_reports if r.get("target_price") and r["target_price"] > 0]
                avg_target_price = sum(valid_prices) / len(valid_prices) if valid_prices else 0

                return {
                    "stock_code": code,
                    "research_reports": research_reports[:5],  # 只返回前5条
                    "rating_summary": {
                        "total_reports": len(research_reports),
                        "rating_distribution": rating_distribution,
                        "avg_target_price": round(avg_target_price, 2),
                        "consensus_rating": max(rating_distribution.items(), key=lambda x: x[1])[0] if rating_distribution else "中性"
                    },
                    "analysis_time": datetime.now().isoformat(),
                    "data_source": "东方财富网实时数据"
                }
            else:
                # 如果所有API都失败，返回带有错误信息的模拟数据
                mock_data = self._get_mock_research_ratings(code)
                mock_data["data_source"] = "模拟数据（网络获取失败）"
                mock_data["error_message"] = "无法从东方财富网获取实时研报数据，显示模拟数据"
                return mock_data

        except Exception as e:
            logger.error(f"获取研报评级失败: {e}")
            mock_data = self._get_mock_research_ratings(code)
            mock_data["data_source"] = "模拟数据（请求异常）"
            mock_data["error_message"] = f"请求异常: {str(e)}"
            return mock_data

    def _parse_eastmoney_research_data(self, data: Dict, research_reports: List, code: str) -> bool:
        """解析东方财富研报数据"""
        try:
            # 东方财富API返回格式: {"hits": 513, "size": 50, "data": [...]}
            if isinstance(data, dict) and 'data' in data:
                reports = data['data']
                if isinstance(reports, list):
                    for report in reports[:5]:  # 只取前5条
                        # 处理评级映射
                        rating_name = report.get('emRatingName', '')
                        if not rating_name:
                            rating_name = report.get('sRatingName', '')

                        # 处理目标价
                        target_price = 0
                        if report.get('indvAimPriceT'):
                            try:
                                target_price = float(report.get('indvAimPriceT', 0))
                            except (ValueError, TypeError):
                                target_price = 0

                        # 处理发布日期
                        publish_date = report.get('publishDate', '')
                        if publish_date:
                            # 格式: "2025-05-21 00:00:00.000" -> "2025-05-21"
                            publish_date = publish_date.split(' ')[0]

                        research_reports.append({
                            'institution': report.get('orgSName', report.get('orgName', '')),
                            'analyst': report.get('researcher', ''),
                            'rating': rating_name,
                            'target_price': target_price,
                            'publish_date': publish_date,
                            'report_title': report.get('title', ''),
                            'key_points': [f"研报来源: {report.get('orgSName', '')}", f"发布日期: {publish_date}"]
                        })
                    return len(research_reports) > 0

            return False

        except Exception as e:
            logger.error(f"解析东方财富研报数据失败: {e}")
            return False

    def _get_mock_research_ratings(self, code: str) -> Dict[str, Any]:
        """获取模拟研报评级数据"""
        import hashlib

        # 使用股票代码作为种子，确保同一股票返回相同数据
        seed = int(hashlib.md5(code.encode()).hexdigest()[:8], 16)

        # 模拟研报评级数据
        ratings = ["买入", "增持", "中性", "减持", "卖出"]
        institutions = ["中信证券", "华泰证券", "国泰君安", "海通证券", "广发证券", "招商证券", "中金公司", "申万宏源"]

        # 为每只股票生成固定的研报数据
        research_reports = []
        for i in range(5):
            # 使用确定性算法生成固定数据
            inst_index = (seed + i * 7) % len(institutions)
            rating_index = (seed + i * 3) % len(ratings)

            # 生成固定的目标价格（基于股票代码）
            base_price = (seed % 40) + 10  # 10-50之间
            price_variation = ((seed + i * 5) % 20 - 10) * 0.1  # -1到+1的变化
            target_price = round(base_price + price_variation, 2)

            # 生成固定的发布日期
            days_ago = (seed + i * 11) % 30 + 1  # 1-30天前
            date = datetime.now() - timedelta(days=days_ago)

            research_reports.append({
                "institution": institutions[inst_index],
                "analyst": f"分析师{chr(65 + i)}",  # 分析师A, B, C, D, E
                "rating": ratings[rating_index],
                "target_price": target_price,
                "publish_date": date.strftime("%Y-%m-%d"),
                "report_title": f"{code}深度研究报告",
                "key_points": [
                    "公司业绩稳健增长，盈利能力持续提升",
                    "行业景气度高，公司市场份额有望扩大",
                    "技术创新能力强，产品竞争优势明显"
                ]
            })

        # 统计评级分布
        rating_distribution = {}
        for report in research_reports:
            rating = report["rating"]
            rating_distribution[rating] = rating_distribution.get(rating, 0) + 1

        return {
            "stock_code": code,
            "research_reports": research_reports,
            "rating_summary": {
                "total_reports": len(research_reports),
                "rating_distribution": rating_distribution,
                "avg_target_price": round(sum(r["target_price"] for r in research_reports) / len(research_reports), 2),
                "consensus_rating": max(rating_distribution.items(), key=lambda x: x[1])[0] if rating_distribution else "中性"
            },
            "analysis_time": datetime.now().isoformat()
        }

    def _get_mock_concept_sectors(self, code: str) -> List[Dict[str, Any]]:
        """模拟概念板块数据"""
        if code.startswith('30'):  # 创业板
            return [
                {'sector_code': 'BK0464', 'sector_name': '人工智能', 'pct_change': 3.25, 'rank': 1},
                {'sector_code': 'BK0493', 'sector_name': '新能源汽车', 'pct_change': 2.85, 'rank': 3},
                {'sector_code': 'BK0477', 'sector_name': '芯片概念', 'pct_change': 1.95, 'rank': 8}
            ]
        elif code.startswith('00'):  # 深市主板
            return [
                {'sector_code': 'BK0475', 'sector_name': '银行', 'pct_change': 1.25, 'rank': 15},
                {'sector_code': 'BK0451', 'sector_name': '白酒', 'pct_change': 0.85, 'rank': 25}
            ]
        else:  # 沪市
            return [
                {'sector_code': 'BK0478', 'sector_name': '有色金属', 'pct_change': 2.15, 'rank': 5},
                {'sector_code': 'BK0473', 'sector_name': '证券', 'pct_change': 1.65, 'rank': 12}
            ]
    
    def _get_mock_stock_list(self) -> List[Dict[str, Any]]:
        """模拟股票列表数据"""
        return [
            {
                'code': '000001',
                'name': '平安银行',
                'market': 'SZ',
                'industry': '银行',
                'current_price': 12.50,
                'change': 0.15,
                'pct_change': 1.22,
                'volume': 1500000,
                'amount': 18750000,
                'turnover_rate': 0.85,
                'pe_ratio': 5.2,
                'pb_ratio': 0.8,
            },
            {
                'code': '000002',
                'name': '万科A',
                'market': 'SZ',
                'industry': '房地产',
                'current_price': 18.30,
                'change': -0.25,
                'pct_change': -1.35,
                'volume': 2100000,
                'amount': 38430000,
                'turnover_rate': 1.12,
                'pe_ratio': 8.5,
                'pb_ratio': 1.2,
            },
            {
                'code': '600000',
                'name': '浦发银行',
                'market': 'SH',
                'industry': '银行',
                'current_price': 8.95,
                'change': 0.08,
                'pct_change': 0.90,
                'volume': 1800000,
                'amount': 16110000,
                'turnover_rate': 0.65,
                'pe_ratio': 4.8,
                'pb_ratio': 0.6,
            }
        ]
    
    def _get_mock_hot_stocks(self, sort_type: str = "pct_change", limit: int = 100) -> List[Dict[str, Any]]:
        """模拟热门股票数据"""
        if sort_type == "pct_change":
            # 模拟涨幅榜数据
            return [
                {
                    'code': '300750',
                    'name': '宁德时代',
                    'market': 'SZ',
                    'current_price': 185.50,
                    'change': 15.20,
                    'pct_change': 8.92,
                    'volume': 25000000,
                    'amount': 4625000000,
                    'turnover_rate': 2.15,
                    'rank': 1,
                    'sort_type': sort_type
                },
                {
                    'code': '002230',
                    'name': '科大讯飞',
                    'market': 'SZ',
                    'current_price': 68.80,
                    'change': 5.60,
                    'pct_change': 8.86,
                    'volume': 18000000,
                    'amount': 1238400000,
                    'turnover_rate': 1.85,
                    'rank': 2,
                    'sort_type': sort_type
                }
            ]
        else:
            # 模拟成交金额榜数据
            return [
                {
                    'code': '000858',
                    'name': '五粮液',
                    'market': 'SZ',
                    'current_price': 158.30,
                    'change': 2.80,
                    'pct_change': 1.80,
                    'volume': 35000000,
                    'amount': 5540500000,
                    'turnover_rate': 1.25,
                    'rank': 1,
                    'sort_type': sort_type
                },
                {
                    'code': '600519',
                    'name': '贵州茅台',
                    'market': 'SH',
                    'current_price': 1680.00,
                    'change': 25.00,
                    'pct_change': 1.51,
                    'volume': 3200000,
                    'amount': 5376000000,
                    'turnover_rate': 0.85,
                    'rank': 2,
                    'sort_type': sort_type
                }
            ]
    
    def _get_mock_stock_detail(self, code: str) -> Dict[str, Any]:
        """模拟股票详情数据"""
        return {
            'code': code,
            'name': f'股票{code}',
            'market': 'SH' if code.startswith('6') else 'SZ',
            'industry': '待获取',
            'current_price': 10.00,
            'change': 0.10,
            'pct_change': 1.00,
            'volume': 1000000,
            'amount': 10000000,
            'turnover_rate': 1.0,
            'pe_ratio': 15.0,
            'pb_ratio': 1.5,
        }
