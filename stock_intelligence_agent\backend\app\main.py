"""
FastAPI 应用主入口
"""

import logging
import time
from datetime import datetime
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from app.core.config import settings, LOGGING_CONFIG
from app.core.database import init_db, close_db, check_db_health
from app.api.v1.api import api_router


# 配置日志
logging.config.dictConfig(LOGGING_CONFIG)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 Starting Stock Intelligence Agent...")

    try:
        # 初始化数据库连接
        await init_db()
        logger.info("✅ Database connections initialized")

        # 检查数据库健康状态
        health_status = await check_db_health()
        logger.info(f"📊 Database health status: {health_status}")

        yield

    except Exception as e:
        logger.error(f"❌ Startup failed: {e}")
        raise
    finally:
        # 关闭时执行
        logger.info("🛑 Shutting down Stock Intelligence Agent...")
        await close_db()
        logger.info("✅ Database connections closed")


# 创建 FastAPI 应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="智能股票资讯系统 - 提供实时行情、智能分析和资讯聚合服务",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
    lifespan=lifespan,
)

# 添加 CORS 中间件
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# 添加信任主机中间件
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", settings.SERVER_NAME],
)


# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    logger.error(f"Global exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": "An unexpected error occurred",
            "detail": str(exc) if settings.DEBUG else None,
        },
    )


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        db_health = await check_db_health()

        # 计算整体健康状态
        healthy_services = sum(db_health.values())
        total_services = len(db_health)
        health_percentage = (healthy_services / total_services) * 100

        status = "healthy" if health_percentage == 100 else "degraded" if health_percentage >= 50 else "unhealthy"

        return {
            "status": status,
            "timestamp": datetime.utcnow().isoformat(),
            "version": settings.VERSION,
            "services": db_health,
            "health_percentage": health_percentage,
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e),
            },
        )


# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Welcome to Stock Intelligence Agent API",
        "version": settings.VERSION,
        "docs": f"{settings.API_V1_STR}/docs",
        "health": "/health",
    }


# 包含 API 路由
app.include_router(api_router, prefix=settings.API_V1_STR)


# 中间件：请求日志
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录请求日志"""
    start_time = time.time()

    # 记录请求信息
    logger.info(f"📥 {request.method} {request.url}")

    # 处理请求
    response = await call_next(request)

    # 计算处理时间
    process_time = time.time() - start_time

    # 记录响应信息
    logger.info(f"📤 {request.method} {request.url} - {response.status_code} - {process_time:.3f}s")

    # 添加处理时间到响应头
    response.headers["X-Process-Time"] = str(process_time)

    return response


# 中间件：限流 (简单实现)
@app.middleware("http")
async def rate_limit_middleware(request: Request, call_next):
    """简单的限流中间件"""
    # 这里可以实现更复杂的限流逻辑
    # 目前只是一个占位符
    response = await call_next(request)
    return response


if __name__ == "__main__":
    # 开发环境直接运行
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True,
    )
