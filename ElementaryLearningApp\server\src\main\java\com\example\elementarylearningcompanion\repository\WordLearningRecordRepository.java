package com.example.elementarylearningcompanion.repository;

import com.example.elementarylearningcompanion.model.WordLearningRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface WordLearningRecordRepository extends JpaRepository<WordLearningRecord, Long> {
    
    List<WordLearningRecord> findByStudentIdAndWordId(Long studentId, Long wordId);
    
    Optional<WordLearningRecord> findByStudentIdAndWordIdAndLearningType(Long studentId, Long wordId, String learningType);
    
    @Query("SELECT AVG(wlr.masteryLevel) FROM WordLearningRecord wlr WHERE wlr.studentId = :studentId")
    Double getAverageMasteryLevel(@Param("studentId") Long studentId);
    
    @Query("SELECT wlr FROM WordLearningRecord wlr WHERE wlr.studentId = :studentId " +
           "AND wlr.masteryLevel < :threshold ORDER BY wlr.masteryLevel ASC")
    List<WordLearningRecord> findWordsNeedingReview(@Param("studentId") Long studentId,
                                                   @Param("threshold") Integer threshold);
    
    @Query("SELECT COUNT(DISTINCT wlr.wordId) FROM WordLearningRecord wlr WHERE wlr.studentId = :studentId " +
           "AND wlr.masteryLevel >= :threshold")
    Long countMasteredWords(@Param("studentId") Long studentId, @Param("threshold") Integer threshold);
    
    @Query("SELECT wlr.wordId, MAX(wlr.masteryLevel) as maxMastery FROM WordLearningRecord wlr " +
           "WHERE wlr.studentId = :studentId GROUP BY wlr.wordId")
    List<Object[]> getWordMasteryLevels(@Param("studentId") Long studentId);
}
