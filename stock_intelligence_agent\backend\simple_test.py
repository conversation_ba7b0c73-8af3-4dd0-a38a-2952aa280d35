"""
简单的后端测试脚本
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# 创建简单的FastAPI应用
app = FastAPI(
    title="股票智能体 API",
    description="股票分析和资讯平台 API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "股票智能体 API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "stock-intelligence-agent",
        "timestamp": "2024-01-01T00:00:00Z"
    }

@app.get("/api/v1/stocks")
async def get_stocks():
    """获取股票列表（模拟数据）"""
    return {
        "data": [
            {
                "code": "000001",
                "name": "平安银行",
                "market": "SZ",
                "industry": "银行",
                "close_price": 12.50,
                "change": 0.15,
                "pct_change": 1.22,
                "volume": 1500000,
                "amount": 18750000,
                "turnover_rate": 0.85,
                "pe_ratio": 5.2,
                "pb_ratio": 0.8
            },
            {
                "code": "000002",
                "name": "万科A",
                "market": "SZ", 
                "industry": "房地产",
                "close_price": 18.30,
                "change": -0.25,
                "pct_change": -1.35,
                "volume": 2100000,
                "amount": 38430000,
                "turnover_rate": 1.12,
                "pe_ratio": 8.5,
                "pb_ratio": 1.2
            }
        ],
        "total": 2,
        "skip": 0,
        "limit": 10
    }

@app.get("/api/v1/stocks/hot")
async def get_hot_stocks():
    """获取热门股票（模拟数据）"""
    return [
        {
            "code": "000001",
            "name": "平安银行",
            "market": "SZ",
            "industry": "银行",
            "close_price": 12.50,
            "change": 0.15,
            "pct_change": 1.22,
            "volume": 1500000,
            "amount": 18750000,
            "turnover_rate": 0.85
        }
    ]

@app.get("/api/v1/market/overview")
async def get_market_overview():
    """获取市场概览（模拟数据）"""
    return {
        "hot_stocks": [
            {
                "code": "000001",
                "name": "平安银行",
                "market": "SZ",
                "close_price": 12.50,
                "pct_change": 1.22
            }
        ],
        "statistics": {
            "total_stocks": 5000,
            "market_distribution": {
                "SH": 2000,
                "SZ": 2500,
                "BJ": 500
            },
            "top_industries": {
                "银行": 50,
                "房地产": 45,
                "医药": 40
            },
            "last_updated": "2024-01-01T00:00:00Z"
        },
        "market_indices": [
            {
                "code": "000001",
                "name": "上证指数",
                "current": 3200.50,
                "change": 15.20,
                "pct_change": 0.48
            }
        ],
        "market_status": {
            "is_trading_day": True,
            "is_trading_time": False,
            "session": "closed",
            "current_time": "2024-01-01T16:00:00Z"
        }
    }

@app.get("/api/v1/news")
async def get_news():
    """获取新闻列表（模拟数据）"""
    return {
        "data": [
            {
                "id": "1",
                "title": "A股市场今日表现强劲",
                "summary": "今日A股三大指数全线上涨，银行板块领涨",
                "source": "财经新闻",
                "published_at": "2024-01-01T10:00:00Z",
                "tags": ["A股", "银行"],
                "importance": 8,
                "view_count": 1500,
                "like_count": 120,
                "comment_count": 45
            }
        ],
        "total": 1,
        "skip": 0,
        "limit": 10
    }

if __name__ == "__main__":
    print("🚀 启动简化版股票智能体后端服务...")
    print("📍 服务地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    print("🔍 健康检查: http://localhost:8000/health")
    
    uvicorn.run(
        "simple_test:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
