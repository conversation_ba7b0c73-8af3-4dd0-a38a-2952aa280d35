"""
简单的后端测试脚本
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# 创建简单的FastAPI应用
app = FastAPI(
    title="股票智能体 API",
    description="股票分析和资讯平台 API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "股票智能体 API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "stock-intelligence-agent",
        "timestamp": "2024-01-01T00:00:00Z"
    }

@app.get("/api/v1/stocks")
async def get_stocks(
    limit: int = 100,
    market: str = "all",
    sort_by: str = "pct_change",
    order: str = "desc"
):
    """获取股票列表（真实数据）

    Args:
        limit: 返回数量 (涨幅榜最多100, 成交金额榜最多300)
        market: 市场筛选 (SH/SZ/all)
        sort_by: 排序字段 (pct_change涨幅/amount成交额)
        order: 排序方向 (asc/desc)
    """
    try:
        # 导入数据源
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))

        from app.data_sources.stock_data import StockDataProvider

        # 根据排序类型设置限制
        if sort_by == "pct_change":
            max_limit = min(limit, 100)  # 涨幅榜最多100
        elif sort_by == "amount":
            max_limit = min(limit, 300)  # 成交金额榜最多300
        else:
            max_limit = min(limit, 100)

        async with StockDataProvider() as provider:
            if sort_by in ["pct_change", "amount"]:
                # 使用热门股票接口获取排行榜数据
                stocks = await provider.get_hot_stocks(sort_type=sort_by, limit=max_limit)
            else:
                # 使用普通股票列表
                stocks = await provider.get_stock_list(market=market)
                # 排序
                if sort_by == "pct_change":
                    stocks.sort(key=lambda x: x.get('pct_change', 0), reverse=(order == "desc"))
                elif sort_by == "amount":
                    stocks.sort(key=lambda x: x.get('amount', 0), reverse=(order == "desc"))
                stocks = stocks[:max_limit]

            return {
                "data": stocks,
                "total": len(stocks),
                "skip": 0,
                "limit": max_limit,
                "sort_by": sort_by,
                "max_limit": max_limit
            }
    except Exception as e:
        # 如果真实数据获取失败，返回模拟数据
        return {
            "data": [
                {
                    "code": "000001",
                    "name": "平安银行",
                    "market": "SZ",
                    "industry": "银行",
                    "current_price": 12.50,
                    "change": 0.15,
                    "pct_change": 1.22,
                    "volume": 1500000,
                    "amount": 18750000,
                    "turnover_rate": 0.85,
                    "pe_ratio": 5.2,
                    "pb_ratio": 0.8,
                    "rank": 1
                }
            ],
            "total": 1,
            "skip": 0,
            "limit": limit
        }

@app.get("/api/v1/stocks/hot")
async def get_hot_stocks(
    sort_type: str = "pct_change",
    limit: int = 100
):
    """获取热门股票（真实数据）

    Args:
        sort_type: 排序类型 ('pct_change' 涨幅榜, 'amount' 成交金额榜)
        limit: 返回数量 (涨幅榜最多100, 成交金额榜最多300)
    """
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))

        from app.data_sources.stock_data import StockDataProvider

        # 根据排序类型设置限制
        if sort_type == "pct_change":
            max_limit = min(limit, 100)  # 涨幅榜最多100
        elif sort_type == "amount":
            max_limit = min(limit, 300)  # 成交金额榜最多300
        else:
            max_limit = min(limit, 100)

        async with StockDataProvider() as provider:
            hot_stocks = await provider.get_hot_stocks(sort_type=sort_type, limit=max_limit)
            return {
                "data": hot_stocks,
                "total": len(hot_stocks),
                "sort_type": sort_type,
                "limit": max_limit
            }
    except Exception:
        # 如果真实数据获取失败，返回模拟数据
        return {
            "data": [
                {
                    "code": "000001",
                    "name": "平安银行",
                    "market": "SZ",
                    "industry": "银行",
                    "current_price": 12.50,
                    "change": 0.15,
                    "pct_change": 1.22,
                    "volume": 1500000,
                    "amount": 18750000,
                    "turnover_rate": 0.85,
                    "rank": 1
                }
            ],
            "total": 1,
            "sort_type": sort_type,
            "limit": limit
        }

@app.get("/api/v1/stocks/{stock_code}")
async def get_stock_detail(stock_code: str):
    """获取股票详情（真实数据）"""
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))

        from app.data_sources.stock_data import StockDataProvider

        async with StockDataProvider() as provider:
            stock_detail = await provider.get_stock_detail(stock_code)
            return stock_detail
    except Exception:
        # 如果真实数据获取失败，返回模拟数据
        return {
            "code": stock_code,
            "name": f"股票{stock_code}",
            "market": "SH" if stock_code.startswith('6') else "SZ",
            "industry": "待获取",
            "current_price": 10.00,
            "change": 0.10,
            "pct_change": 1.00,
            "volume": 1000000,
            "amount": 10000000,
            "turnover_rate": 1.0,
            "pe_ratio": 15.0,
            "pb_ratio": 1.5,
            "open_price": 9.95,
            "high_price": 10.20,
            "low_price": 9.85,
            "close_price": 10.00
        }

@app.get("/api/v1/analysis/technical/{stock_code}")
async def get_technical_analysis(stock_code: str):
    """获取技术分析（模拟数据）"""
    return {
        "stock_code": stock_code,
        "indicators": {
            "ma": {
                "ma5": 12.45,
                "ma10": 12.30,
                "ma20": 12.10,
                "ma60": 11.95
            },
            "macd": {
                "macd": 0.15,
                "signal": 0.12,
                "histogram": 0.03
            },
            "rsi": 65.5,
            "kdj": {
                "k": 75.2,
                "d": 68.9,
                "j": 88.8
            },
            "boll": {
                "upper": 13.20,
                "middle": 12.50,
                "lower": 11.80
            }
        },
        "signals": {
            "overall": "BUY",
            "confidence": 0.75,
            "details": {
                "ma_signal": "BUY",
                "macd_signal": "BUY",
                "rsi_signal": "HOLD"
            }
        },
        "analysis_time": "2024-01-01T10:00:00Z"
    }

@app.get("/api/v1/analysis/fundamental/{stock_code}")
async def get_fundamental_analysis(stock_code: str):
    """获取基本面分析（模拟数据）"""
    return {
        "stock_code": stock_code,
        "financial_ratios": {
            "pe_ratio": 15.2,
            "pb_ratio": 1.8,
            "roe": 12.5,
            "roa": 8.3,
            "debt_ratio": 0.45,
            "current_ratio": 1.25
        },
        "growth_metrics": {
            "revenue_growth": 8.5,
            "profit_growth": 12.3,
            "eps_growth": 10.8
        },
        "valuation": {
            "fair_value": 13.50,
            "current_price": 12.50,
            "upside_potential": 8.0,
            "rating": "BUY"
        },
        "analysis_time": "2024-01-01T10:00:00Z"
    }

@app.get("/api/v1/analysis/risk/{stock_code}")
async def get_risk_assessment(stock_code: str):
    """获取风险评估（模拟数据）"""
    return {
        "stock_code": stock_code,
        "risk_metrics": {
            "var_95": -2.5,
            "var_99": -4.2,
            "sharpe_ratio": 1.25,
            "max_drawdown": -15.8,
            "volatility": 18.5,
            "beta": 1.15
        },
        "risk_level": "MEDIUM",
        "risk_score": 65,
        "recommendations": [
            "适合风险承受能力中等的投资者",
            "建议控制仓位在10%以内",
            "关注市场整体波动"
        ],
        "analysis_time": "2024-01-01T10:00:00Z"
    }

@app.get("/api/v1/market/overview")
async def get_market_overview():
    """获取市场概览（真实数据）"""
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))

        from app.data_sources.market_data import MarketDataProvider
        from app.data_sources.stock_data import StockDataProvider

        async with MarketDataProvider() as market_provider:
            overview = await market_provider.get_market_overview()

            # 获取热门股票
            async with StockDataProvider() as stock_provider:
                hot_stocks = await stock_provider.get_hot_stocks(limit=10)
                overview["hot_stocks"] = hot_stocks

            return overview
    except Exception:
        # 如果真实数据获取失败，返回模拟数据
        return {
            "hot_stocks": [
                {
                    "code": "000001",
                    "name": "平安银行",
                    "market": "SZ",
                    "current_price": 12.50,
                    "pct_change": 1.22
                }
            ],
            "statistics": {
                "total_stocks": 5000,
                "market_distribution": {
                    "SH": 2000,
                    "SZ": 2500,
                    "BJ": 500
                },
                "top_industries": {
                    "银行": 50,
                    "房地产": 45,
                    "医药": 40
                },
                "last_updated": "2024-01-01T00:00:00Z"
            },
            "market_indices": [
                {
                    "code": "000001",
                    "name": "上证指数",
                    "current": 3200.50,
                    "change": 15.20,
                    "pct_change": 0.48
                }
            ],
            "market_status": {
                "is_trading_day": True,
                "is_trading_time": False,
                "session": "closed",
                "current_time": "2024-01-01T16:00:00Z"
            }
        }

@app.get("/api/v1/news")
async def get_news(
    limit: int = 50,
    category: str = "all",
    search: str = None
):
    """获取新闻列表（真实数据）- 重点A股相关

    Args:
        limit: 返回数量
        category: 新闻分类 (all/market_dynamics/policy/company/market/funds)
        search: 搜索关键词
    """
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))

        from app.data_sources.news_data import NewsDataProvider

        async with NewsDataProvider() as provider:
            news_list = await provider.get_news_list(category=category, limit=limit)

            # 按分类筛选
            if category != "all":
                news_list = [
                    news for news in news_list
                    if news.get('category') == category
                ]

            # 如果有搜索条件，进行筛选
            if search:
                news_list = [
                    news for news in news_list
                    if search.lower() in news.get('title', '').lower() or
                       search.lower() in news.get('content', '').lower() or
                       any(search.lower() in tag.lower() for tag in news.get('tags', []))
                ]

            return {
                "data": news_list,
                "total": len(news_list),
                "skip": 0,
                "limit": limit,
                "category": category,
                "categories": {
                    "all": "全部新闻",
                    "market_dynamics": "市场动态",
                    "policy": "政策监管",
                    "company": "公司公告",
                    "market": "市场行情",
                    "funds": "资金流向"
                }
            }
    except Exception:
        # 如果真实数据获取失败，返回模拟数据
        return {
            "data": [
                {
                    "id": "1",
                    "title": "AI概念股集体涨停，科大讯飞领涨科技板块",
                    "summary": "今日AI概念股表现强劲，科大讯飞、海康威视等龙头股涨停，带动科技板块整体上涨",
                    "source": "财经新闻",
                    "category": "market_dynamics",
                    "publish_time": "2024-01-01T10:00:00Z",
                    "tags": ["AI", "涨停", "科技", "概念股"],
                    "sentiment": "positive",
                    "importance": 8,
                    "view_count": 1500,
                    "like_count": 120,
                    "comment_count": 45
                }
            ],
            "total": 1,
            "skip": 0,
            "limit": limit,
            "category": category
        }

@app.get("/api/v1/news/hot-topics")
async def get_hot_topics(limit: int = 20):
    """获取热点话题（真实数据）"""
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))

        from app.data_sources.news_data import NewsDataProvider

        async with NewsDataProvider() as provider:
            hot_topics = await provider.get_hot_topics(limit=limit)
            return hot_topics
    except Exception:
        # 如果真实数据获取失败，返回模拟数据
        return [
            {
                "topic": "银行股",
                "count": 15,
                "trend": "up",
                "heat_score": 85
            },
            {
                "topic": "新能源",
                "count": 12,
                "trend": "up",
                "heat_score": 78
            }
        ]

@app.get("/api/v1/sectors/hot")
async def get_hot_sectors(
    sector_type: str = "concept",
    limit: int = 20
):
    """获取热门板块（真实数据）

    Args:
        sector_type: 板块类型 ('concept' 概念板块, 'industry' 行业板块)
        limit: 返回数量
    """
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))

        from app.data_sources.market_data import MarketDataProvider

        async with MarketDataProvider() as provider:
            hot_sectors = await provider.get_hot_sectors(sector_type=sector_type, limit=limit)
            return {
                "data": hot_sectors,
                "total": len(hot_sectors),
                "sector_type": sector_type,
                "limit": limit
            }
    except Exception:
        # 如果真实数据获取失败，返回模拟数据
        return {
            "data": [
                {
                    "sector_name": "人工智能" if sector_type == "concept" else "银行",
                    "sector_type": sector_type,
                    "stock_count": 42,
                    "avg_pct_change": 1.25,
                    "total_amount": 15000000000,
                    "avg_turnover_rate": 0.85,
                    "rank": 1
                }
            ],
            "total": 1,
            "sector_type": sector_type,
            "limit": limit
        }

@app.get("/api/v1/analysis/market/sentiment")
async def get_market_sentiment():
    """获取市场情绪分析（模拟数据）"""
    return {
        "sentiment_score": 75,
        "sentiment_level": "乐观",
        "rising_stocks": 2850,
        "falling_stocks": 1650,
        "unchanged_stocks": 500,
        "total_stocks": 5000,
        "rising_ratio": 0.57,
        "avg_change": 0.85,
        "market_activity": "活跃",
        "emotion_indicators": {
            "fear_greed_index": 68,
            "volatility_index": 22.5,
            "volume_ratio": 1.15
        },
        "analysis_time": "2024-01-01T10:00:00Z"
    }

@app.get("/api/v1/sectors/funds-flow")
async def get_sectors_funds_flow():
    """获取板块资金流向（模拟数据）"""
    return {
        "data": [
            {
                "sector_name": "人工智能",
                "net_inflow": 1250000000,
                "inflow": 2800000000,
                "outflow": 1550000000,
                "net_inflow_ratio": 0.089,
                "main_force_inflow": 850000000,
                "retail_inflow": 400000000
            },
            {
                "sector_name": "新能源汽车",
                "net_inflow": 980000000,
                "inflow": 2200000000,
                "outflow": **********,
                "net_inflow_ratio": 0.078,
                "main_force_inflow": 650000000,
                "retail_inflow": 330000000
            }
        ],
        "total": 2,
        "analysis_time": "2024-01-01T10:00:00Z"
    }



if __name__ == "__main__":
    print("🚀 启动简化版股票智能体后端服务...")
    print("📍 服务地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    print("🔍 健康检查: http://localhost:8000/health")
    
    uvicorn.run(
        "simple_test:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
