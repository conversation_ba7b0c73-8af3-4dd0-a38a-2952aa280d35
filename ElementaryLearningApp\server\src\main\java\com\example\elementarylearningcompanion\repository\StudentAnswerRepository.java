package com.example.elementarylearningcompanion.repository;

import com.example.elementarylearningcompanion.model.StudentAnswer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface StudentAnswerRepository extends JpaRepository<StudentAnswer, Long> {
    
    List<StudentAnswer> findByStudentIdAndTestPaperId(Long studentId, Long testPaperId);
    
    List<StudentAnswer> findByStudentIdAndTestPaperIdOrderByCreatedAtAsc(Long studentId, Long testPaperId);
    
    @Query("SELECT sa FROM StudentAnswer sa WHERE sa.studentId = :studentId " +
           "AND sa.testPaperId IS NULL ORDER BY sa.createdAt DESC")
    List<StudentAnswer> findIndividualPracticeAnswers(@Param("studentId") Long studentId);
    
    @Query("SELECT COUNT(sa) FROM StudentAnswer sa WHERE sa.studentId = :studentId " +
           "AND sa.testPaperId = :testPaperId AND sa.isCorrect = true")
    Long countCorrectAnswers(@Param("studentId") Long studentId, @Param("testPaperId") Long testPaperId);
    
    @Query("SELECT COUNT(sa) FROM StudentAnswer sa WHERE sa.studentId = :studentId " +
           "AND sa.testPaperId = :testPaperId")
    Long countTotalAnswers(@Param("studentId") Long studentId, @Param("testPaperId") Long testPaperId);
    
    @Query("SELECT SUM(sa.pointsEarned) FROM StudentAnswer sa WHERE sa.studentId = :studentId " +
           "AND sa.testPaperId = :testPaperId")
    Long getTotalScore(@Param("studentId") Long studentId, @Param("testPaperId") Long testPaperId);
    
    @Query("SELECT SUM(sa.timeSpent) FROM StudentAnswer sa WHERE sa.studentId = :studentId " +
           "AND sa.testPaperId = :testPaperId")
    Long getTotalTimeSpent(@Param("studentId") Long studentId, @Param("testPaperId") Long testPaperId);
    
    @Query("SELECT sa FROM StudentAnswer sa WHERE sa.studentId = :studentId " +
           "AND sa.isCorrect = false AND sa.createdAt BETWEEN :start AND :end")
    List<StudentAnswer> findWrongAnswersInPeriod(@Param("studentId") Long studentId,
                                                @Param("start") LocalDateTime start,
                                                @Param("end") LocalDateTime end);
}
