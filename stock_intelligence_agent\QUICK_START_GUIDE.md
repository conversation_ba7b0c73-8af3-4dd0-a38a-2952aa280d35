# 🚀 股票智能体 - 快速入门指南

## 📋 项目简介

股票智能体是一个基于AI的专业股票分析平台，集成了实时数据获取、智能分析、新闻情绪分析等功能。

### 🎯 核心功能
- **实时股票数据**: 获取沪深两市实时行情
- **智能技术分析**: 20+专业技术指标计算
- **新闻情绪分析**: 自动分析财经新闻情绪
- **市场热点发现**: 智能发现市场热点和趋势
- **投资组合分析**: 专业的风险评估和优化建议

## ⚡ 5分钟快速体验

### 第一步：启动后端服务
```bash
# 进入后端目录
cd backend

# 启动服务 (包含真实数据)
python simple_test.py
```

**看到这个输出表示启动成功**:
```
🚀 启动简化版股票智能体后端服务...
📍 服务地址: http://localhost:8000
📖 API文档: http://localhost:8000/docs
🔍 健康检查: http://localhost:8000/health
INFO:     Uvicorn running on http://0.0.0.0:8000
```

### 第二步：验证服务运行
在浏览器中访问: http://localhost:8000/health

**看到这个响应表示服务正常**:
```json
{
  "status": "healthy",
  "service": "stock-intelligence-agent",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 第三步：体验核心功能
打开详细测试页面: `frontend/detailed-test.html`

**测试清单**:
- [ ] 点击"获取股票列表" - 验证真实股票数据
- [ ] 点击"热门股票" - 查看涨幅排行
- [ ] 点击"新闻列表" - 获取最新财经新闻
- [ ] 点击"热点话题" - 发现市场热点
- [ ] 点击"市场概览" - 查看市场整体情况

## 📊 数据来源说明

### 股票数据源
```
新浪财经API → 实时行情数据
├── 股票价格、涨跌幅
├── 成交量、成交额
├── 换手率、市盈率
└── 实时更新 (30秒延迟)

东方财富API → 热门股票数据
├── 涨幅排行榜
├── 成交量排行
├── 板块热度排行
└── 技术指标数据

交易所官方API → 基础信息
├── 股票代码和名称
├── 上市时间
├── 所属行业
└── 市场分类 (沪深)
```

### 新闻数据源
```
新浪财经新闻 → 财经资讯
├── 市场动态新闻
├── 政策法规新闻
├── 公司公告新闻
└── 自动情绪分析

东方财富公告 → 上市公司公告
├── 财务报告
├── 重大事项披露
├── 股东大会决议
└── 监管公告
```

### 智能分析算法
```
情绪分析算法:
1. 定义情绪词典
   - 正面词汇: ['上涨', '利好', '增长', '突破']
   - 负面词汇: ['下跌', '利空', '下滑', '风险']

2. 文本分析流程
   - 分词和词性标注
   - 情绪词汇匹配
   - 权重计算和评分
   - 情绪分类 (利好/利空/中性)

热点发现算法:
1. 关键词提取
   - 股票代码识别
   - 行业关键词匹配
   - 概念主题提取

2. 热度计算
   - 出现频率统计
   - 时间衰减权重
   - 热度评分排序
```

## 🔧 API接口使用指南

### 核心接口说明

#### 1. 获取股票列表
```http
GET /api/v1/stocks?limit=20&market=all&sort_by=pct_change

参数说明:
- limit: 返回数量 (默认50)
- market: 市场筛选 (SH/SZ/all)
- sort_by: 排序字段 (pct_change/amount/volume)
- order: 排序方向 (asc/desc)

响应示例:
{
  "data": [
    {
      "code": "000001",
      "name": "平安银行",
      "market": "SZ",
      "current_price": 12.50,
      "change": 0.15,
      "pct_change": 1.22,
      "volume": 1500000,
      "amount": 18750000
    }
  ],
  "total": 1,
  "limit": 20
}
```

#### 2. 获取新闻列表
```http
GET /api/v1/news?limit=10&category=market

参数说明:
- limit: 返回数量
- category: 新闻分类 (market/policy/company/all)
- search: 搜索关键词

响应示例:
{
  "data": [
    {
      "id": "1",
      "title": "A股市场今日表现强劲",
      "summary": "今日A股三大指数全线上涨...",
      "source": "新浪财经",
      "sentiment": "positive",
      "importance": 8,
      "tags": ["A股", "银行"]
    }
  ]
}
```

#### 3. 获取热门板块
```http
GET /api/v1/sectors/hot?limit=10

响应示例:
{
  "data": [
    {
      "sector_name": "银行",
      "stock_count": 42,
      "avg_pct_change": 1.25,
      "total_amount": 15000000000,
      "leading_stock": "平安银行"
    }
  ]
}
```

#### 4. 技术分析
```http
GET /api/v1/analysis/technical/000001

响应示例:
{
  "stock_code": "000001",
  "indicators": {
    "ma": {
      "ma5": 12.45,
      "ma10": 12.30,
      "ma20": 12.10
    },
    "macd": {
      "macd": 0.15,
      "signal": 0.12,
      "histogram": 0.03
    },
    "rsi": 65.5
  },
  "signals": {
    "overall": "BUY",
    "confidence": 0.75
  }
}
```

## 🎨 前端界面使用指南

### React应用启动
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm start

# 浏览器自动打开: http://localhost:3000
```

### 页面功能说明

#### 1. 首页 (/)
- **市场概览**: 显示主要指数和市场统计
- **热门股票**: 展示涨幅榜前10名
- **最新新闻**: 显示最新的财经新闻
- **快速搜索**: 支持股票代码和名称搜索

#### 2. 股票页面 (/stocks)
- **股票列表**: 显示所有股票的实时行情
- **筛选功能**: 按市场、行业、涨跌幅筛选
- **排序功能**: 按价格、涨跌幅、成交量排序
- **自选股操作**: 快速添加/移除自选股

#### 3. 股票详情页 (/stocks/:code)
- **基本信息**: 股票代码、名称、行业等
- **实时行情**: 当前价格、涨跌幅、成交量等
- **技术分析**: 技术指标图表和买卖信号
- **相关新闻**: 该股票的相关新闻资讯

#### 4. 新闻页面 (/news)
- **新闻列表**: 最新财经新闻列表
- **分类筛选**: 按新闻类型筛选
- **情绪标签**: 显示新闻情绪分析结果
- **热点话题**: 当前市场热点话题排行

#### 5. 自选股页面 (/watchlist)
- **自选股列表**: 用户关注的股票列表
- **批量操作**: 批量添加/删除自选股
- **预警设置**: 设置价格预警和涨跌幅预警
- **快速交易**: 快速查看自选股行情

## 🔍 故障排除指南

### 常见问题及解决方案

#### 1. 后端启动失败
**问题**: `ModuleNotFoundError: No module named 'fastapi'`
**解决**: 
```bash
pip install fastapi uvicorn aiohttp beautifulsoup4
```

**问题**: `Port 8000 is already in use`
**解决**: 
```bash
# 查找占用端口的进程
netstat -ano | findstr :8000
# 杀死进程 (Windows)
taskkill /PID <进程ID> /F
```

#### 2. 数据获取失败
**问题**: API返回空数据或错误
**原因**: 
- 网络连接问题
- 外部数据源限制
- 请求频率过高

**解决**: 
- 检查网络连接
- 等待一段时间后重试
- 系统会自动降级到模拟数据

#### 3. 前端启动失败
**问题**: `npm: command not found`
**解决**: 安装Node.js (版本16+)

**问题**: `Module not found: Can't resolve '@/components'`
**解决**: 
```bash
npm install
npm start
```

#### 4. 跨域问题
**问题**: `CORS policy: No 'Access-Control-Allow-Origin' header`
**解决**: 后端已配置CORS，确保后端服务正常运行

### 性能优化建议

#### 1. 数据获取优化
- 使用合理的limit参数 (建议≤50)
- 避免频繁刷新页面
- 利用浏览器缓存

#### 2. 网络优化
- 使用稳定的网络连接
- 避免在网络高峰期使用
- 考虑使用CDN加速

#### 3. 浏览器优化
- 使用现代浏览器 (Chrome/Firefox/Edge)
- 清理浏览器缓存
- 关闭不必要的浏览器插件

## 📚 进阶使用

### 自定义开发
```bash
# 克隆项目
git clone <repository-url>

# 安装后端依赖
cd backend
pip install -r requirements.txt

# 安装前端依赖
cd frontend
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件设置数据库连接等
```

### 数据库配置
```bash
# 安装PostgreSQL
# 创建数据库
createdb stock_intelligence

# 运行数据库迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser
```

### 部署到生产环境
```bash
# 构建前端
cd frontend
npm run build

# 配置Nginx
# 配置SSL证书
# 配置域名解析

# 启动生产服务
cd backend
gunicorn app.main:app --workers 4 --bind 0.0.0.0:8000
```

## 🎯 下一步学习

### 推荐学习路径
1. **熟悉API**: 通过API文档了解所有接口
2. **理解算法**: 阅读技术文档了解分析算法
3. **自定义功能**: 基于现有代码开发新功能
4. **性能优化**: 学习系统优化和监控
5. **部署运维**: 学习生产环境部署

### 相关资源
- **API文档**: http://localhost:8000/docs
- **技术文档**: `TECHNICAL_DOCUMENTATION.md`
- **架构指南**: `ARCHITECTURE_GUIDE.md`
- **项目总结**: `FINAL_SUMMARY.md`

---

**🎉 恭喜！您已经成功入门股票智能体系统！**

如有问题，请参考技术文档或查看项目代码。
