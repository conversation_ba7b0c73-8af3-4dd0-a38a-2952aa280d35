#!/usr/bin/env python3
"""
测试AKShare研报数据接口
"""

import akshare as ak
import pandas as pd

def test_research_report():
    """测试研报数据接口"""
    try:
        print("🧪 测试AKShare研报数据接口...")
        
        # 测试不同的股票代码
        test_codes = ["300750", "000001", "600036"]
        
        for code in test_codes:
            print(f"\n📊 测试股票代码: {code}")
            try:
                # 尝试获取研报数据
                df = ak.stock_research_report_em(symbol=code)
                
                if df.empty:
                    print(f"❌ {code}: 无研报数据")
                else:
                    print(f"✅ {code}: 获取到 {len(df)} 条研报数据")
                    print("📋 数据列名:", list(df.columns))
                    
                    # 显示前几条数据
                    if len(df) > 0:
                        print("📄 前3条数据:")
                        for i, (_, row) in enumerate(df.head(3).iterrows()):
                            print(f"  {i+1}. {dict(row)}")
                            
            except Exception as e:
                print(f"❌ {code}: 获取失败 - {e}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_other_interfaces():
    """测试其他相关接口"""
    try:
        print("\n🔍 测试其他AKShare接口...")
        
        # 测试股票基本信息
        try:
            info_df = ak.stock_individual_info_em(symbol="300750")
            print("✅ 股票基本信息接口正常")
            print("📋 信息字段:", list(info_df.columns) if hasattr(info_df, 'columns') else "非DataFrame格式")
        except Exception as e:
            print(f"❌ 股票基本信息接口失败: {e}")
            
        # 测试实时行情
        try:
            spot_df = ak.stock_zh_a_spot_em()
            print(f"✅ 实时行情接口正常，获取到 {len(spot_df)} 只股票")
        except Exception as e:
            print(f"❌ 实时行情接口失败: {e}")
            
    except Exception as e:
        print(f"❌ 其他接口测试失败: {e}")

if __name__ == "__main__":
    print("📊 AKShare接口测试")
    print("=" * 50)
    
    test_research_report()
    test_other_interfaces()
    
    print("\n🎉 测试完成！")
