# 🏗️ 股票智能体 - 架构设计指南

## 📊 系统架构总览

### 整体架构图
```
┌─────────────────────────────────────────────────────────────────┐
│                        用户界面层                                │
├─────────────────────────────────────────────────────────────────┤
│  React前端应用                                                   │
│  ├── 页面组件 (Pages)                                           │
│  │   ├── 首页 (Home)                                            │
│  │   ├── 股票页面 (Stocks)                                      │
│  │   ├── 股票详情 (StockDetail)                                 │
│  │   ├── 新闻页面 (News)                                        │
│  │   └── 自选股页面 (Watchlist)                                 │
│  ├── 状态管理 (Redux)                                           │
│  │   ├── stockSlice                                             │
│  │   ├── newsSlice                                              │
│  │   ├── watchlistSlice                                         │
│  │   └── authSlice                                              │
│  └── 服务层 (Services)                                          │
│      ├── API服务                                                │
│      ├── WebSocket服务                                          │
│      └── 缓存服务                                               │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼ HTTP/WebSocket
┌─────────────────────────────────────────────────────────────────┐
│                        API网关层                                │
├─────────────────────────────────────────────────────────────────┤
│  FastAPI后端应用                                                │
│  ├── 路由层 (Routers)                                           │
│  │   ├── 股票路由 (/api/v1/stocks)                             │
│  │   ├── 新闻路由 (/api/v1/news)                               │
│  │   ├── 分析路由 (/api/v1/analysis)                           │
│  │   └── 用户路由 (/api/v1/users)                              │
│  ├── 中间件 (Middleware)                                        │
│  │   ├── CORS中间件                                             │
│  │   ├── 认证中间件                                             │
│  │   └── 日志中间件                                             │
│  └── WebSocket管理器                                            │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼ 服务调用
┌─────────────────────────────────────────────────────────────────┐
│                        业务逻辑层                                │
├─────────────────────────────────────────────────────────────────┤
│  业务服务 (Services)                                            │
│  ├── 股票服务 (StockService)                                    │
│  │   ├── 股票数据获取                                           │
│  │   ├── 实时行情处理                                           │
│  │   └── 股票筛选排序                                           │
│  ├── 分析服务 (AnalysisService)                                 │
│  │   ├── 技术分析算法                                           │
│  │   ├── 基本面分析                                             │
│  │   └── 风险评估模型                                           │
│  ├── 新闻服务 (NewsService)                                     │
│  │   ├── 新闻数据获取                                           │
│  │   ├── 情绪分析算法                                           │
│  │   └── 热点话题发现                                           │
│  └── 用户服务 (UserService)                                     │
│      ├── 自选股管理                                             │
│      ├── 预警系统                                               │
│      └── 用户认证                                               │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼ 数据访问
┌─────────────────────────────────────────────────────────────────┐
│                        数据访问层                                │
├─────────────────────────────────────────────────────────────────┤
│  数据源提供者 (Data Providers)                                   │
│  ├── 股票数据源 (StockDataProvider)                             │
│  │   ├── 新浪财经API                                            │
│  │   ├── 东方财富API                                            │
│  │   └── 交易所API                                              │
│  ├── 新闻数据源 (NewsDataProvider)                              │
│  │   ├── 新浪财经新闻                                           │
│  │   ├── 东方财富公告                                           │
│  │   └── 其他财经媒体                                           │
│  └── 市场数据源 (MarketDataProvider)                            │
│      ├── 板块数据                                               │
│      ├── 指数数据                                               │
│      └── 市场统计                                               │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼ 数据存储
┌─────────────────────────────────────────────────────────────────┐
│                        数据存储层                                │
├─────────────────────────────────────────────────────────────────┤
│  ├── PostgreSQL数据库                                           │
│  │   ├── 用户数据表                                             │
│  │   ├── 股票基础信息表                                         │
│  │   ├── 股票行情数据表                                         │
│  │   ├── 新闻信息表                                             │
│  │   ├── 自选股表                                               │
│  │   └── 预警表                                                 │
│  └── Redis缓存                                                  │
│      ├── 实时行情缓存                                           │
│      ├── 热门数据缓存                                           │
│      ├── 用户会话缓存                                           │
│      └── API响应缓存                                            │
└─────────────────────────────────────────────────────────────────┘
```

## 🔄 数据流程图

### 股票数据获取流程
```
用户请求 → API路由 → 股票服务 → 数据源提供者 → 外部API
    ↓         ↓         ↓           ↓            ↓
响应返回 ← JSON格式 ← 业务处理 ← 数据清洗 ← 原始数据

详细流程:
1. 用户在前端点击"获取股票列表"
2. 前端发送HTTP请求到 /api/v1/stocks
3. FastAPI路由接收请求，调用StockService
4. StockService调用StockDataProvider
5. StockDataProvider异步请求多个外部API:
   - 新浪财经: 获取实时行情
   - 东方财富: 获取热门股票
   - 交易所: 获取基础信息
6. 数据清洗和格式统一
7. 返回标准化的JSON数据
8. 前端Redux更新状态
9. 页面重新渲染显示数据
```

### 新闻分析流程
```
新闻获取 → 内容分析 → 情绪判断 → 关键词提取 → 热点发现
    ↓         ↓         ↓         ↓           ↓
原始新闻 → 文本处理 → 情绪标签 → 标签列表 → 热点排行

详细算法:
1. 从多个新闻源获取原始新闻数据
2. 文本预处理 (去除HTML标签、特殊字符)
3. 情绪分析算法:
   - 定义正面词汇: ['上涨', '利好', '增长', '突破']
   - 定义负面词汇: ['下跌', '利空', '下滑', '风险']
   - 统计词汇出现频率
   - 计算情绪倾向得分
4. 关键词提取:
   - 正则表达式提取股票代码
   - 关键词匹配提取行业词汇
   - 去重和标准化
5. 热点话题发现:
   - 统计关键词在所有新闻中的出现频率
   - 计算热度评分 = 出现次数 × 权重系数
   - 按热度排序生成热点排行榜
```

### 技术分析计算流程
```
历史价格 → TA-Lib计算 → 指标数值 → 信号生成 → 投资建议
    ↓          ↓          ↓         ↓         ↓
价格数组 → 技术指标函数 → 指标结果 → 买卖信号 → 综合评分

详细计算:
1. 获取股票历史价格数据 (收盘价、最高价、最低价、成交量)
2. 使用TA-Lib库计算技术指标:
   - MA(5,10,20): 移动平均线
   - MACD(12,26,9): 指数平滑移动平均线
   - RSI(14): 相对强弱指标
   - BOLL(20,2): 布林带
   - KDJ: 随机指标
3. 信号生成逻辑:
   - MA信号: 短期均线上穿长期均线 = 买入信号
   - MACD信号: MACD线上穿信号线 = 买入信号
   - RSI信号: RSI < 30 = 超卖买入, RSI > 70 = 超买卖出
4. 综合评分算法:
   - 统计各指标的买入/卖出信号数量
   - 计算信号一致性和可信度
   - 生成最终的投资建议和置信度
```

## 🔐 安全架构设计

### 认证授权流程
```
用户登录 → 密码验证 → JWT生成 → Token存储 → API访问
    ↓         ↓         ↓         ↓         ↓
登录表单 → 密码哈希 → 签名Token → 本地存储 → 请求头携带

安全机制:
1. 密码加密存储 (bcrypt哈希)
2. JWT Token机制:
   - 包含用户ID、用户名、过期时间
   - 使用HMAC SHA256签名
   - 24小时过期时间
3. API请求验证:
   - 每个API请求检查Authorization头
   - 验证Token有效性和过期时间
   - 提取用户信息进行权限检查
4. 权限控制:
   - 基于角色的访问控制 (RBAC)
   - 不同用户角色有不同的API访问权限
   - 敏感操作需要额外验证
```

### 数据安全措施
```
数据传输安全:
├── HTTPS加密传输
├── API请求签名验证
├── 敏感数据脱敏
└── 请求频率限制

数据存储安全:
├── 数据库连接加密
├── 敏感字段加密存储
├── 定期数据备份
└── 访问日志记录

API安全防护:
├── CORS跨域限制
├── SQL注入防护
├── XSS攻击防护
└── DDoS攻击防护
```

## ⚡ 性能优化策略

### 缓存架构设计
```
多层缓存策略:

1. 浏览器缓存 (前端)
   ├── 静态资源缓存 (CSS, JS, 图片)
   ├── API响应缓存 (短期缓存)
   └── 用户状态缓存 (Redux持久化)

2. CDN缓存 (边缘节点)
   ├── 静态文件分发
   ├── API响应缓存
   └── 地理位置就近访问

3. 应用层缓存 (后端)
   ├── 内存缓存 (热点数据)
   ├── Redis缓存 (共享缓存)
   └── 数据库查询缓存

4. 数据库缓存 (存储层)
   ├── 查询结果缓存
   ├── 索引缓存
   └── 连接池缓存

缓存策略:
- 股票实时数据: 30秒缓存
- 股票列表数据: 5分钟缓存
- 新闻数据: 10分钟缓存
- 用户数据: 1小时缓存
- 静态配置: 24小时缓存
```

### 数据库优化策略
```
索引优化:
├── 主键索引 (自动创建)
├── 唯一索引 (股票代码、用户名)
├── 复合索引 (股票代码+交易日期)
└── 全文索引 (新闻标题和内容)

查询优化:
├── 分页查询 (LIMIT + OFFSET)
├── 条件筛选 (WHERE子句优化)
├── 连接查询 (JOIN优化)
└── 聚合查询 (GROUP BY优化)

数据分区:
├── 按时间分区 (行情数据按月分区)
├── 按类型分区 (不同市场分区存储)
└── 读写分离 (主从数据库)

连接池管理:
├── 最大连接数限制
├── 连接超时设置
├── 连接复用机制
└── 连接健康检查
```

### 异步处理架构
```
异步任务队列:

1. 实时数据更新任务
   ├── 股票行情数据获取
   ├── 新闻数据抓取
   ├── 市场数据更新
   └── WebSocket数据推送

2. 定时任务调度
   ├── 每日股票数据同步
   ├── 新闻情绪分析批处理
   ├── 用户预警检查
   └── 系统性能监控

3. 后台计算任务
   ├── 技术指标批量计算
   ├── 投资组合分析
   ├── 风险评估计算
   └── 数据统计分析

任务队列实现:
├── Celery任务队列
├── Redis作为消息代理
├── 任务优先级管理
└── 任务失败重试机制
```

## 📊 监控和运维架构

### 系统监控体系
```
监控层次:

1. 基础设施监控
   ├── 服务器CPU、内存、磁盘使用率
   ├── 网络带宽和延迟
   ├── 数据库连接数和查询性能
   └── Redis缓存命中率

2. 应用性能监控
   ├── API响应时间和吞吐量
   ├── 错误率和异常统计
   ├── 用户访问量和活跃度
   └── 业务指标监控

3. 业务监控
   ├── 数据获取成功率
   ├── 用户操作行为分析
   ├── 功能使用统计
   └── 收入和转化率

监控工具:
├── Prometheus (指标收集)
├── Grafana (可视化面板)
├── ELK Stack (日志分析)
└── 自定义监控脚本
```

### 日志管理体系
```
日志分类:

1. 访问日志
   ├── HTTP请求日志
   ├── API调用日志
   ├── 用户操作日志
   └── 系统访问日志

2. 错误日志
   ├── 应用异常日志
   ├── 数据库错误日志
   ├── 外部API调用失败日志
   └── 系统错误日志

3. 业务日志
   ├── 数据更新日志
   ├── 用户行为日志
   ├── 交易操作日志
   └── 安全事件日志

日志处理:
├── 结构化日志格式 (JSON)
├── 日志等级分类 (DEBUG/INFO/WARN/ERROR)
├── 日志轮转和压缩
└── 日志集中收集和分析
```

这个架构指南详细说明了系统的各个层次和组件之间的关系。您觉得还需要补充哪些架构方面的内容吗？
