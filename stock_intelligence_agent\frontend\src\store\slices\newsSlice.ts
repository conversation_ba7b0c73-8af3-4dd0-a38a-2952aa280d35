/**
 * 新闻相关状态管理
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../index';
import { apiService } from '@/services/api';

// 类型定义
export interface NewsItem {
  id: string;
  title: string;
  summary: string;
  content: string;
  source: string;
  author: string;
  publish_time: string;
  url: string;
  category: string;
  tags: string[];
  sentiment: 'positive' | 'negative' | 'neutral';
  importance: number;
  view_count: number;
  like_count: number;
}

export interface HotTopic {
  topic: string;
  count: number;
  trend: 'up' | 'down' | 'stable';
  heat_score: number;
}

export interface NewsState {
  // 新闻列表
  news: NewsItem[];
  newsLoading: boolean;
  newsError: string | null;
  
  // 新闻详情
  newsDetail: NewsItem | null;
  newsDetailLoading: boolean;
  newsDetailError: string | null;
  
  // 热点话题
  hotTopics: HotTopic[];
  hotTopicsLoading: boolean;
  hotTopicsError: string | null;
  
  // 筛选条件
  filters: {
    category: string;
    sentiment: string;
    search: string;
    sort_by: string;
  };
}

// 初始状态
const initialState: NewsState = {
  news: [],
  newsLoading: false,
  newsError: null,
  
  newsDetail: null,
  newsDetailLoading: false,
  newsDetailError: null,
  
  hotTopics: [],
  hotTopicsLoading: false,
  hotTopicsError: null,
  
  filters: {
    category: 'all',
    sentiment: 'all',
    search: '',
    sort_by: 'publish_time',
  },
};

// 异步 actions
export const fetchNewsAsync = createAsyncThunk(
  'news/fetchNews',
  async (params: {
    limit?: number;
    category?: string;
    sentiment?: string;
    search?: string;
    sort_by?: string;
  }) => {
    const response = await apiService.getNews({
      limit: params.limit,
      category_id: params.category,
      sentiment: params.sentiment,
      search_query: params.search,
    });
    return response.data;
  }
);

export const fetchNewsDetailAsync = createAsyncThunk(
  'news/fetchNewsDetail',
  async (newsId: string) => {
    const response = await apiService.getNewsById(newsId);
    return response;
  }
);

export const fetchHotTopicsAsync = createAsyncThunk(
  'news/fetchHotTopics',
  async (params: { limit?: number }) => {
    const response = await apiService.getHotNews(params);
    return response;
  }
);

// Slice
const newsSlice = createSlice({
  name: 'news',
  initialState,
  reducers: {
    // 设置筛选条件
    setFilters: (state, action: PayloadAction<Partial<NewsState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    // 清除新闻详情
    clearNewsDetail: (state) => {
      state.newsDetail = null;
      state.newsDetailError = null;
    },
    
    // 清除错误
    clearErrors: (state) => {
      state.newsError = null;
      state.newsDetailError = null;
      state.hotTopicsError = null;
    },
  },
  extraReducers: (builder) => {
    // 获取新闻列表
    builder
      .addCase(fetchNewsAsync.pending, (state) => {
        state.newsLoading = true;
        state.newsError = null;
      })
      .addCase(fetchNewsAsync.fulfilled, (state, action) => {
        state.newsLoading = false;
        state.news = action.payload;
      })
      .addCase(fetchNewsAsync.rejected, (state, action) => {
        state.newsLoading = false;
        state.newsError = action.error.message || '获取新闻失败';
      });
    
    // 获取新闻详情
    builder
      .addCase(fetchNewsDetailAsync.pending, (state) => {
        state.newsDetailLoading = true;
        state.newsDetailError = null;
      })
      .addCase(fetchNewsDetailAsync.fulfilled, (state, action) => {
        state.newsDetailLoading = false;
        state.newsDetail = action.payload;
      })
      .addCase(fetchNewsDetailAsync.rejected, (state, action) => {
        state.newsDetailLoading = false;
        state.newsDetailError = action.error.message || '获取新闻详情失败';
      });
    
    // 获取热点话题
    builder
      .addCase(fetchHotTopicsAsync.pending, (state) => {
        state.hotTopicsLoading = true;
        state.hotTopicsError = null;
      })
      .addCase(fetchHotTopicsAsync.fulfilled, (state, action) => {
        state.hotTopicsLoading = false;
        state.hotTopics = action.payload;
      })
      .addCase(fetchHotTopicsAsync.rejected, (state, action) => {
        state.hotTopicsLoading = false;
        state.hotTopicsError = action.error.message || '获取热点话题失败';
      });
  },
});

// 导出 actions
export const { setFilters, clearNewsDetail, clearErrors } = newsSlice.actions;

// 选择器
export const selectNews = (state: RootState) => state.news.news;
export const selectNewsLoading = (state: RootState) => state.news.newsLoading;
export const selectNewsError = (state: RootState) => state.news.newsError;

export const selectNewsDetail = (state: RootState) => state.news.newsDetail;
export const selectNewsDetailLoading = (state: RootState) => state.news.newsDetailLoading;
export const selectNewsDetailError = (state: RootState) => state.news.newsDetailError;

export const selectHotTopics = (state: RootState) => state.news.hotTopics;
export const selectHotTopicsLoading = (state: RootState) => state.news.hotTopicsLoading;
export const selectHotTopicsError = (state: RootState) => state.news.hotTopicsError;

export const selectNewsFilters = (state: RootState) => state.news.filters;

export default newsSlice.reducer;
