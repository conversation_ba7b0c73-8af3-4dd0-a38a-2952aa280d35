#Mon Jun 16 23:41:25 CST 2025
base.0=F\:\\study\\ElementaryLearningApp\\client\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=F\:\\study\\ElementaryLearningApp\\client\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.2=F\:\\study\\ElementaryLearningApp\\client\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\11\\classes.dex
base.3=F\:\\study\\ElementaryLearningApp\\client\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\3\\classes.dex
base.4=F\:\\study\\ElementaryLearningApp\\client\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\5\\classes.dex
base.5=F\:\\study\\ElementaryLearningApp\\client\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\7\\classes.dex
base.6=F\:\\study\\ElementaryLearningApp\\client\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\8\\classes.dex
base.7=F\:\\study\\ElementaryLearningApp\\client\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
base.8=F\:\\study\\ElementaryLearningApp\\client\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes3.dex
path.0=classes.dex
path.1=0/classes.dex
path.2=11/classes.dex
path.3=3/classes.dex
path.4=5/classes.dex
path.5=7/classes.dex
path.6=8/classes.dex
path.7=classes2.dex
path.8=classes3.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
