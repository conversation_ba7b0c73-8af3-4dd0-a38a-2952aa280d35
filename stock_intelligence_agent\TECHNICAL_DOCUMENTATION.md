# 📚 股票智能体 - 详细技术说明文档

## 🎯 项目架构总览

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端API       │    │   数据源        │
│   React+TS      │◄──►│   FastAPI       │◄──►│   真实数据      │
│   Redux管理     │    │   异步处理      │    │   多源集成      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面      │    │   业务逻辑      │    │   数据处理      │
│   - 股票列表    │    │   - 技术分析    │    │   - 数据清洗    │
│   - 新闻资讯    │    │   - 情绪分析    │    │   - 格式统一    │
│   - 市场分析    │    │   - 风险评估    │    │   - 实时更新    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📊 数据源模块详解

### 1. 股票数据源 (`StockDataProvider`)

#### 数据来源
- **新浪财经API**: `http://hq.sinajs.cn/list=`
  - 实时行情数据
  - 股票基本信息
  - 成交量、成交额等
  
- **东方财富API**: `http://push2.eastmoney.com/api/qt/`
  - 热门股票排行
  - 涨跌幅数据
  - 技术指标数据

- **交易所API**: 
  - 上交所: `http://query.sse.com.cn/`
  - 深交所: `http://www.szse.cn/api/`

#### 运作原理
```python
async def get_stock_list(self, market: str = "all"):
    """
    1. 根据市场参数确定数据源
    2. 异步请求多个数据源
    3. 解析和清洗数据
    4. 统一数据格式
    5. 返回标准化结果
    """
    # 获取上海股票
    if market == "SH" or market == "all":
        sh_stocks = await self._get_sh_stocks()
    
    # 获取深圳股票  
    if market == "SZ" or market == "all":
        sz_stocks = await self._get_sz_stocks()
    
    # 合并并获取实时行情
    all_stocks = sh_stocks + sz_stocks
    quotes = await self.get_realtime_quotes(stock_codes)
    
    # 数据合并和格式化
    return formatted_stocks
```

#### 数据字段说明
```json
{
  "code": "000001",           // 股票代码
  "name": "平安银行",         // 股票名称
  "market": "SZ",             // 市场(SH/SZ/BJ)
  "industry": "银行",         // 所属行业
  "current_price": 12.50,     // 当前价格
  "change": 0.15,             // 涨跌额
  "pct_change": 1.22,         // 涨跌幅(%)
  "volume": 1500000,          // 成交量(股)
  "amount": 18750000,         // 成交额(元)
  "turnover_rate": 0.85,      // 换手率(%)
  "pe_ratio": 5.2,            // 市盈率
  "pb_ratio": 0.8             // 市净率
}
```

### 2. 新闻数据源 (`NewsDataProvider`)

#### 数据来源
- **新浪财经新闻**: `https://feed.mix.sina.com.cn/api/roll/get`
  - 财经新闻资讯
  - 实时新闻更新
  - 新闻分类标签

- **东方财富公告**: `https://np-anotice-stock.eastmoney.com/api/security/ann`
  - 上市公司公告
  - 重大事项披露
  - 财务报告等

#### 智能分析算法
```python
def _analyze_sentiment(self, title: str) -> str:
    """
    新闻情绪分析算法
    
    1. 定义情绪词典
    """
    positive_words = ['上涨', '利好', '增长', '突破', '创新高', '大涨']
    negative_words = ['下跌', '利空', '下滑', '暴跌', '创新低', '风险']
    
    # 2. 统计正负面词汇
    positive_count = sum(1 for word in positive_words if word in title)
    negative_count = sum(1 for word in negative_words if word in title)
    
    # 3. 判断情绪倾向
    if positive_count > negative_count:
        return 'positive'  # 利好
    elif negative_count > positive_count:
        return 'negative'  # 利空
    else:
        return 'neutral'   # 中性
```

#### 关键词提取算法
```python
def _extract_tags(self, title: str) -> List[str]:
    """
    关键词提取算法
    
    1. 提取股票代码
    """
    tags = []
    stock_codes = re.findall(r'\d{6}', title)  # 6位数字
    tags.extend(stock_codes)
    
    # 2. 提取行业关键词
    keywords = ['银行', '地产', '科技', '医药', '新能源', '芯片', '5G', 'AI']
    for keyword in keywords:
        if keyword in title:
            tags.append(keyword)
    
    return list(set(tags))  # 去重
```

#### 热点话题发现算法
```python
async def get_hot_topics(self, limit: int = 20):
    """
    热点话题发现算法
    
    1. 获取大量新闻数据
    """
    news_list = await self.get_news_list(limit=100)
    
    # 2. 统计关键词频率
    keyword_count = {}
    for news in news_list:
        for tag in news.get('tags', []):
            keyword_count[tag] = keyword_count.get(tag, 0) + 1
    
    # 3. 计算热度评分
    hot_topics = []
    for topic, count in sorted(keyword_count.items(), key=lambda x: x[1], reverse=True):
        hot_topics.append({
            'topic': topic,
            'count': count,
            'trend': 'up' if count > 3 else 'stable',
            'heat_score': min(count * 10, 100)  # 热度评分
        })
    
    return hot_topics[:limit]
```

### 3. 市场数据源 (`MarketDataProvider`)

#### 数据来源
- **东方财富板块API**: `http://push2.eastmoney.com/api/qt/clist/get`
  - 行业板块数据 (`fs=m:90+t:2`)
  - 概念板块数据 (`fs=m:90+t:3`)
  - 板块内股票数据

#### 板块分析算法
```python
async def get_hot_sectors(self, limit: int = 20):
    """
    热门板块分析算法
    
    1. 获取板块基础数据
    """
    params = {
        'fs': 'm:90+t:2',  # 行业板块
        'fields': 'f3,f12,f14,f104,f6,f8,f15,f16,f140,f141'
    }
    
    # 2. 数据解析和计算
    for item in response_data:
        sector = {
            'sector_name': item.get('f14'),           # 板块名称
            'stock_count': item.get('f104'),          # 股票数量
            'avg_pct_change': item.get('f3') / 100,   # 平均涨跌幅
            'total_amount': item.get('f6'),           # 总成交额
            'leading_stock': item.get('f140'),        # 领涨股
        }
    
    # 3. 按涨跌幅排序
    return sorted(sectors, key=lambda x: x['avg_pct_change'], reverse=True)
```

#### 市场情绪计算算法
```python
async def calculate_market_sentiment(self):
    """
    市场情绪计算算法
    
    1. 获取全市场股票数据
    """
    all_stocks = await self.get_all_stocks()
    
    # 2. 统计涨跌股票数量
    rising_stocks = sum(1 for stock in all_stocks if stock['pct_change'] > 0)
    falling_stocks = sum(1 for stock in all_stocks if stock['pct_change'] < 0)
    total_stocks = len(all_stocks)
    
    # 3. 计算情绪指标
    rising_ratio = rising_stocks / total_stocks
    avg_change = sum(stock['pct_change'] for stock in all_stocks) / total_stocks
    
    # 4. 情绪评分算法
    sentiment_score = min(100, max(0, (rising_ratio * 50) + (avg_change * 10) + 50))
    
    # 5. 情绪等级判断
    if sentiment_score >= 80:
        sentiment_level = "极度乐观"
    elif sentiment_score >= 60:
        sentiment_level = "乐观"
    elif sentiment_score >= 40:
        sentiment_level = "中性"
    elif sentiment_score >= 20:
        sentiment_level = "悲观"
    else:
        sentiment_level = "极度悲观"
    
    return {
        'sentiment_score': sentiment_score,
        'sentiment_level': sentiment_level,
        'rising_ratio': rising_ratio,
        'avg_change': avg_change
    }
```

## 🧮 分析算法模块详解

### 1. 技术分析模块 (`TechnicalAnalysisService`)

#### 基于TA-Lib的技术指标计算
```python
import talib

def calculate_technical_indicators(self, prices: List[float], volumes: List[int]):
    """
    技术指标计算算法
    
    1. 移动平均线 (MA)
    """
    ma5 = talib.SMA(prices, timeperiod=5)    # 5日均线
    ma10 = talib.SMA(prices, timeperiod=10)  # 10日均线
    ma20 = talib.SMA(prices, timeperiod=20)  # 20日均线
    
    # 2. MACD指标
    macd, macd_signal, macd_hist = talib.MACD(prices, 
                                              fastperiod=12, 
                                              slowperiod=26, 
                                              signalperiod=9)
    
    # 3. RSI相对强弱指标
    rsi = talib.RSI(prices, timeperiod=14)
    
    # 4. 布林带
    upper, middle, lower = talib.BBANDS(prices, 
                                        timeperiod=20, 
                                        nbdevup=2, 
                                        nbdevdn=2)
    
    # 5. KDJ指标
    slowk, slowd = talib.STOCH(high_prices, low_prices, close_prices,
                               fastk_period=9,
                               slowk_period=3,
                               slowd_period=3)
    
    return {
        'ma': {'ma5': ma5[-1], 'ma10': ma10[-1], 'ma20': ma20[-1]},
        'macd': {'macd': macd[-1], 'signal': macd_signal[-1], 'hist': macd_hist[-1]},
        'rsi': rsi[-1],
        'bbands': {'upper': upper[-1], 'middle': middle[-1], 'lower': lower[-1]},
        'kdj': {'k': slowk[-1], 'd': slowd[-1], 'j': 3*slowk[-1] - 2*slowd[-1]}
    }
```

#### 买卖信号生成算法
```python
def generate_trading_signals(self, indicators: Dict):
    """
    买卖信号生成算法
    
    1. 均线信号
    """
    ma_signal = 'HOLD'
    if indicators['ma']['ma5'] > indicators['ma']['ma10'] > indicators['ma']['ma20']:
        ma_signal = 'BUY'   # 多头排列
    elif indicators['ma']['ma5'] < indicators['ma']['ma10'] < indicators['ma']['ma20']:
        ma_signal = 'SELL'  # 空头排列
    
    # 2. MACD信号
    macd_signal = 'HOLD'
    if indicators['macd']['macd'] > indicators['macd']['signal'] and indicators['macd']['hist'] > 0:
        macd_signal = 'BUY'   # 金叉且柱状线为正
    elif indicators['macd']['macd'] < indicators['macd']['signal'] and indicators['macd']['hist'] < 0:
        macd_signal = 'SELL'  # 死叉且柱状线为负
    
    # 3. RSI信号
    rsi_signal = 'HOLD'
    if indicators['rsi'] < 30:
        rsi_signal = 'BUY'    # 超卖
    elif indicators['rsi'] > 70:
        rsi_signal = 'SELL'   # 超买
    
    # 4. 综合信号判断
    buy_signals = [ma_signal, macd_signal, rsi_signal].count('BUY')
    sell_signals = [ma_signal, macd_signal, rsi_signal].count('SELL')
    
    if buy_signals >= 2:
        final_signal = 'BUY'
        confidence = buy_signals / 3
    elif sell_signals >= 2:
        final_signal = 'SELL'
        confidence = sell_signals / 3
    else:
        final_signal = 'HOLD'
        confidence = 0.5
    
    return {
        'signal': final_signal,
        'confidence': confidence,
        'details': {
            'ma_signal': ma_signal,
            'macd_signal': macd_signal,
            'rsi_signal': rsi_signal
        }
    }
```

### 2. 风险评估模块 (`RiskAssessmentService`)

#### VaR (Value at Risk) 计算
```python
import numpy as np
from scipy import stats

def calculate_var(self, returns: List[float], confidence_level: float = 0.95):
    """
    VaR风险价值计算
    
    1. 历史模拟法
    """
    returns_array = np.array(returns)
    var_historical = np.percentile(returns_array, (1 - confidence_level) * 100)
    
    # 2. 参数法 (假设正态分布)
    mean_return = np.mean(returns_array)
    std_return = np.std(returns_array)
    var_parametric = stats.norm.ppf(1 - confidence_level, mean_return, std_return)
    
    # 3. 蒙特卡洛模拟法
    simulated_returns = np.random.normal(mean_return, std_return, 10000)
    var_monte_carlo = np.percentile(simulated_returns, (1 - confidence_level) * 100)
    
    return {
        'var_historical': var_historical,
        'var_parametric': var_parametric,
        'var_monte_carlo': var_monte_carlo,
        'confidence_level': confidence_level
    }
```

#### 夏普比率计算
```python
def calculate_sharpe_ratio(self, returns: List[float], risk_free_rate: float = 0.03):
    """
    夏普比率计算
    
    公式: (投资组合收益率 - 无风险收益率) / 投资组合标准差
    """
    returns_array = np.array(returns)
    
    # 年化收益率
    annual_return = np.mean(returns_array) * 252  # 假设252个交易日
    
    # 年化波动率
    annual_volatility = np.std(returns_array) * np.sqrt(252)
    
    # 夏普比率
    sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility
    
    return {
        'sharpe_ratio': sharpe_ratio,
        'annual_return': annual_return,
        'annual_volatility': annual_volatility,
        'risk_free_rate': risk_free_rate
    }
```

### 3. 投资组合分析模块

#### 现代投资组合理论应用
```python
def analyze_portfolio(self, portfolio: Dict[str, float]):
    """
    投资组合分析算法
    
    1. 获取各股票历史数据
    """
    stock_data = {}
    for stock_code, weight in portfolio.items():
        stock_data[stock_code] = await self.get_stock_history(stock_code)
    
    # 2. 计算收益率矩阵
    returns_matrix = self.calculate_returns_matrix(stock_data)
    
    # 3. 计算协方差矩阵
    cov_matrix = np.cov(returns_matrix.T)
    
    # 4. 计算组合期望收益率
    weights = np.array(list(portfolio.values()))
    expected_returns = np.array([np.mean(returns_matrix[i]) for i in range(len(portfolio))])
    portfolio_return = np.dot(weights, expected_returns)
    
    # 5. 计算组合风险 (标准差)
    portfolio_variance = np.dot(weights.T, np.dot(cov_matrix, weights))
    portfolio_risk = np.sqrt(portfolio_variance)
    
    # 6. 计算相关性
    correlation_matrix = np.corrcoef(returns_matrix)
    
    # 7. 行业分散度分析
    industry_distribution = self.analyze_industry_distribution(portfolio)
    
    return {
        'expected_return': portfolio_return * 252,  # 年化
        'risk': portfolio_risk * np.sqrt(252),     # 年化
        'sharpe_ratio': portfolio_return / portfolio_risk,
        'correlation_matrix': correlation_matrix.tolist(),
        'industry_distribution': industry_distribution,
        'diversification_score': self.calculate_diversification_score(correlation_matrix, weights)
    }
```

## 🔄 数据流处理机制

### 1. 异步数据获取流程
```python
async def data_acquisition_pipeline():
    """
    数据获取流水线
    
    1. 并发获取多源数据
    """
    async with aiohttp.ClientSession() as session:
        tasks = [
            fetch_sina_data(session),      # 新浪财经
            fetch_eastmoney_data(session), # 东方财富
            fetch_exchange_data(session)   # 交易所
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 2. 数据合并和去重
    merged_data = merge_data_sources(results)
    
    # 3. 数据验证和清洗
    cleaned_data = validate_and_clean(merged_data)
    
    # 4. 格式标准化
    standardized_data = standardize_format(cleaned_data)
    
    return standardized_data
```

### 2. 智能降级机制
```python
async def get_data_with_fallback(data_type: str, params: Dict):
    """
    智能降级机制
    
    1. 尝试获取真实数据
    """
    try:
        real_data = await fetch_real_data(data_type, params)
        if validate_data(real_data):
            return real_data
    except Exception as e:
        logger.warning(f"真实数据获取失败: {e}")
    
    # 2. 降级到模拟数据
    try:
        mock_data = get_mock_data(data_type, params)
        logger.info("使用模拟数据")
        return mock_data
    except Exception as e:
        logger.error(f"模拟数据获取失败: {e}")
        raise
```

### 3. 缓存策略
```python
class DataCache:
    """
    数据缓存策略
    """
    def __init__(self):
        self.cache = {}
        self.cache_ttl = {
            'stock_list': 300,      # 5分钟
            'stock_detail': 60,     # 1分钟
            'news': 600,            # 10分钟
            'market_data': 180      # 3分钟
        }
    
    async def get_cached_data(self, key: str, data_type: str):
        """获取缓存数据"""
        if key in self.cache:
            data, timestamp = self.cache[key]
            if time.time() - timestamp < self.cache_ttl[data_type]:
                return data
        return None
    
    def set_cache(self, key: str, data: Any):
        """设置缓存"""
        self.cache[key] = (data, time.time())
```

## 📱 前端状态管理详解

### Redux Store 架构
```typescript
// store/index.ts
export const store = configureStore({
  reducer: {
    auth: authSlice,           // 认证状态
    stocks: stockSlice,        // 股票数据
    news: newsSlice,           // 新闻数据
    watchlist: watchlistSlice, // 自选股
    alerts: alertSlice,        // 预警
    ui: uiSlice               // UI状态
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
});
```

### 异步数据获取
```typescript
// stockSlice.ts
export const fetchStocksAsync = createAsyncThunk(
  'stocks/fetchStocks',
  async (params: StockParams) => {
    // 1. 调用API服务
    const response = await apiService.getStocks(params);
    
    // 2. 数据验证
    if (!response || !Array.isArray(response.data)) {
      throw new Error('Invalid data format');
    }
    
    // 3. 数据转换
    const transformedData = response.data.map(stock => ({
      ...stock,
      pct_change: Number(stock.pct_change),
      volume: Number(stock.volume),
      amount: Number(stock.amount)
    }));
    
    return transformedData;
  }
);
```

## 🔐 认证和安全模块

### JWT认证机制
```python
# auth.py
import jwt
from datetime import datetime, timedelta

class AuthService:
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
        self.algorithm = "HS256"
        self.access_token_expire = timedelta(hours=24)

    def create_access_token(self, user_id: str, username: str):
        """
        创建访问令牌

        1. 设置载荷数据
        """
        payload = {
            "user_id": user_id,
            "username": username,
            "exp": datetime.utcnow() + self.access_token_expire,
            "iat": datetime.utcnow(),
            "type": "access"
        }

        # 2. 生成JWT令牌
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        return token

    def verify_token(self, token: str):
        """
        验证令牌
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="Token expired")
        except jwt.JWTError:
            raise HTTPException(status_code=401, detail="Invalid token")
```

### 权限控制系统
```python
# permissions.py
from enum import Enum
from functools import wraps

class Permission(Enum):
    READ_STOCKS = "read:stocks"
    WRITE_WATCHLIST = "write:watchlist"
    READ_NEWS = "read:news"
    ADMIN_ACCESS = "admin:access"

def require_permission(permission: Permission):
    """
    权限装饰器
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 1. 获取当前用户
            current_user = get_current_user()

            # 2. 检查权限
            if not has_permission(current_user, permission):
                raise HTTPException(status_code=403, detail="Insufficient permissions")

            # 3. 执行原函数
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# 使用示例
@app.get("/api/v1/stocks")
@require_permission(Permission.READ_STOCKS)
async def get_stocks():
    return await stock_service.get_stocks()
```

## 📡 WebSocket实时推送模块

### WebSocket连接管理
```python
# websocket_manager.py
import asyncio
import json
from typing import Dict, List
from fastapi import WebSocket

class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_subscriptions: Dict[str, List[str]] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        """
        建立WebSocket连接
        """
        await websocket.accept()
        self.active_connections[user_id] = websocket
        self.user_subscriptions[user_id] = []

        # 发送连接成功消息
        await self.send_personal_message({
            "type": "connection",
            "status": "connected",
            "message": "WebSocket连接成功"
        }, user_id)

    def disconnect(self, user_id: str):
        """
        断开连接
        """
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        if user_id in self.user_subscriptions:
            del self.user_subscriptions[user_id]

    async def send_personal_message(self, message: dict, user_id: str):
        """
        发送个人消息
        """
        if user_id in self.active_connections:
            websocket = self.active_connections[user_id]
            try:
                await websocket.send_text(json.dumps(message))
            except:
                self.disconnect(user_id)

    async def broadcast_stock_update(self, stock_data: dict):
        """
        广播股票更新
        """
        message = {
            "type": "stock_update",
            "data": stock_data,
            "timestamp": datetime.now().isoformat()
        }

        # 发送给所有订阅该股票的用户
        stock_code = stock_data.get("code")
        for user_id, subscriptions in self.user_subscriptions.items():
            if stock_code in subscriptions:
                await self.send_personal_message(message, user_id)
```

### 实时数据推送服务
```python
# real_time_service.py
class RealTimeDataService:
    def __init__(self, websocket_manager: WebSocketManager):
        self.websocket_manager = websocket_manager
        self.is_running = False

    async def start_real_time_updates(self):
        """
        启动实时数据更新
        """
        self.is_running = True

        while self.is_running:
            try:
                # 1. 获取热门股票实时数据
                hot_stocks = await self.get_hot_stocks_data()

                # 2. 推送股票更新
                for stock in hot_stocks:
                    await self.websocket_manager.broadcast_stock_update(stock)

                # 3. 获取市场指数更新
                market_indices = await self.get_market_indices_data()
                await self.websocket_manager.broadcast_market_update(market_indices)

                # 4. 等待下次更新
                await asyncio.sleep(30)  # 30秒更新一次

            except Exception as e:
                logger.error(f"实时数据推送错误: {e}")
                await asyncio.sleep(60)  # 错误时等待1分钟
```

## 🗄️ 数据库设计详解

### 数据模型设计
```python
# models.py
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

class Stock(Base):
    """股票基础信息表"""
    __tablename__ = "stocks"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(10), unique=True, index=True, comment="股票代码")
    name = Column(String(50), comment="股票名称")
    market = Column(String(10), comment="市场(SH/SZ/BJ)")
    industry = Column(String(50), comment="所属行业")
    list_date = Column(String(20), comment="上市日期")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联关系
    quotes = relationship("StockQuote", back_populates="stock")
    watchlists = relationship("Watchlist", back_populates="stock")

class StockQuote(Base):
    """股票行情数据表"""
    __tablename__ = "stock_quotes"

    id = Column(Integer, primary_key=True, index=True)
    stock_id = Column(Integer, ForeignKey("stocks.id"), index=True)
    trade_date = Column(String(20), index=True, comment="交易日期")
    open_price = Column(Float, comment="开盘价")
    high_price = Column(Float, comment="最高价")
    low_price = Column(Float, comment="最低价")
    close_price = Column(Float, comment="收盘价")
    volume = Column(Integer, comment="成交量")
    amount = Column(Float, comment="成交额")
    pct_change = Column(Float, comment="涨跌幅")
    turnover_rate = Column(Float, comment="换手率")
    pe_ratio = Column(Float, comment="市盈率")
    pb_ratio = Column(Float, comment="市净率")
    created_at = Column(DateTime, default=datetime.utcnow)

    # 关联关系
    stock = relationship("Stock", back_populates="quotes")

class News(Base):
    """新闻信息表"""
    __tablename__ = "news"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), comment="新闻标题")
    summary = Column(Text, comment="新闻摘要")
    content = Column(Text, comment="新闻内容")
    source = Column(String(50), comment="新闻来源")
    author = Column(String(50), comment="作者")
    publish_time = Column(DateTime, comment="发布时间")
    url = Column(String(500), comment="原文链接")
    category = Column(String(50), comment="新闻分类")
    sentiment = Column(String(20), comment="情绪分析结果")
    importance = Column(Integer, comment="重要程度(1-10)")
    view_count = Column(Integer, default=0, comment="阅读量")
    like_count = Column(Integer, default=0, comment="点赞数")
    created_at = Column(DateTime, default=datetime.utcnow)

class User(Base):
    """用户信息表"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, comment="用户名")
    email = Column(String(100), unique=True, index=True, comment="邮箱")
    hashed_password = Column(String(100), comment="加密密码")
    full_name = Column(String(100), comment="真实姓名")
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_superuser = Column(Boolean, default=False, comment="是否超级用户")
    created_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime, comment="最后登录时间")

    # 关联关系
    watchlists = relationship("Watchlist", back_populates="user")
    alerts = relationship("Alert", back_populates="user")

class Watchlist(Base):
    """自选股表"""
    __tablename__ = "watchlists"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    stock_id = Column(Integer, ForeignKey("stocks.id"), index=True)
    added_time = Column(DateTime, default=datetime.utcnow, comment="添加时间")
    notes = Column(Text, comment="备注")

    # 关联关系
    user = relationship("User", back_populates="watchlists")
    stock = relationship("Stock", back_populates="watchlists")

class Alert(Base):
    """预警表"""
    __tablename__ = "alerts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    stock_code = Column(String(10), comment="股票代码")
    alert_type = Column(String(20), comment="预警类型")
    condition = Column(String(20), comment="触发条件")
    target_value = Column(Float, comment="目标值")
    current_value = Column(Float, comment="当前值")
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_triggered = Column(Boolean, default=False, comment="是否已触发")
    triggered_time = Column(DateTime, comment="触发时间")
    created_time = Column(DateTime, default=datetime.utcnow)
    message = Column(Text, comment="预警消息")

    # 关联关系
    user = relationship("User", back_populates="alerts")
```

### 数据库操作服务
```python
# database_service.py
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc

class DatabaseService:
    def __init__(self, db: Session):
        self.db = db

    async def get_stocks_with_quotes(self,
                                   market: str = None,
                                   industry: str = None,
                                   limit: int = 50):
        """
        获取股票及其最新行情
        """
        query = self.db.query(Stock).join(StockQuote)

        # 添加筛选条件
        if market:
            query = query.filter(Stock.market == market)
        if industry:
            query = query.filter(Stock.industry == industry)

        # 获取最新行情
        subquery = self.db.query(
            StockQuote.stock_id,
            func.max(StockQuote.trade_date).label('latest_date')
        ).group_by(StockQuote.stock_id).subquery()

        query = query.join(
            subquery,
            and_(
                StockQuote.stock_id == subquery.c.stock_id,
                StockQuote.trade_date == subquery.c.latest_date
            )
        )

        return query.limit(limit).all()

    async def save_stock_quotes_batch(self, quotes_data: List[Dict]):
        """
        批量保存股票行情数据
        """
        try:
            # 1. 批量插入
            self.db.bulk_insert_mappings(StockQuote, quotes_data)

            # 2. 提交事务
            self.db.commit()

            return True
        except Exception as e:
            # 3. 回滚事务
            self.db.rollback()
            logger.error(f"批量保存行情数据失败: {e}")
            return False

    async def get_user_watchlist(self, user_id: int):
        """
        获取用户自选股
        """
        return self.db.query(Watchlist).join(Stock).filter(
            Watchlist.user_id == user_id
        ).order_by(desc(Watchlist.added_time)).all()
```

## 🧪 测试框架详解

### 单元测试
```python
# tests/test_stock_service.py
import pytest
from unittest.mock import AsyncMock, patch
from app.services.stock_service import StockService

class TestStockService:
    @pytest.fixture
    def stock_service(self):
        return StockService()

    @pytest.mark.asyncio
    async def test_get_stock_list(self, stock_service):
        """
        测试获取股票列表
        """
        # 1. 模拟数据源返回
        mock_data = [
            {
                "code": "000001",
                "name": "平安银行",
                "market": "SZ",
                "current_price": 12.50
            }
        ]

        with patch.object(stock_service.data_provider, 'get_stock_list',
                         return_value=mock_data) as mock_get:
            # 2. 调用服务方法
            result = await stock_service.get_stocks()

            # 3. 验证结果
            assert len(result) == 1
            assert result[0]["code"] == "000001"
            assert result[0]["name"] == "平安银行"
            mock_get.assert_called_once()

    @pytest.mark.asyncio
    async def test_technical_analysis(self, stock_service):
        """
        测试技术分析
        """
        # 1. 准备测试数据
        prices = [10.0, 10.5, 11.0, 10.8, 11.2, 11.5, 11.3, 11.8, 12.0, 11.9]

        # 2. 调用技术分析
        result = await stock_service.calculate_technical_indicators("000001", prices)

        # 3. 验证结果
        assert "ma" in result
        assert "macd" in result
        assert "rsi" in result
        assert result["ma"]["ma5"] > 0
```

### 集成测试
```python
# tests/test_api_integration.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

class TestAPIIntegration:
    def test_get_stocks_endpoint(self):
        """
        测试股票列表API
        """
        response = client.get("/api/v1/stocks?limit=5")

        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert isinstance(data["data"], list)
        assert len(data["data"]) <= 5

    def test_get_news_endpoint(self):
        """
        测试新闻列表API
        """
        response = client.get("/api/v1/news?limit=3")

        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert isinstance(data["data"], list)

        # 验证新闻数据结构
        if data["data"]:
            news_item = data["data"][0]
            assert "title" in news_item
            assert "source" in news_item
            assert "sentiment" in news_item
```

### 性能测试
```python
# tests/test_performance.py
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor

class TestPerformance:
    @pytest.mark.asyncio
    async def test_api_response_time(self):
        """
        测试API响应时间
        """
        start_time = time.time()

        response = client.get("/api/v1/stocks?limit=10")

        end_time = time.time()
        response_time = end_time - start_time

        assert response.status_code == 200
        assert response_time < 2.0  # 响应时间应小于2秒

    def test_concurrent_requests(self):
        """
        测试并发请求
        """
        def make_request():
            return client.get("/api/v1/stocks?limit=5")

        # 并发10个请求
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            results = [future.result() for future in futures]

        # 验证所有请求都成功
        for response in results:
            assert response.status_code == 200
```

## 📊 监控和日志系统

### 日志配置
```python
# logging_config.py
import logging
import sys
from logging.handlers import RotatingFileHandler

def setup_logging():
    """
    配置日志系统
    """
    # 1. 创建日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 2. 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)

    # 3. 文件处理器
    file_handler = RotatingFileHandler(
        'logs/app.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.DEBUG)

    # 4. 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)

    return root_logger
```

### 性能监控
```python
# monitoring.py
import time
import psutil
from functools import wraps

class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'api_calls': 0,
            'total_response_time': 0,
            'error_count': 0
        }

    def monitor_api_call(self, func):
        """
        API调用监控装饰器
        """
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()

            try:
                # 执行API调用
                result = await func(*args, **kwargs)

                # 记录成功指标
                end_time = time.time()
                response_time = end_time - start_time

                self.metrics['api_calls'] += 1
                self.metrics['total_response_time'] += response_time

                # 记录慢查询
                if response_time > 1.0:
                    logger.warning(f"慢查询: {func.__name__} 耗时 {response_time:.2f}s")

                return result

            except Exception as e:
                # 记录错误指标
                self.metrics['error_count'] += 1
                logger.error(f"API调用失败: {func.__name__} - {str(e)}")
                raise

        return wrapper

    def get_system_metrics(self):
        """
        获取系统指标
        """
        return {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent,
            'api_calls': self.metrics['api_calls'],
            'avg_response_time': (
                self.metrics['total_response_time'] / self.metrics['api_calls']
                if self.metrics['api_calls'] > 0 else 0
            ),
            'error_rate': (
                self.metrics['error_count'] / self.metrics['api_calls']
                if self.metrics['api_calls'] > 0 else 0
            )
        }
```

这个详细的技术文档涵盖了股票智能体项目的所有核心模块和算法。每个部分都包含了具体的代码实现、运作原理和数据流程。您觉得还需要补充哪些技术细节吗？
