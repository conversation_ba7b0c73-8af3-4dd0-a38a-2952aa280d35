/**
 * 全局类型定义
 */

// 基础类型
export interface BaseResponse<T = any> {
  data: T;
  message?: string;
  success?: boolean;
}

export interface PaginationResponse<T = any> {
  data: T[];
  total: number;
  skip: number;
  limit: number;
  has_more: boolean;
}

// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  full_name?: string;
  is_active: boolean;
  is_superuser: boolean;
  created_at: string;
  updated_at: string;
  last_login?: string;
}

export interface UserProfile {
  id: string;
  user_id: string;
  avatar_url?: string;
  bio?: string;
  location?: string;
  website?: string;
  phone?: string;
  timezone: string;
  language: string;
  theme: string;
  created_at: string;
  updated_at: string;
}

export interface LoginRequest {
  username: string;
  password: string;
  remember_me?: boolean;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

// 股票相关类型
export interface Stock {
  id: string;
  code: string;
  name: string;
  market: string;
  industry?: string;
  sector?: string;
  list_date?: string;
  delist_date?: string;
  is_active: boolean;
  total_share?: number;
  float_share?: number;
  created_at: string;
  updated_at: string;
}

export interface StockQuote {
  id: string;
  stock_id: string;
  trade_date: string;
  open_price?: number;
  high_price?: number;
  low_price?: number;
  close_price?: number;
  pre_close?: number;
  volume?: number;
  amount?: number;
  turnover_rate?: number;
  change?: number;
  pct_change?: number;
  pe_ratio?: number;
  pb_ratio?: number;
  ps_ratio?: number;
  market_cap?: number;
  float_cap?: number;
  created_at: string;
  updated_at: string;
}

export interface RealtimeQuote {
  id: string;
  stock_id: string;
  current_price?: number;
  open_price?: number;
  high_price?: number;
  low_price?: number;
  pre_close?: number;
  volume?: number;
  amount?: number;
  change?: number;
  pct_change?: number;
  bid1_price?: number;
  bid1_volume?: number;
  ask1_price?: number;
  ask1_volume?: number;
  timestamp: string;
  created_at: string;
}

export interface HotStock {
  code: string;
  name: string;
  market: string;
  industry?: string;
  close_price?: number;
  change?: number;
  pct_change?: number;
  volume?: number;
  amount?: number;
  turnover_rate?: number;
}

// 技术分析相关类型
export interface TechnicalIndicators {
  ma5?: number;
  ma10?: number;
  ma20?: number;
  ma60?: number;
  macd_dif?: number;
  macd_dea?: number;
  macd_histogram?: number;
  rsi6?: number;
  rsi12?: number;
  rsi24?: number;
  kdj_k?: number;
  kdj_d?: number;
  kdj_j?: number;
  boll_upper?: number;
  boll_mid?: number;
  boll_lower?: number;
  volume_ma5?: number;
  volume_ma10?: number;
}

export interface TechnicalAnalysis {
  stock_id: string;
  calculation_date: string;
  data_points: number;
  indicators: TechnicalIndicators;
}

// 新闻相关类型
export interface News {
  id: string;
  title: string;
  summary?: string;
  content?: string;
  source: string;
  author?: string;
  url?: string;
  published_at: string;
  tags: string[];
  related_stocks: string[];
  importance: number;
  sentiment_score?: number;
  sentiment_label?: string;
  view_count: number;
  like_count: number;
  comment_count: number;
  created_at: string;
  updated_at: string;
}

// 预警相关类型
export interface Alert {
  id: string;
  user_id: string;
  name: string;
  stock_code: string;
  alert_type: string;
  condition: Record<string, any>;
  is_active: boolean;
  trigger_count: number;
  last_triggered?: string;
  notification_methods: string[];
  created_at: string;
  updated_at: string;
}

export interface AlertTrigger {
  id: string;
  alert_id: string;
  trigger_value: number;
  trigger_time: string;
  message: string;
  is_read: boolean;
}

// 自选股相关类型
export interface Watchlist {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  is_public: boolean;
  stock_count: number;
  created_at: string;
  updated_at: string;
}

export interface WatchlistItem {
  id: string;
  stock_code: string;
  stock_name?: string;
  market?: string;
  industry?: string;
  notes?: string;
  sort_order: number;
  added_at: string;
  current_price?: number;
  change?: number;
  pct_change?: number;
  volume?: number;
  amount?: number;
}

export interface WatchlistWithItems extends Watchlist {
  items: WatchlistItem[];
}

// WebSocket 消息类型
export interface WebSocketMessage {
  type: string;
  data?: any;
  timestamp: string;
}

export interface StockDataMessage extends WebSocketMessage {
  type: 'stock_data';
  stock_code: string;
  data: RealtimeQuote;
}

export interface NewsDataMessage extends WebSocketMessage {
  type: 'news_data';
  category: string;
  data: News;
}

export interface AlertMessage extends WebSocketMessage {
  type: 'alert';
  data: {
    alert_id: string;
    alert_name: string;
    stock_code: string;
    trigger_value: number;
    message: string;
    trigger_time: string;
    alert_type: string;
  };
}

// 图表数据类型
export interface ChartDataPoint {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface KLineData {
  data: ChartDataPoint[];
  indicators?: TechnicalIndicators[];
}

// 市场概览类型
export interface MarketOverview {
  hot_stocks: HotStock[];
  statistics: {
    total_stocks: number;
    market_distribution: Record<string, number>;
    top_industries: Record<string, number>;
    last_updated: string;
  };
  market_indices: Array<{
    code: string;
    name: string;
    current: number;
    change: number;
    pct_change: number;
  }>;
  market_status: {
    is_trading_day: boolean;
    is_trading_time: boolean;
    session: string;
    current_time: string;
  };
}

// 搜索相关类型
export interface SearchSuggestion {
  code: string;
  name: string;
  market: string;
  industry?: string;
}

export interface SearchResponse {
  query: string;
  suggestions: SearchSuggestion[];
}

// 主题类型
export type ThemeType = 'light' | 'dark';

// 语言类型
export type LanguageType = 'zh-CN' | 'en-US';

// 排序类型
export type SortOrder = 'asc' | 'desc';

// 时间范围类型
export type TimeRange = '1d' | '5d' | '1m' | '3m' | '6m' | '1y' | '2y' | '5y' | 'all';

// 图表类型
export type ChartType = 'kline' | 'line' | 'area';

// 错误类型
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}
