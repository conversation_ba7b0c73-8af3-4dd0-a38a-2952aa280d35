"""
用户服务层
提供用户管理、认证、权限等业务逻辑
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, func
from sqlalchemy.orm import selectinload
from uuid import UUID

from app.models.user import User, UserProfile, Alert, AlertTrigger, Watchlist, WatchlistItem
from app.auth.security import get_password_hash, verify_password, create_access_token, create_refresh_token
from app.core.config import settings

logger = logging.getLogger(__name__)


class UserService:
    """用户服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_user(
        self,
        username: str,
        email: str,
        password: str,
        full_name: Optional[str] = None,
        is_superuser: bool = False
    ) -> User:
        """
        创建用户
        
        Args:
            username: 用户名
            email: 邮箱
            password: 密码
            full_name: 全名
            is_superuser: 是否超级用户
            
        Returns:
            创建的用户对象
        """
        try:
            # 检查用户名是否已存在
            existing_user = await self.get_user_by_username(username)
            if existing_user:
                raise ValueError("用户名已存在")
            
            # 检查邮箱是否已存在
            existing_email = await self.get_user_by_email(email)
            if existing_email:
                raise ValueError("邮箱已存在")
            
            # 创建用户
            hashed_password = get_password_hash(password)
            user = User(
                username=username,
                email=email,
                hashed_password=hashed_password,
                full_name=full_name,
                is_superuser=is_superuser,
                is_active=True
            )
            
            self.db.add(user)
            await self.db.flush()  # 获取用户ID
            
            # 创建用户档案
            profile = UserProfile(
                user_id=user.id,
                timezone="Asia/Shanghai",
                language="zh-CN",
                theme="light"
            )
            
            self.db.add(profile)
            
            # 创建默认自选股
            default_watchlist = Watchlist(
                user_id=user.id,
                name="默认自选股",
                description="系统默认创建的自选股列表",
                is_public=False
            )
            
            self.db.add(default_watchlist)
            await self.db.commit()
            
            logger.info(f"用户创建成功: {username}")
            return user
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建用户失败: {e}")
            raise
    
    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """
        用户认证
        
        Args:
            username: 用户名或邮箱
            password: 密码
            
        Returns:
            认证成功的用户对象，失败返回None
        """
        try:
            # 尝试用户名登录
            user = await self.get_user_by_username(username)
            
            # 如果用户名不存在，尝试邮箱登录
            if not user:
                user = await self.get_user_by_email(username)
            
            if not user:
                return None
            
            # 检查用户是否活跃
            if not user.is_active:
                return None
            
            # 验证密码
            if not verify_password(password, user.hashed_password):
                return None
            
            # 更新最后登录时间
            user.last_login = datetime.utcnow()
            await self.db.commit()
            
            return user
            
        except Exception as e:
            logger.error(f"用户认证失败: {e}")
            return None
    
    async def create_user_tokens(self, user: User) -> Dict[str, Any]:
        """
        创建用户令牌
        
        Args:
            user: 用户对象
            
        Returns:
            包含访问令牌和刷新令牌的字典
        """
        try:
            # 令牌数据
            token_data = {
                "sub": str(user.id),
                "username": user.username,
                "email": user.email,
                "is_superuser": user.is_superuser,
                "permissions": []  # TODO: 实现权限系统
            }
            
            # 创建令牌
            access_token = create_access_token(token_data)
            refresh_token = create_refresh_token({"sub": str(user.id)})
            
            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
            }
            
        except Exception as e:
            logger.error(f"创建用户令牌失败: {e}")
            raise
    
    async def get_user_by_id(self, user_id: UUID) -> Optional[User]:
        """根据ID获取用户"""
        try:
            stmt = select(User).where(User.id == user_id)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        try:
            stmt = select(User).where(User.username == username)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        try:
            stmt = select(User).where(User.email == email)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
    
    async def update_user(
        self,
        user_id: UUID,
        **kwargs
    ) -> Optional[User]:
        """
        更新用户信息
        
        Args:
            user_id: 用户ID
            **kwargs: 更新字段
            
        Returns:
            更新后的用户对象
        """
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                return None
            
            # 更新字段
            for key, value in kwargs.items():
                if hasattr(user, key) and value is not None:
                    setattr(user, key, value)
            
            user.updated_at = datetime.utcnow()
            await self.db.commit()
            
            return user
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新用户失败: {e}")
            raise
    
    async def change_password(
        self,
        user_id: UUID,
        current_password: str,
        new_password: str
    ) -> bool:
        """
        修改密码
        
        Args:
            user_id: 用户ID
            current_password: 当前密码
            new_password: 新密码
            
        Returns:
            是否修改成功
        """
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                return False
            
            # 验证当前密码
            if not verify_password(current_password, user.hashed_password):
                return False
            
            # 更新密码
            user.hashed_password = get_password_hash(new_password)
            user.updated_at = datetime.utcnow()
            await self.db.commit()
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"修改密码失败: {e}")
            return False
    
    async def get_user_profile(self, user_id: UUID) -> Optional[UserProfile]:
        """获取用户档案"""
        try:
            stmt = select(UserProfile).where(UserProfile.user_id == user_id)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取用户档案失败: {e}")
            return None
    
    async def update_user_profile(
        self,
        user_id: UUID,
        **kwargs
    ) -> Optional[UserProfile]:
        """
        更新用户档案
        
        Args:
            user_id: 用户ID
            **kwargs: 更新字段
            
        Returns:
            更新后的用户档案
        """
        try:
            profile = await self.get_user_profile(user_id)
            if not profile:
                # 创建新档案
                profile = UserProfile(user_id=user_id)
                self.db.add(profile)
            
            # 更新字段
            for key, value in kwargs.items():
                if hasattr(profile, key) and value is not None:
                    setattr(profile, key, value)
            
            profile.updated_at = datetime.utcnow()
            await self.db.commit()
            
            return profile
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新用户档案失败: {e}")
            raise
    
    async def get_users_list(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_superuser: Optional[bool] = None
    ) -> Tuple[List[User], int]:
        """
        获取用户列表
        
        Args:
            skip: 跳过数量
            limit: 限制数量
            search: 搜索关键词
            is_active: 是否活跃
            is_superuser: 是否超级用户
            
        Returns:
            (用户列表, 总数)
        """
        try:
            conditions = []
            
            if search:
                search_condition = or_(
                    User.username.ilike(f"%{search}%"),
                    User.email.ilike(f"%{search}%"),
                    User.full_name.ilike(f"%{search}%")
                )
                conditions.append(search_condition)
            
            if is_active is not None:
                conditions.append(User.is_active == is_active)
            
            if is_superuser is not None:
                conditions.append(User.is_superuser == is_superuser)
            
            # 查询总数
            count_stmt = select(func.count(User.id))
            if conditions:
                count_stmt = count_stmt.where(and_(*conditions))
            
            count_result = await self.db.execute(count_stmt)
            total = count_result.scalar()
            
            # 查询数据
            stmt = select(User).order_by(desc(User.created_at)).offset(skip).limit(limit)
            if conditions:
                stmt = stmt.where(and_(*conditions))
            
            result = await self.db.execute(stmt)
            users = result.scalars().all()
            
            return list(users), total
            
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            raise
    
    async def deactivate_user(self, user_id: UUID) -> bool:
        """停用用户"""
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                return False
            
            user.is_active = False
            user.updated_at = datetime.utcnow()
            await self.db.commit()
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"停用用户失败: {e}")
            return False
    
    async def activate_user(self, user_id: UUID) -> bool:
        """激活用户"""
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                return False
            
            user.is_active = True
            user.updated_at = datetime.utcnow()
            await self.db.commit()
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"激活用户失败: {e}")
            return False
    
    async def delete_user(self, user_id: UUID) -> bool:
        """
        删除用户（软删除）
        
        Args:
            user_id: 用户ID
            
        Returns:
            是否删除成功
        """
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                return False
            
            # 软删除：停用用户并标记删除时间
            user.is_active = False
            user.updated_at = datetime.utcnow()
            await self.db.commit()
            
            logger.info(f"用户已删除: {user.username}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除用户失败: {e}")
            return False
    
    async def get_user_statistics(self) -> Dict[str, Any]:
        """
        获取用户统计信息
        
        Returns:
            用户统计数据
        """
        try:
            # 总用户数
            total_stmt = select(func.count(User.id))
            total_result = await self.db.execute(total_stmt)
            total_users = total_result.scalar()
            
            # 活跃用户数
            active_stmt = select(func.count(User.id)).where(User.is_active == True)
            active_result = await self.db.execute(active_stmt)
            active_users = active_result.scalar()
            
            # 今日新用户
            today = datetime.utcnow().date()
            today_stmt = select(func.count(User.id)).where(
                func.date(User.created_at) == today
            )
            today_result = await self.db.execute(today_stmt)
            new_users_today = today_result.scalar()
            
            # 本周新用户
            week_ago = datetime.utcnow() - timedelta(days=7)
            week_stmt = select(func.count(User.id)).where(
                User.created_at >= week_ago
            )
            week_result = await self.db.execute(week_stmt)
            new_users_week = week_result.scalar()
            
            # 本月新用户
            month_ago = datetime.utcnow() - timedelta(days=30)
            month_stmt = select(func.count(User.id)).where(
                User.created_at >= month_ago
            )
            month_result = await self.db.execute(month_stmt)
            new_users_month = month_result.scalar()
            
            return {
                "total_users": total_users,
                "active_users": active_users,
                "inactive_users": total_users - active_users,
                "new_users_today": new_users_today,
                "new_users_week": new_users_week,
                "new_users_month": new_users_month,
                "user_growth_rate": (new_users_month / max(total_users - new_users_month, 1)) * 100,
                "activation_rate": (active_users / max(total_users, 1)) * 100,
                "last_updated": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取用户统计失败: {e}")
            raise
