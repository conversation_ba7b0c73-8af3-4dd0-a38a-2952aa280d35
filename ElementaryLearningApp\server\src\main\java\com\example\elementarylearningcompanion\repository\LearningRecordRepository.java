package com.example.elementarylearningcompanion.repository;

import com.example.elementarylearningcompanion.model.LearningRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface LearningRecordRepository extends JpaRepository<LearningRecord, Long> {
    
    List<LearningRecord> findByStudentIdOrderByCreatedAtDesc(Long studentId);
    
    List<LearningRecord> findByStudentIdAndContentType(Long studentId, String contentType);
    
    List<LearningRecord> findByStudentIdAndCreatedAtBetween(Long studentId, LocalDateTime start, LocalDateTime end);
    
    @Query("SELECT COUNT(lr) FROM LearningRecord lr WHERE lr.studentId = :studentId " +
           "AND lr.contentType = :contentType AND lr.isCorrect = true")
    Long countCorrectAnswers(@Param("studentId") Long studentId, @Param("contentType") String contentType);
    
    @Query("SELECT COUNT(lr) FROM LearningRecord lr WHERE lr.studentId = :studentId " +
           "AND lr.contentType = :contentType AND lr.isCorrect = false")
    Long countWrongAnswers(@Param("studentId") Long studentId, @Param("contentType") String contentType);
    
    @Query("SELECT SUM(lr.timeSpent) FROM LearningRecord lr WHERE lr.studentId = :studentId " +
           "AND lr.createdAt BETWEEN :start AND :end")
    Long getTotalStudyTime(@Param("studentId") Long studentId, 
                          @Param("start") LocalDateTime start, 
                          @Param("end") LocalDateTime end);
}
