package com.example.elementarylearningcompanion.controller;

import com.example.elementarylearningcompanion.dto.ApiResponse;
import com.example.elementarylearningcompanion.model.Student;
import com.example.elementarylearningcompanion.model.Parent;
import com.example.elementarylearningcompanion.service.UserProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/profile")
public class UserProfileController {

    @Autowired
    private UserProfileService userProfileService;

    @GetMapping("/student/{studentId}")
    public ResponseEntity<Student> getStudentProfile(@PathVariable Long studentId) {
        return userProfileService.getStudentProfile(studentId)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/parent/{parentId}")
    public ResponseEntity<Parent> getParentProfile(@PathVariable Long parentId) {
        return userProfileService.getParentProfile(parentId)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/student/{studentId}")
    public ResponseEntity<ApiResponse> updateStudentProfile(
            @PathVariable Long studentId,
            @RequestBody Map<String, Object> request) {
        try {
            String name = (String) request.get("name");
            Integer grade = request.get("grade") != null ? 
                Integer.valueOf(request.get("grade").toString()) : null;
            Integer textbookVersionId = request.get("textbookVersionId") != null ? 
                Integer.valueOf(request.get("textbookVersionId").toString()) : null;

            boolean success = userProfileService.updateStudentProfile(studentId, name, grade, textbookVersionId);
            
            if (success) {
                return ResponseEntity.ok(new ApiResponse(true, "学生信息更新成功"));
            } else {
                return ResponseEntity.badRequest().body(new ApiResponse(false, "更新失败"));
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "更新失败：" + e.getMessage()));
        }
    }

    @PutMapping("/parent/{parentId}")
    public ResponseEntity<ApiResponse> updateParentProfile(
            @PathVariable Long parentId,
            @RequestBody Map<String, Object> request) {
        try {
            String name = (String) request.get("name");
            String phone = (String) request.get("phone");
            String email = (String) request.get("email");

            boolean success = userProfileService.updateParentProfile(parentId, name, phone, email);
            
            if (success) {
                return ResponseEntity.ok(new ApiResponse(true, "家长信息更新成功"));
            } else {
                return ResponseEntity.badRequest().body(new ApiResponse(false, "更新失败"));
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "更新失败：" + e.getMessage()));
        }
    }

    @GetMapping("/student/{studentId}/statistics")
    public ResponseEntity<Map<String, Object>> getStudentStatistics(@PathVariable Long studentId) {
        Map<String, Object> stats = userProfileService.getStudentLearningStatistics(studentId);
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/parent/{parentId}/dashboard")
    public ResponseEntity<Map<String, Object>> getParentDashboard(@PathVariable Long parentId) {
        Map<String, Object> dashboard = userProfileService.getParentDashboard(parentId);
        return ResponseEntity.ok(dashboard);
    }

    @GetMapping("/settings/{userId}")
    public ResponseEntity<Map<String, Object>> getAppSettings(@PathVariable Long userId) {
        Map<String, Object> settings = userProfileService.getAppSettings(userId);
        return ResponseEntity.ok(settings);
    }

    @PutMapping("/settings/{userId}")
    public ResponseEntity<ApiResponse> updateAppSettings(
            @PathVariable Long userId,
            @RequestBody Map<String, Object> settings) {
        boolean success = userProfileService.updateAppSettings(userId, settings);
        
        if (success) {
            return ResponseEntity.ok(new ApiResponse(true, "设置更新成功"));
        } else {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "设置更新失败"));
        }
    }

    @GetMapping("/student/{studentId}/achievements")
    public ResponseEntity<Map<String, Object>> getAchievements(@PathVariable Long studentId) {
        Map<String, Object> achievements = userProfileService.getAchievements(studentId);
        return ResponseEntity.ok(achievements);
    }

    @GetMapping("/grades")
    public ResponseEntity<Map<String, Object>> getAvailableGrades() {
        Map<String, Object> grades = Map.of(
            "grades", Map.of(
                1, "一年级",
                2, "二年级", 
                3, "三年级",
                4, "四年级",
                5, "五年级",
                6, "六年级"
            )
        );
        return ResponseEntity.ok(grades);
    }
}
