"""
股票相关 API 端点
"""

from datetime import date, datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db, get_redis
from app.models.stock import Stock, StockQuote, RealtimeQuote
from app.schemas.stock import (
    StockResponse,
    StockQuoteResponse,
    RealtimeQuoteResponse,
    StockListResponse,
)
from app.services.stock_service import StockService

router = APIRouter()


@router.get("/", response_model=StockListResponse)
async def get_stocks(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    market: Optional[str] = Query(None, description="市场筛选(SH/SZ)"),
    industry: Optional[str] = Query(None, description="行业筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取股票列表
    
    支持分页、筛选和搜索功能
    """
    try:
        stock_service = StockService(db)
        
        stocks, total = await stock_service.get_stocks(
            skip=skip,
            limit=limit,
            market=market,
            industry=industry,
            search=search,
        )
        
        return StockListResponse(
            stocks=stocks,
            total=total,
            skip=skip,
            limit=limit,
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取股票列表失败: {str(e)}")


@router.get("/{stock_code}", response_model=StockResponse)
async def get_stock(
    stock_code: str,
    db: AsyncSession = Depends(get_db),
):
    """
    获取单个股票信息
    """
    try:
        stock_service = StockService(db)
        stock = await stock_service.get_stock_by_code(stock_code)
        
        if not stock:
            raise HTTPException(status_code=404, detail="股票不存在")
        
        return StockResponse.from_orm(stock)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取股票信息失败: {str(e)}")


@router.get("/{stock_code}/quotes", response_model=List[StockQuoteResponse])
async def get_stock_quotes(
    stock_code: str,
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取股票历史行情
    """
    try:
        stock_service = StockService(db)
        
        # 检查股票是否存在
        stock = await stock_service.get_stock_by_code(stock_code)
        if not stock:
            raise HTTPException(status_code=404, detail="股票不存在")
        
        quotes = await stock_service.get_stock_quotes(
            stock_id=stock.id,
            start_date=start_date,
            end_date=end_date,
            limit=limit,
        )
        
        return [StockQuoteResponse.from_orm(quote) for quote in quotes]
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取股票行情失败: {str(e)}")


@router.get("/{stock_code}/realtime", response_model=RealtimeQuoteResponse)
async def get_realtime_quote(
    stock_code: str,
    db: AsyncSession = Depends(get_db),
    redis = Depends(get_redis),
):
    """
    获取股票实时行情
    """
    try:
        stock_service = StockService(db, redis)
        
        # 检查股票是否存在
        stock = await stock_service.get_stock_by_code(stock_code)
        if not stock:
            raise HTTPException(status_code=404, detail="股票不存在")
        
        # 先从缓存获取
        realtime_quote = await stock_service.get_realtime_quote_from_cache(stock_code)
        
        if not realtime_quote:
            # 缓存中没有，从数据库获取最新的
            realtime_quote = await stock_service.get_latest_realtime_quote(stock.id)
            
            if not realtime_quote:
                raise HTTPException(status_code=404, detail="暂无实时行情数据")
        
        return RealtimeQuoteResponse.from_orm(realtime_quote)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取实时行情失败: {str(e)}")


@router.get("/{stock_code}/technical", response_model=dict)
async def get_technical_analysis(
    stock_code: str,
    period: str = Query("daily", description="周期(daily/weekly/monthly)"),
    indicators: Optional[str] = Query(None, description="指标列表,逗号分隔"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取股票技术分析
    """
    try:
        stock_service = StockService(db)
        
        # 检查股票是否存在
        stock = await stock_service.get_stock_by_code(stock_code)
        if not stock:
            raise HTTPException(status_code=404, detail="股票不存在")
        
        # 解析指标列表
        indicator_list = indicators.split(",") if indicators else None
        
        technical_data = await stock_service.get_technical_analysis(
            stock_id=stock.id,
            period=period,
            indicators=indicator_list,
        )
        
        return technical_data
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取技术分析失败: {str(e)}")


@router.post("/{stock_code}/refresh")
async def refresh_stock_data(
    stock_code: str,
    db: AsyncSession = Depends(get_db),
):
    """
    刷新股票数据
    
    触发数据采集任务
    """
    try:
        stock_service = StockService(db)
        
        # 检查股票是否存在
        stock = await stock_service.get_stock_by_code(stock_code)
        if not stock:
            raise HTTPException(status_code=404, detail="股票不存在")
        
        # 触发数据刷新任务
        task_id = await stock_service.trigger_data_refresh(stock_code)
        
        return {
            "message": "数据刷新任务已启动",
            "task_id": task_id,
            "stock_code": stock_code,
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"刷新股票数据失败: {str(e)}")


@router.get("/search/suggest")
async def search_stocks(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(10, ge=1, le=50, description="返回记录数"),
    db: AsyncSession = Depends(get_db),
):
    """
    股票搜索建议
    
    支持股票代码和名称搜索
    """
    try:
        stock_service = StockService(db)
        
        suggestions = await stock_service.search_stocks(q, limit)
        
        return {
            "query": q,
            "suggestions": [
                {
                    "code": stock.code,
                    "name": stock.name,
                    "market": stock.market,
                    "industry": stock.industry,
                }
                for stock in suggestions
            ],
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索股票失败: {str(e)}")


@router.get("/hot/ranking")
async def get_hot_stocks(
    limit: int = Query(20, ge=1, le=100, description="返回记录数"),
    sort_by: str = Query("pct_change", description="排序字段"),
    order: str = Query("desc", description="排序方向(asc/desc)"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取热门股票排行
    
    支持按涨跌幅、成交量、成交额等排序
    """
    try:
        stock_service = StockService(db)
        
        hot_stocks = await stock_service.get_hot_stocks(
            limit=limit,
            sort_by=sort_by,
            order=order,
        )
        
        return {
            "hot_stocks": hot_stocks,
            "sort_by": sort_by,
            "order": order,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取热门股票失败: {str(e)}")
