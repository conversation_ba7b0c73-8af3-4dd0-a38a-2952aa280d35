/**
 * 股票详情页面
 */

import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Row,
  Col,
  Card,
  Typography,
  Space,
  Button,
  Tabs,
  Table,
  Tag,
  Statistic,
  Progress,
  Alert,
  Spin,
  message,
} from 'antd';
import {
  ArrowLeftOutlined,
  StarOutlined,
  StarFilled,
  BellOutlined,
  ShareAltOutlined,
  TrendingUpOutlined,
  Bar<PERSON>hartOutlined,
  LineChartOutlined,
  FileTextOutlined,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '@/store';
import {
  fetchStockDetailAsync,
  fetchStockQuotesAsync,
  fetchTechnicalAnalysisAsync,
  selectStockDetail,
  selectStockDetailLoading,
  selectStockQuotes,
  selectTechnicalAnalysis,
} from '@/store/slices/stockSlice';
import StockPriceDisplay from '@/components/StockPriceDisplay';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const StockDetailPage: React.FC = () => {
  const { code } = useParams<{ code: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  // 状态
  const stockDetail = useAppSelector(selectStockDetail);
  const stockDetailLoading = useAppSelector(selectStockDetailLoading);
  const stockQuotes = useAppSelector(selectStockQuotes);
  const technicalAnalysis = useAppSelector(selectTechnicalAnalysis);
  
  // 本地状态
  const [activeTab, setActiveTab] = useState('overview');
  const [isWatched, setIsWatched] = useState(false);

  // 初始化数据
  useEffect(() => {
    if (code) {
      dispatch(fetchStockDetailAsync(code));
      dispatch(fetchStockQuotesAsync({ code, period: '1d', limit: 100 }));
      dispatch(fetchTechnicalAnalysisAsync({ code, period: 'daily' }));
    }
  }, [dispatch, code]);

  // 处理自选股
  const handleWatchlistToggle = () => {
    // TODO: 实现自选股添加/移除逻辑
    setIsWatched(!isWatched);
    message.success(isWatched ? '已移除自选股' : '已添加到自选股');
  };

  // 处理预警设置
  const handleAlertSetting = () => {
    navigate(`/alerts/create?stock=${code}`);
  };

  // 处理分享
  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href);
    message.success('链接已复制到剪贴板');
  };

  if (stockDetailLoading) {
    return (
      <div className="flex-center" style={{ height: 400 }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!stockDetail) {
    return (
      <Alert
        message="股票不存在"
        description="请检查股票代码是否正确"
        type="error"
        showIcon
      />
    );
  }

  // 技术指标表格列
  const technicalColumns = [
    {
      title: '指标',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '当前值',
      dataIndex: 'current',
      key: 'current',
      render: (value: number) => value?.toFixed(4) || '-',
    },
    {
      title: '信号',
      dataIndex: 'signal',
      key: 'signal',
      render: (signal: string) => {
        const color = signal === 'BUY' ? 'green' : signal === 'SELL' ? 'red' : 'orange';
        return <Tag color={color}>{signal}</Tag>;
      },
    },
    {
      title: '强度',
      dataIndex: 'strength',
      key: 'strength',
      render: (strength: number) => (
        <Progress
          percent={strength * 100}
          size="small"
          strokeColor={strength > 0.6 ? '#52c41a' : strength > 0.3 ? '#faad14' : '#ff4d4f'}
          showInfo={false}
        />
      ),
    },
  ];

  return (
    <div>
      {/* 页面头部 */}
      <div className="flex-between" style={{ marginBottom: 24 }}>
        <div className="flex-center">
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate(-1)}
            style={{ marginRight: 16 }}
          >
            返回
          </Button>
          <div>
            <Title level={2} style={{ margin: 0 }}>
              {stockDetail.name} ({stockDetail.code})
            </Title>
            <Space>
              <Tag color="blue">{stockDetail.market}</Tag>
              <Tag>{stockDetail.industry}</Tag>
            </Space>
          </div>
        </div>
        
        <Space>
          <Button
            icon={isWatched ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
            onClick={handleWatchlistToggle}
          >
            {isWatched ? '已关注' : '关注'}
          </Button>
          <Button
            icon={<BellOutlined />}
            onClick={handleAlertSetting}
          >
            设置预警
          </Button>
          <Button
            icon={<ShareAltOutlined />}
            onClick={handleShare}
          >
            分享
          </Button>
        </Space>
      </div>

      {/* 股价信息 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} sm={12} md={8}>
            <div>
              <Text type="secondary">当前价格</Text>
              <div style={{ marginTop: 8 }}>
                <StockPriceDisplay
                  price={stockDetail.close_price}
                  change={stockDetail.change}
                  pctChange={stockDetail.pct_change}
                  size="large"
                />
              </div>
            </div>
          </Col>
          
          <Col xs={12} sm={6} md={4}>
            <Statistic
              title="今开"
              value={stockDetail.open_price}
              precision={2}
              valueStyle={{ fontSize: 16 }}
            />
          </Col>
          
          <Col xs={12} sm={6} md={4}>
            <Statistic
              title="最高"
              value={stockDetail.high_price}
              precision={2}
              valueStyle={{ fontSize: 16, color: '#cf1322' }}
            />
          </Col>
          
          <Col xs={12} sm={6} md={4}>
            <Statistic
              title="最低"
              value={stockDetail.low_price}
              precision={2}
              valueStyle={{ fontSize: 16, color: '#3f8600' }}
            />
          </Col>
          
          <Col xs={12} sm={6} md={4}>
            <Statistic
              title="成交量"
              value={stockDetail.volume}
              formatter={(value) => `${(Number(value) / 10000).toFixed(1)}万`}
              valueStyle={{ fontSize: 16 }}
            />
          </Col>
          
          <Col xs={12} sm={6} md={4}>
            <Statistic
              title="成交额"
              value={stockDetail.amount}
              formatter={(value) => `${(Number(value) / 100000000).toFixed(2)}亿`}
              valueStyle={{ fontSize: 16 }}
            />
          </Col>
        </Row>
      </Card>

      {/* 详细信息标签页 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={<span><BarChartOutlined />概览</span>} key="overview">
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={12}>
              <Card title="基本信息">
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Text type="secondary">市盈率(PE)</Text>
                    <div>
                      <Text strong style={{ fontSize: 16 }}>
                        {stockDetail.pe_ratio?.toFixed(2) || '-'}
                      </Text>
                    </div>
                  </Col>
                  <Col span={12}>
                    <Text type="secondary">市净率(PB)</Text>
                    <div>
                      <Text strong style={{ fontSize: 16 }}>
                        {stockDetail.pb_ratio?.toFixed(2) || '-'}
                      </Text>
                    </div>
                  </Col>
                  <Col span={12}>
                    <Text type="secondary">换手率</Text>
                    <div>
                      <Text strong style={{ fontSize: 16 }}>
                        {stockDetail.turnover_rate?.toFixed(2)}%
                      </Text>
                    </div>
                  </Col>
                  <Col span={12}>
                    <Text type="secondary">总市值</Text>
                    <div>
                      <Text strong style={{ fontSize: 16 }}>
                        {stockDetail.market_cap ? 
                          `${(stockDetail.market_cap / 100000000).toFixed(0)}亿` : '-'
                        }
                      </Text>
                    </div>
                  </Col>
                </Row>
              </Card>
            </Col>
            
            <Col xs={24} lg={12}>
              <Card title="公司信息">
                <div style={{ lineHeight: '1.8' }}>
                  <Text type="secondary">公司全称：</Text>
                  <Text>{stockDetail.full_name || '-'}</Text>
                  <br />
                  <Text type="secondary">所属行业：</Text>
                  <Text>{stockDetail.industry || '-'}</Text>
                  <br />
                  <Text type="secondary">上市日期：</Text>
                  <Text>{stockDetail.list_date || '-'}</Text>
                  <br />
                  <Text type="secondary">主营业务：</Text>
                  <Text>{stockDetail.business_scope || '暂无信息'}</Text>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab={<span><LineChartOutlined />技术分析</span>} key="technical">
          <Card title="技术指标">
            {technicalAnalysis ? (
              <Table
                columns={technicalColumns}
                dataSource={technicalAnalysis.indicators || []}
                pagination={false}
                size="small"
              />
            ) : (
              <div className="text-center p-3">
                <Text type="secondary">正在加载技术分析数据...</Text>
              </div>
            )}
          </Card>
        </TabPane>

        <TabPane tab={<span><FileTextOutlined />相关新闻</span>} key="news">
          <Card>
            <div className="text-center p-3">
              <Text type="secondary">正在加载相关新闻...</Text>
            </div>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default StockDetailPage;
