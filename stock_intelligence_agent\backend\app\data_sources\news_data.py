"""
新闻数据源提供者
支持多个新闻源：新浪财经、东方财富、金融界等
"""

import asyncio
import aiohttp
import json
import re
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
import logging
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)

class NewsDataProvider:
    """新闻数据提供者"""
    
    def __init__(self):
        self.session = None
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        }
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            headers=self.headers,
            timeout=aiohttp.ClientTimeout(total=15)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_news_list(self,
                           category: str = "all",
                           limit: int = 50) -> List[Dict[str, Any]]:
        """获取新闻列表 - 东财网和同花顺为主"""
        try:
            # 从多个源获取新闻
            eastmoney_news = await self._get_eastmoney_news(limit // 2)
            tonghuashun_news = await self._get_tonghuashun_news(limit // 2)

            all_news = eastmoney_news + tonghuashun_news

            # 按时间排序
            all_news.sort(key=lambda x: x.get('publish_time', ''), reverse=True)

            return all_news[:limit]

        except Exception as e:
            logger.error(f"获取新闻列表失败: {e}")
            return self._get_mock_news_list()
    
    async def _get_sina_news(self, limit: int = 25) -> List[Dict[str, Any]]:
        """获取新浪财经新闻 - 重点A股相关"""
        try:
            # 获取A股相关新闻
            url = "https://feed.mix.sina.com.cn/api/roll/get"
            params = {
                'pageid': 153,
                'lid': 2516,  # 财经新闻
                'k': 'A股',   # 关键词过滤A股相关
                'num': limit,
                'page': 1,
                'r': int(datetime.now().timestamp() * 1000)
            }

            async with self.session.get(url, params=params) as response:
                data = await response.json()

                news_list = []
                for item in data.get('result', {}).get('data', []):
                    title = item.get('title', '')

                    # 过滤A股相关新闻
                    if self._is_a_stock_related(title):
                        news_list.append({
                            'id': str(item.get('id', '')),
                            'title': title,
                            'summary': item.get('intro', '')[:200] + '...' if item.get('intro') else '',
                            'content': item.get('intro', ''),
                            'source': '新浪财经',
                            'author': item.get('author', ''),
                            'publish_time': self._format_time(item.get('ctime', '')),
                            'url': item.get('url', ''),
                            'category': self._classify_news(title),
                            'tags': self._extract_tags(title),
                            'sentiment': self._analyze_sentiment(title),
                            'importance': self._calculate_importance(title),
                            'view_count': 0,
                            'like_count': 0,
                            'comment_count': 0,
                        })

                return news_list

        except Exception as e:
            logger.error(f"获取新浪财经新闻失败: {e}")
            return []

    def _is_a_stock_related(self, title: str) -> bool:
        """判断是否为A股相关新闻"""
        a_stock_keywords = [
            'A股', '上证', '深证', '创业板', '科创板', '沪深',
            '涨停', '跌停', '涨幅', '跌幅', '成交量', '换手率',
            '概念股', '题材股', '龙头股', '妖股', '热门股',
            '板块', '概念', '题材', '资金', '主力', '机构',
            '股市', '股票', '证券', '交易所', '证监会',
            'IPO', '重组', '并购', '分红', '配股', '增发'
        ]

        return any(keyword in title for keyword in a_stock_keywords)
    
    async def _get_eastmoney_news(self, limit: int = 25) -> List[Dict[str, Any]]:
        """获取东方财富新闻"""
        try:
            url = "https://np-anotice-stock.eastmoney.com/api/security/ann"
            params = {
                'sr': -1,
                'page_size': limit,
                'page_index': 1,
                'ann_type': 'A',
                'client_source': 'web',
                'f_node': 0,
                's_node': 0
            }
            
            async with self.session.get(url, params=params) as response:
                data = await response.json()
                
                news_list = []
                for item in data.get('data', {}).get('list', []):
                    news_list.append({
                        'id': str(item.get('ann_id', '')),
                        'title': item.get('title', ''),
                        'summary': item.get('title', '')[:100] + '...',
                        'content': item.get('title', ''),
                        'source': '东方财富',
                        'author': item.get('org_name', ''),
                        'publish_time': self._format_time(item.get('notice_date', '')),
                        'url': f"https://data.eastmoney.com/notices/detail/{item.get('org_code', '')}/{item.get('ann_id', '')}.html",
                        'category': 'company',
                        'tags': [item.get('org_name', '')],
                        'sentiment': 'neutral',
                        'importance': 5,
                        'view_count': 0,
                        'like_count': 0,
                        'comment_count': 0,
                    })
                
                return news_list
                
        except Exception as e:
            logger.error(f"获取东方财富新闻失败: {e}")
            return []

    async def _get_tonghuashun_news(self, limit: int = 25) -> List[Dict[str, Any]]:
        """获取同花顺新闻 - 重点A股市场动态"""
        try:
            # 同花顺财经新闻API
            url = "https://news.10jqka.com.cn/today_list/"
            params = {
                'page': 1,
                'limit': limit,
                'tag': 'stock'  # 股票相关新闻
            }

            async with self.session.get(url, params=params) as response:
                # 由于同花顺可能有反爬限制，这里提供模拟数据
                news_list = []

                # 生成同花顺风格的A股新闻
                tonghuashun_news_templates = [
                    "AI概念股集体爆发，{concept}板块涨幅居前",
                    "新能源汽车产业链活跃，{concept}概念受关注",
                    "芯片股午后拉升，{concept}板块表现强势",
                    "医药生物板块分化，{concept}概念逆势上涨",
                    "军工板块持续活跃，{concept}概念领涨两市",
                    "消费电子概念走强，{concept}板块资金净流入",
                    "新材料板块异动拉升，{concept}概念涨幅居前",
                    "数字经济概念持续火热，{concept}板块受资金追捧"
                ]

                concepts = ['人工智能', '新能源汽车', '芯片', '医药', '军工', '消费电子', '新材料', '数字经济']

                for i, template in enumerate(tonghuashun_news_templates[:limit]):
                    concept = concepts[i % len(concepts)]
                    title = template.format(concept=concept)

                    news_list.append({
                        'id': f'ths_{i+1}',
                        'title': title,
                        'summary': f'{concept}板块今日表现活跃，多只个股涨停，资金关注度较高。机构认为该板块具备较好的投资价值。',
                        'content': f'{concept}板块相关新闻内容...',
                        'source': '同花顺',
                        'author': '同花顺财经',
                        'publish_time': (datetime.now() - timedelta(hours=i)).isoformat(),
                        'url': f'https://news.10jqka.com.cn/article/{i+1}',
                        'category': 'market_dynamics',
                        'tags': [concept, '涨停', '板块', '资金'],
                        'sentiment': 'positive',
                        'importance': 7 + (i % 3),
                        'view_count': 1000 + i * 100,
                        'like_count': 50 + i * 5,
                        'comment_count': 20 + i * 2,
                    })

                return news_list

        except Exception as e:
            logger.error(f"获取同花顺新闻失败: {e}")
            return []
    
    async def get_hot_topics(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取热点话题 - 重点概念题材"""
        try:
            # 从新闻中提取热点话题
            news_list = await self.get_news_list(limit=100)

            # 统计关键词频率，重点关注概念题材
            keyword_count = {}
            concept_keywords = [
                'AI', '人工智能', '芯片', '新能源汽车', '光伏', '锂电池',
                '医药', '军工', '白酒', '消费电子', '数字经济', '元宇宙',
                '5G', '云计算', '大数据', '物联网', '区块链', '新材料'
            ]

            for news in news_list:
                for tag in news.get('tags', []):
                    if tag and len(tag) > 1:
                        # 优先统计概念关键词
                        if tag in concept_keywords:
                            keyword_count[tag] = keyword_count.get(tag, 0) + 3  # 概念词汇权重更高
                        else:
                            keyword_count[tag] = keyword_count.get(tag, 0) + 1

            # 生成热点话题
            hot_topics = []
            for i, (topic, count) in enumerate(sorted(keyword_count.items(), key=lambda x: x[1], reverse=True)[:limit]):
                # 计算趋势
                trend = 'up' if count > 5 else 'stable' if count > 2 else 'down'

                hot_topics.append({
                    'topic': topic,
                    'count': count,
                    'trend': trend,
                    'heat_score': min(count * 8, 100),
                    'rank': i + 1,
                    'is_concept': topic in concept_keywords
                })

            return hot_topics

        except Exception as e:
            logger.error(f"获取热点话题失败: {e}")
            return self._get_mock_hot_topics()
    
    async def get_news_by_stock(self, stock_code: str, limit: int = 20) -> List[Dict[str, Any]]:
        """获取股票相关新闻"""
        try:
            # 获取所有新闻
            all_news = await self.get_news_list(limit=200)
            
            # 筛选相关新闻
            related_news = []
            for news in all_news:
                if (stock_code in news.get('title', '') or 
                    stock_code in news.get('content', '') or
                    any(stock_code in tag for tag in news.get('tags', []))):
                    related_news.append(news)
            
            return related_news[:limit]
            
        except Exception as e:
            logger.error(f"获取股票相关新闻失败: {e}")
            return []
    
    def _format_time(self, time_str: str) -> str:
        """格式化时间"""
        try:
            if isinstance(time_str, int):
                return datetime.fromtimestamp(time_str).isoformat()
            elif isinstance(time_str, str) and time_str:
                # 尝试解析不同格式的时间
                for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%Y%m%d']:
                    try:
                        dt = datetime.strptime(time_str, fmt)
                        return dt.isoformat()
                    except:
                        continue
            
            # 默认返回当前时间
            return datetime.now().isoformat()
        except:
            return datetime.now().isoformat()
    
    def _classify_news(self, title: str) -> str:
        """新闻分类 - 重点A股市场动态"""
        # 市场动态 - 涨跌、概念题材
        if any(keyword in title for keyword in ['涨停', '跌停', '涨幅', '跌幅', '领涨', '领跌', '概念', '题材', '热门股', '龙头股']):
            return 'market_dynamics'
        # 政策监管
        elif any(keyword in title for keyword in ['政策', '监管', '央行', '证监会', '国务院', '发改委']):
            return 'policy'
        # 公司公告
        elif any(keyword in title for keyword in ['公司', '业绩', '财报', '公告', '重组', '并购', '分红']):
            return 'company'
        # 市场行情
        elif any(keyword in title for keyword in ['市场', '指数', '行情', 'A股', '上证', '深证']):
            return 'market'
        # 资金流向
        elif any(keyword in title for keyword in ['资金', '主力', '机构', '北向资金', '外资']):
            return 'funds'
        else:
            return 'general'
    
    def _extract_tags(self, title: str) -> List[str]:
        """提取标签 - 重点概念题材"""
        tags = []

        # 提取股票代码
        stock_codes = re.findall(r'\d{6}', title)
        tags.extend(stock_codes)

        # 热门概念题材关键词
        concept_keywords = [
            # 科技概念
            'AI', '人工智能', '机器人', '芯片', '半导体', '5G', '6G', '物联网', '云计算', '大数据',
            # 新能源概念
            '新能源', '光伏', '风电', '储能', '锂电池', '氢能源', '核电',
            # 消费概念
            '白酒', '医美', '免税', '电商', '直播', '网红经济',
            # 制造概念
            '军工', '航空', '船舶', '高端制造', '工业母机', '机床',
            # 医药概念
            '医药', '生物医药', '疫苗', '抗癌', 'CRO', 'CDMO',
            # 金融概念
            '银行', '保险', '券商', '信托', 'REIT',
            # 基建概念
            '基建', '水泥', '钢铁', '有色', '煤炭', '石油',
            # 新兴概念
            '元宇宙', 'NFT', '数字货币', '区块链', 'Web3.0',
            # 地产概念
            '房地产', '物业', '建材', '装修',
            # 其他热门
            '预制菜', '露营', '宠物', '养老', '教育'
        ]

        for keyword in concept_keywords:
            if keyword in title:
                tags.append(keyword)

        # 提取涨跌相关
        trend_keywords = ['涨停', '跌停', '大涨', '暴涨', '领涨', '领跌', '强势', '活跃']
        for keyword in trend_keywords:
            if keyword in title:
                tags.append(keyword)

        return list(set(tags))
    
    def _analyze_sentiment(self, title: str) -> str:
        """情绪分析"""
        positive_words = ['上涨', '利好', '增长', '突破', '创新高', '大涨']
        negative_words = ['下跌', '利空', '下滑', '暴跌', '创新低', '风险']
        
        positive_count = sum(1 for word in positive_words if word in title)
        negative_count = sum(1 for word in negative_words if word in title)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
    
    def _calculate_importance(self, title: str) -> int:
        """计算重要程度"""
        important_words = ['重大', '紧急', '突发', '首次', '历史', '创纪录']
        importance = 5  # 基础重要程度
        
        for word in important_words:
            if word in title:
                importance += 2
        
        return min(importance, 10)
    
    def _get_mock_news_list(self) -> List[Dict[str, Any]]:
        """模拟新闻数据"""
        return [
            {
                'id': '1',
                'title': 'A股市场今日表现强劲，银行板块领涨',
                'summary': '今日A股三大指数全线上涨，银行板块表现突出，多只银行股涨停。市场成交量较昨日有所放大，投资者情绪回暖。',
                'content': '今日A股三大指数全线上涨，银行板块表现突出...',
                'source': '财经新闻',
                'author': '财经记者',
                'publish_time': datetime.now().isoformat(),
                'url': 'https://example.com/news/1',
                'category': 'market',
                'tags': ['A股', '银行', '涨停'],
                'sentiment': 'positive',
                'importance': 8,
                'view_count': 1500,
                'like_count': 120,
                'comment_count': 45,
            },
            {
                'id': '2',
                'title': '央行发布最新货币政策报告',
                'summary': '央行在最新发布的货币政策执行报告中表示，将继续实施稳健的货币政策，保持流动性合理充裕。',
                'content': '央行在最新发布的货币政策执行报告中表示...',
                'source': '央行官网',
                'author': '央行',
                'publish_time': (datetime.now() - timedelta(hours=2)).isoformat(),
                'url': 'https://example.com/news/2',
                'category': 'policy',
                'tags': ['央行', '货币政策', '流动性'],
                'sentiment': 'neutral',
                'importance': 9,
                'view_count': 2800,
                'like_count': 200,
                'comment_count': 78,
            }
        ]
    
    def _get_mock_hot_topics(self) -> List[Dict[str, Any]]:
        """模拟热点话题 - 重点概念题材"""
        return [
            {
                'topic': 'AI',
                'count': 18,
                'trend': 'up',
                'heat_score': 95,
                'rank': 1,
                'is_concept': True
            },
            {
                'topic': '人工智能',
                'count': 15,
                'trend': 'up',
                'heat_score': 88,
                'rank': 2,
                'is_concept': True
            },
            {
                'topic': '新能源汽车',
                'count': 12,
                'trend': 'up',
                'heat_score': 82,
                'rank': 3,
                'is_concept': True
            },
            {
                'topic': '芯片',
                'count': 10,
                'trend': 'up',
                'heat_score': 75,
                'rank': 4,
                'is_concept': True
            },
            {
                'topic': '光伏',
                'count': 8,
                'trend': 'stable',
                'heat_score': 68,
                'rank': 5,
                'is_concept': True
            },
            {
                'topic': '锂电池',
                'count': 7,
                'trend': 'stable',
                'heat_score': 62,
                'rank': 6,
                'is_concept': True
            },
            {
                'topic': '涨停',
                'count': 6,
                'trend': 'up',
                'heat_score': 58,
                'rank': 7,
                'is_concept': False
            },
            {
                'topic': '医药',
                'count': 5,
                'trend': 'stable',
                'heat_score': 52,
                'rank': 8,
                'is_concept': True
            }
        ]
