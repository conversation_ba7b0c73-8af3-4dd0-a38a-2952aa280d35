"""
新闻数据源提供者
支持多个新闻源：新浪财经、东方财富、金融界等
"""

import asyncio
import aiohttp
import json
import re
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
import logging
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)

class NewsDataProvider:
    """新闻数据提供者"""
    
    def __init__(self):
        self.session = None
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        }
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            headers=self.headers,
            timeout=aiohttp.ClientTimeout(total=15)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_news_list(self, 
                           category: str = "all", 
                           limit: int = 50) -> List[Dict[str, Any]]:
        """获取新闻列表"""
        try:
            # 从多个源获取新闻
            sina_news = await self._get_sina_news(limit // 2)
            eastmoney_news = await self._get_eastmoney_news(limit // 2)
            
            all_news = sina_news + eastmoney_news
            
            # 按时间排序
            all_news.sort(key=lambda x: x.get('publish_time', ''), reverse=True)
            
            return all_news[:limit]
            
        except Exception as e:
            logger.error(f"获取新闻列表失败: {e}")
            return self._get_mock_news_list()
    
    async def _get_sina_news(self, limit: int = 25) -> List[Dict[str, Any]]:
        """获取新浪财经新闻"""
        try:
            url = "https://feed.mix.sina.com.cn/api/roll/get"
            params = {
                'pageid': 153,
                'lid': 2516,
                'k': '',
                'num': limit,
                'page': 1,
                'r': int(datetime.now().timestamp() * 1000)
            }
            
            async with self.session.get(url, params=params) as response:
                data = await response.json()
                
                news_list = []
                for item in data.get('result', {}).get('data', []):
                    news_list.append({
                        'id': str(item.get('id', '')),
                        'title': item.get('title', ''),
                        'summary': item.get('intro', '')[:200] + '...' if item.get('intro') else '',
                        'content': item.get('intro', ''),
                        'source': '新浪财经',
                        'author': item.get('author', ''),
                        'publish_time': self._format_time(item.get('ctime', '')),
                        'url': item.get('url', ''),
                        'category': self._classify_news(item.get('title', '')),
                        'tags': self._extract_tags(item.get('title', '')),
                        'sentiment': self._analyze_sentiment(item.get('title', '')),
                        'importance': self._calculate_importance(item.get('title', '')),
                        'view_count': 0,
                        'like_count': 0,
                        'comment_count': 0,
                    })
                
                return news_list
                
        except Exception as e:
            logger.error(f"获取新浪财经新闻失败: {e}")
            return []
    
    async def _get_eastmoney_news(self, limit: int = 25) -> List[Dict[str, Any]]:
        """获取东方财富新闻"""
        try:
            url = "https://np-anotice-stock.eastmoney.com/api/security/ann"
            params = {
                'sr': -1,
                'page_size': limit,
                'page_index': 1,
                'ann_type': 'A',
                'client_source': 'web',
                'f_node': 0,
                's_node': 0
            }
            
            async with self.session.get(url, params=params) as response:
                data = await response.json()
                
                news_list = []
                for item in data.get('data', {}).get('list', []):
                    news_list.append({
                        'id': str(item.get('ann_id', '')),
                        'title': item.get('title', ''),
                        'summary': item.get('title', '')[:100] + '...',
                        'content': item.get('title', ''),
                        'source': '东方财富',
                        'author': item.get('org_name', ''),
                        'publish_time': self._format_time(item.get('notice_date', '')),
                        'url': f"https://data.eastmoney.com/notices/detail/{item.get('org_code', '')}/{item.get('ann_id', '')}.html",
                        'category': 'company',
                        'tags': [item.get('org_name', '')],
                        'sentiment': 'neutral',
                        'importance': 5,
                        'view_count': 0,
                        'like_count': 0,
                        'comment_count': 0,
                    })
                
                return news_list
                
        except Exception as e:
            logger.error(f"获取东方财富新闻失败: {e}")
            return []
    
    async def get_hot_topics(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取热点话题"""
        try:
            # 从新闻中提取热点话题
            news_list = await self.get_news_list(limit=100)
            
            # 统计关键词频率
            keyword_count = {}
            for news in news_list:
                for tag in news.get('tags', []):
                    if tag and len(tag) > 1:
                        keyword_count[tag] = keyword_count.get(tag, 0) + 1
            
            # 生成热点话题
            hot_topics = []
            for i, (topic, count) in enumerate(sorted(keyword_count.items(), key=lambda x: x[1], reverse=True)[:limit]):
                hot_topics.append({
                    'topic': topic,
                    'count': count,
                    'trend': 'up' if count > 3 else 'stable',
                    'heat_score': min(count * 10, 100),
                })
            
            return hot_topics
            
        except Exception as e:
            logger.error(f"获取热点话题失败: {e}")
            return self._get_mock_hot_topics()
    
    async def get_news_by_stock(self, stock_code: str, limit: int = 20) -> List[Dict[str, Any]]:
        """获取股票相关新闻"""
        try:
            # 获取所有新闻
            all_news = await self.get_news_list(limit=200)
            
            # 筛选相关新闻
            related_news = []
            for news in all_news:
                if (stock_code in news.get('title', '') or 
                    stock_code in news.get('content', '') or
                    any(stock_code in tag for tag in news.get('tags', []))):
                    related_news.append(news)
            
            return related_news[:limit]
            
        except Exception as e:
            logger.error(f"获取股票相关新闻失败: {e}")
            return []
    
    def _format_time(self, time_str: str) -> str:
        """格式化时间"""
        try:
            if isinstance(time_str, int):
                return datetime.fromtimestamp(time_str).isoformat()
            elif isinstance(time_str, str) and time_str:
                # 尝试解析不同格式的时间
                for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%Y%m%d']:
                    try:
                        dt = datetime.strptime(time_str, fmt)
                        return dt.isoformat()
                    except:
                        continue
            
            # 默认返回当前时间
            return datetime.now().isoformat()
        except:
            return datetime.now().isoformat()
    
    def _classify_news(self, title: str) -> str:
        """新闻分类"""
        if any(keyword in title for keyword in ['政策', '监管', '央行', '证监会']):
            return 'policy'
        elif any(keyword in title for keyword in ['公司', '业绩', '财报', '公告']):
            return 'company'
        elif any(keyword in title for keyword in ['市场', '指数', '涨跌', '行情']):
            return 'market'
        else:
            return 'general'
    
    def _extract_tags(self, title: str) -> List[str]:
        """提取标签"""
        tags = []
        
        # 提取股票代码
        stock_codes = re.findall(r'\d{6}', title)
        tags.extend(stock_codes)
        
        # 提取关键词
        keywords = ['银行', '地产', '科技', '医药', '新能源', '芯片', '5G', 'AI', '区块链']
        for keyword in keywords:
            if keyword in title:
                tags.append(keyword)
        
        return list(set(tags))
    
    def _analyze_sentiment(self, title: str) -> str:
        """情绪分析"""
        positive_words = ['上涨', '利好', '增长', '突破', '创新高', '大涨']
        negative_words = ['下跌', '利空', '下滑', '暴跌', '创新低', '风险']
        
        positive_count = sum(1 for word in positive_words if word in title)
        negative_count = sum(1 for word in negative_words if word in title)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
    
    def _calculate_importance(self, title: str) -> int:
        """计算重要程度"""
        important_words = ['重大', '紧急', '突发', '首次', '历史', '创纪录']
        importance = 5  # 基础重要程度
        
        for word in important_words:
            if word in title:
                importance += 2
        
        return min(importance, 10)
    
    def _get_mock_news_list(self) -> List[Dict[str, Any]]:
        """模拟新闻数据"""
        return [
            {
                'id': '1',
                'title': 'A股市场今日表现强劲，银行板块领涨',
                'summary': '今日A股三大指数全线上涨，银行板块表现突出，多只银行股涨停。市场成交量较昨日有所放大，投资者情绪回暖。',
                'content': '今日A股三大指数全线上涨，银行板块表现突出...',
                'source': '财经新闻',
                'author': '财经记者',
                'publish_time': datetime.now().isoformat(),
                'url': 'https://example.com/news/1',
                'category': 'market',
                'tags': ['A股', '银行', '涨停'],
                'sentiment': 'positive',
                'importance': 8,
                'view_count': 1500,
                'like_count': 120,
                'comment_count': 45,
            },
            {
                'id': '2',
                'title': '央行发布最新货币政策报告',
                'summary': '央行在最新发布的货币政策执行报告中表示，将继续实施稳健的货币政策，保持流动性合理充裕。',
                'content': '央行在最新发布的货币政策执行报告中表示...',
                'source': '央行官网',
                'author': '央行',
                'publish_time': (datetime.now() - timedelta(hours=2)).isoformat(),
                'url': 'https://example.com/news/2',
                'category': 'policy',
                'tags': ['央行', '货币政策', '流动性'],
                'sentiment': 'neutral',
                'importance': 9,
                'view_count': 2800,
                'like_count': 200,
                'comment_count': 78,
            }
        ]
    
    def _get_mock_hot_topics(self) -> List[Dict[str, Any]]:
        """模拟热点话题"""
        return [
            {
                'topic': '银行股',
                'count': 15,
                'trend': 'up',
                'heat_score': 85,
            },
            {
                'topic': '新能源',
                'count': 12,
                'trend': 'up',
                'heat_score': 78,
            },
            {
                'topic': '芯片',
                'count': 8,
                'trend': 'stable',
                'heat_score': 65,
            }
        ]
