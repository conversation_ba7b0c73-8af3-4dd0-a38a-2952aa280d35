package com.example.elementarylearningcompanion.service;

import com.example.elementarylearningcompanion.model.*;
import com.example.elementarylearningcompanion.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.util.HashMap;

@Service
public class TestService {

    @Autowired
    private QuestionRepository questionRepository;

    @Autowired
    private TestPaperRepository testPaperRepository;

    @Autowired
    private StudentAnswerRepository studentAnswerRepository;

    @Autowired
    private WrongQuestionRepository wrongQuestionRepository;

    public List<Question> getQuestionsBySubject(Integer textbookVersionId, Integer grade, String subject) {
        return questionRepository.findByTextbookVersionIdAndGradeAndSubject(textbookVersionId, grade, subject);
    }

    public List<Question> getRandomQuestions(Integer textbookVersionId, Integer grade, String subject, Integer count) {
        return questionRepository.findRandomQuestions(textbookVersionId, grade, subject, count);
    }

    public List<Question> getRandomQuestionsByTypeAndDifficulty(Integer textbookVersionId, Integer grade, 
                                                               String subject, String questionType, 
                                                               Integer difficultyLevel, Integer count) {
        return questionRepository.findRandomQuestionsByTypeAndDifficulty(
            textbookVersionId, grade, subject, questionType, difficultyLevel, count);
    }

    public List<String> getAvailableQuestionTypes(Integer textbookVersionId, Integer grade, String subject) {
        return questionRepository.findDistinctQuestionTypes(textbookVersionId, grade, subject);
    }

    public List<TestPaper> getActiveTestPapers(Integer textbookVersionId, Integer grade, String subject) {
        return testPaperRepository.findByTextbookVersionIdAndGradeAndSubjectAndIsActiveTrue(
            textbookVersionId, grade, subject);
    }

    public Optional<TestPaper> getTestPaper(Long testPaperId) {
        return testPaperRepository.findById(testPaperId);
    }

    @Transactional
    public boolean submitAnswer(Long studentId, Long questionId, String answer, Integer timeSpent, Long testPaperId) {
        Optional<Question> questionOpt = questionRepository.findById(questionId);
        if (questionOpt.isEmpty()) {
            return false;
        }

        Question question = questionOpt.get();
        boolean isCorrect = evaluateAnswer(question, answer);
        int pointsEarned = isCorrect ? question.getPoints() : 0;

        // Save student answer
        StudentAnswer studentAnswer = new StudentAnswer(studentId, questionId, answer, isCorrect, pointsEarned);
        studentAnswer.setTestPaperId(testPaperId);
        studentAnswer.setTimeSpent(timeSpent);
        studentAnswerRepository.save(studentAnswer);

        // Handle wrong answers
        if (!isCorrect) {
            handleWrongAnswer(studentId, questionId, answer);
        } else {
            markQuestionAsMastered(studentId, questionId);
        }

        return isCorrect;
    }

    private boolean evaluateAnswer(Question question, String answer) {
        if (answer == null || answer.trim().isEmpty()) {
            return false;
        }

        String correctAnswer = question.getCorrectAnswer().trim();
        String studentAnswer = answer.trim();

        switch (question.getQuestionType().toLowerCase()) {
            case "single_choice":
            case "multiple_choice":
                return correctAnswer.equalsIgnoreCase(studentAnswer);
            case "true_false":
                return correctAnswer.equalsIgnoreCase(studentAnswer);
            case "fill_blank":
                // For fill-in-the-blank, allow some flexibility
                return correctAnswer.equalsIgnoreCase(studentAnswer) || 
                       correctAnswer.toLowerCase().contains(studentAnswer.toLowerCase());
            default:
                return correctAnswer.equalsIgnoreCase(studentAnswer);
        }
    }

    private void handleWrongAnswer(Long studentId, Long questionId, String wrongAnswer) {
        Optional<WrongQuestion> existingWrong = wrongQuestionRepository
            .findByStudentIdAndContentTypeAndContentId(studentId, "question", questionId);

        if (existingWrong.isPresent()) {
            WrongQuestion wrongQuestion = existingWrong.get();
            wrongQuestion.setRetryCount(wrongQuestion.getRetryCount() + 1);
            wrongQuestion.setWrongAnswer(wrongAnswer);
            wrongQuestion.setIsMastered(false);
            wrongQuestionRepository.save(wrongQuestion);
        } else {
            WrongQuestion wrongQuestion = new WrongQuestion(studentId, "question", questionId, wrongAnswer);
            wrongQuestionRepository.save(wrongQuestion);
        }
    }

    private void markQuestionAsMastered(Long studentId, Long questionId) {
        Optional<WrongQuestion> wrongQuestionOpt = wrongQuestionRepository
            .findByStudentIdAndContentTypeAndContentId(studentId, "question", questionId);

        if (wrongQuestionOpt.isPresent()) {
            WrongQuestion wrongQuestion = wrongQuestionOpt.get();
            wrongQuestion.setIsMastered(true);
            wrongQuestionRepository.save(wrongQuestion);
        }
    }

    public Map<String, Object> getTestResult(Long studentId, Long testPaperId) {
        Long correctAnswers = studentAnswerRepository.countCorrectAnswers(studentId, testPaperId);
        Long totalAnswers = studentAnswerRepository.countTotalAnswers(studentId, testPaperId);
        Long totalScore = studentAnswerRepository.getTotalScore(studentId, testPaperId);
        Long totalTimeSpent = studentAnswerRepository.getTotalTimeSpent(studentId, testPaperId);

        Map<String, Object> result = new HashMap<>();
        result.put("correctAnswers", correctAnswers);
        result.put("totalAnswers", totalAnswers);
        result.put("accuracy", totalAnswers > 0 ? (double) correctAnswers / totalAnswers * 100 : 0);
        result.put("totalScore", totalScore);
        result.put("totalTimeSpent", totalTimeSpent);

        return result;
    }

    public List<StudentAnswer> getStudentAnswers(Long studentId, Long testPaperId) {
        return studentAnswerRepository.findByStudentIdAndTestPaperIdOrderByCreatedAtAsc(studentId, testPaperId);
    }

    public List<WrongQuestion> getWrongQuestions(Long studentId) {
        return wrongQuestionRepository.findByStudentIdAndContentTypeAndIsMasteredFalse(studentId, "question");
    }

    // Generate practice test
    public TestPaper generatePracticeTest(Integer textbookVersionId, Integer grade, String subject, 
                                         Integer questionCount, String title) {
        List<Question> questions = getRandomQuestions(textbookVersionId, grade, subject, questionCount);
        
        TestPaper testPaper = new TestPaper(title, textbookVersionId, grade, subject, "practice");
        testPaper.setDescription("系统生成的练习测试");
        testPaper.setTimeLimit(questionCount * 2); // 2 minutes per question
        
        int totalPoints = questions.stream().mapToInt(Question::getPoints).sum();
        testPaper.setTotalPoints(totalPoints);
        
        return testPaperRepository.save(testPaper);
    }

    public List<Question> getTestQuestions(Long testPaperId) {
        // This would typically involve joining with TestPaperQuestion
        // For now, return empty list - would need to implement proper relationship
        return List.of();
    }
}
