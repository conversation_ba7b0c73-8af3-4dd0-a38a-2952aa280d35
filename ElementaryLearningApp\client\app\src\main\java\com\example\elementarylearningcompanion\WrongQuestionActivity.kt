package com.example.elementarylearningcompanion

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Done
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.elementarylearningcompanion.ui.theme.ElementaryLearningCompanionTheme
import com.example.elementarylearningcompanion.viewmodel.WrongQuestionViewModel

class WrongQuestionActivity : ComponentActivity() {
    private val viewModel: WrongQuestionViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ElementaryLearningCompanionTheme {
                Surface(modifier = Modifier.fillMaxSize(), color = MaterialTheme.colorScheme.background) {
                    WrongQuestionScreen(viewModel)
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WrongQuestionScreen(viewModel: WrongQuestionViewModel) {
    val detailedWrongQuestions by viewModel.detailedWrongQuestions.observeAsState(emptyList())
    val statistics by viewModel.statistics.observeAsState(emptyMap())
    val isLoading by viewModel.isLoading.observeAsState(false)
    val errorMessage by viewModel.errorMessage.observeAsState()
    val operationResult by viewModel.operationResult.observeAsState()

    var selectedFilter by remember { mutableStateOf("all") }

    LaunchedEffect(Unit) {
        viewModel.loadDetailedWrongQuestions(1L) // Using dummy student ID
        viewModel.loadStatistics(1L)
    }

    LaunchedEffect(selectedFilter) {
        when (selectedFilter) {
            "all" -> viewModel.loadDetailedWrongQuestions(1L)
            "math" -> viewModel.loadWrongQuestionsByType(1L, "math_exercise")
            "english" -> viewModel.loadWrongQuestionsByType(1L, "english_word")
            "general" -> viewModel.loadWrongQuestionsByType(1L, "question")
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            text = "错题本",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // Statistics Card
        if (statistics.isNotEmpty()) {
            WrongQuestionStatisticsCard(statistics)
            Spacer(modifier = Modifier.height(16.dp))
        }

        // Filter Tabs
        FilterTabs(
            selectedFilter = selectedFilter,
            onFilterSelected = { selectedFilter = it },
            statistics = statistics
        )

        Spacer(modifier = Modifier.height(16.dp))

        when {
            isLoading -> {
                Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                    CircularProgressIndicator()
                }
            }
            
            detailedWrongQuestions.isEmpty() -> {
                Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                    Text(
                        text = "暂无错题",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            else -> {
                LazyColumn {
                    items(detailedWrongQuestions) { item ->
                        WrongQuestionItem(
                            item = item,
                            onMarkMastered = { wrongQuestionId ->
                                viewModel.markAsMastered(1L, wrongQuestionId)
                            },
                            onDelete = { wrongQuestionId ->
                                viewModel.deleteWrongQuestion(1L, wrongQuestionId)
                            }
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
            }
        }

        errorMessage?.let { message ->
            LaunchedEffect(message) {
                // Show error message
            }
        }

        operationResult?.let { result ->
            LaunchedEffect(result) {
                // Show operation result
                viewModel.clearOperationResult()
            }
        }
    }
}

@Composable
fun WrongQuestionStatisticsCard(statistics: Map<String, Any>) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "错题统计",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                WrongQuestionStatisticItem("数学", (statistics["mathExercises"] as? Double)?.toInt() ?: 0)
                WrongQuestionStatisticItem("英语", (statistics["englishWords"] as? Double)?.toInt() ?: 0)
                WrongQuestionStatisticItem("综合", (statistics["generalQuestions"] as? Double)?.toInt() ?: 0)
                WrongQuestionStatisticItem("总计", (statistics["total"] as? Double)?.toInt() ?: 0)
            }
        }
    }
}

@Composable
fun WrongQuestionStatisticItem(label: String, count: Int) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = count.toString(),
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
fun FilterTabs(
    selectedFilter: String,
    onFilterSelected: (String) -> Unit,
    statistics: Map<String, Any>
) {
    val filters = listOf(
        "all" to "全部",
        "math" to "数学",
        "english" to "英语",
        "general" to "综合"
    )

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        filters.forEach { (key, label) ->
            FilterChip(
                onClick = { onFilterSelected(key) },
                label = { Text(label) },
                selected = selectedFilter == key,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
fun WrongQuestionItem(
    item: Map<String, Any>,
    onMarkMastered: (Long) -> Unit,
    onDelete: (Long) -> Unit
) {
    val wrongQuestion = item["wrongQuestion"] as? Map<String, Any>
    val title = item["title"] as? String ?: "未知题目"
    val type = item["type"] as? String ?: "题目"
    val wrongQuestionId = (wrongQuestion?.get("id") as? Double)?.toLong() ?: 0L
    val retryCount = (wrongQuestion?.get("retryCount") as? Double)?.toInt() ?: 0

    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = type,
                        style = MaterialTheme.typography.labelMedium,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = title,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    if (retryCount > 0) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "重做次数：$retryCount",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                Row {
                    IconButton(
                        onClick = { onMarkMastered(wrongQuestionId) }
                    ) {
                        Icon(
                            Icons.Default.Done,
                            contentDescription = "标记为掌握",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                    IconButton(
                        onClick = { onDelete(wrongQuestionId) }
                    ) {
                        Icon(
                            Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
        }
    }
}
