/**
 * UI 状态管理
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ThemeType, LanguageType } from '@/types';

// 状态接口
interface UIState {
  // 主题
  theme: ThemeType;
  
  // 语言
  language: LanguageType;
  
  // 侧边栏
  sidebarCollapsed: boolean;
  
  // 移动端侧边栏
  mobileSidebarVisible: boolean;
  
  // 全屏模式
  isFullscreen: boolean;
  
  // 加载状态
  globalLoading: boolean;
  
  // 通知设置
  notifications: {
    desktop: boolean;
    sound: boolean;
    email: boolean;
  };
  
  // 布局设置
  layout: {
    headerHeight: number;
    sidebarWidth: number;
    contentPadding: number;
  };
  
  // 图表设置
  chartSettings: {
    defaultType: 'kline' | 'line' | 'area';
    showVolume: boolean;
    showIndicators: boolean;
    autoRefresh: boolean;
    refreshInterval: number; // 秒
  };
  
  // 表格设置
  tableSettings: {
    pageSize: number;
    showBorder: boolean;
    size: 'small' | 'middle' | 'large';
  };
  
  // 实时数据设置
  realtimeSettings: {
    enabled: boolean;
    autoSubscribe: boolean;
    maxSubscriptions: number;
  };
}

// 从本地存储获取初始设置
const getInitialTheme = (): ThemeType => {
  const saved = localStorage.getItem('theme');
  return (saved as ThemeType) || 'light';
};

const getInitialLanguage = (): LanguageType => {
  const saved = localStorage.getItem('language');
  return (saved as LanguageType) || 'zh-CN';
};

const getInitialSidebarCollapsed = (): boolean => {
  const saved = localStorage.getItem('sidebarCollapsed');
  return saved ? JSON.parse(saved) : false;
};

const getInitialNotifications = () => {
  const saved = localStorage.getItem('notifications');
  return saved ? JSON.parse(saved) : {
    desktop: true,
    sound: true,
    email: false,
  };
};

const getInitialChartSettings = () => {
  const saved = localStorage.getItem('chartSettings');
  return saved ? JSON.parse(saved) : {
    defaultType: 'kline',
    showVolume: true,
    showIndicators: true,
    autoRefresh: true,
    refreshInterval: 5,
  };
};

const getInitialTableSettings = () => {
  const saved = localStorage.getItem('tableSettings');
  return saved ? JSON.parse(saved) : {
    pageSize: 20,
    showBorder: true,
    size: 'middle',
  };
};

const getInitialRealtimeSettings = () => {
  const saved = localStorage.getItem('realtimeSettings');
  return saved ? JSON.parse(saved) : {
    enabled: true,
    autoSubscribe: true,
    maxSubscriptions: 50,
  };
};

// 初始状态
const initialState: UIState = {
  theme: getInitialTheme(),
  language: getInitialLanguage(),
  sidebarCollapsed: getInitialSidebarCollapsed(),
  mobileSidebarVisible: false,
  isFullscreen: false,
  globalLoading: false,
  notifications: getInitialNotifications(),
  layout: {
    headerHeight: 64,
    sidebarWidth: 256,
    contentPadding: 24,
  },
  chartSettings: getInitialChartSettings(),
  tableSettings: getInitialTableSettings(),
  realtimeSettings: getInitialRealtimeSettings(),
};

// 创建 slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // 切换主题
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
      localStorage.setItem('theme', state.theme);
    },
    
    // 设置主题
    setTheme: (state, action: PayloadAction<ThemeType>) => {
      state.theme = action.payload;
      localStorage.setItem('theme', state.theme);
    },
    
    // 设置语言
    setLanguage: (state, action: PayloadAction<LanguageType>) => {
      state.language = action.payload;
      localStorage.setItem('language', state.language);
    },
    
    // 切换侧边栏
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
      localStorage.setItem('sidebarCollapsed', JSON.stringify(state.sidebarCollapsed));
    },
    
    // 设置侧边栏状态
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload;
      localStorage.setItem('sidebarCollapsed', JSON.stringify(state.sidebarCollapsed));
    },
    
    // 切换移动端侧边栏
    toggleMobileSidebar: (state) => {
      state.mobileSidebarVisible = !state.mobileSidebarVisible;
    },
    
    // 设置移动端侧边栏状态
    setMobileSidebarVisible: (state, action: PayloadAction<boolean>) => {
      state.mobileSidebarVisible = action.payload;
    },
    
    // 切换全屏模式
    toggleFullscreen: (state) => {
      state.isFullscreen = !state.isFullscreen;
    },
    
    // 设置全屏模式
    setFullscreen: (state, action: PayloadAction<boolean>) => {
      state.isFullscreen = action.payload;
    },
    
    // 设置全局加载状态
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.globalLoading = action.payload;
    },
    
    // 更新通知设置
    updateNotifications: (state, action: PayloadAction<Partial<UIState['notifications']>>) => {
      state.notifications = { ...state.notifications, ...action.payload };
      localStorage.setItem('notifications', JSON.stringify(state.notifications));
    },
    
    // 更新布局设置
    updateLayout: (state, action: PayloadAction<Partial<UIState['layout']>>) => {
      state.layout = { ...state.layout, ...action.payload };
    },
    
    // 更新图表设置
    updateChartSettings: (state, action: PayloadAction<Partial<UIState['chartSettings']>>) => {
      state.chartSettings = { ...state.chartSettings, ...action.payload };
      localStorage.setItem('chartSettings', JSON.stringify(state.chartSettings));
    },
    
    // 更新表格设置
    updateTableSettings: (state, action: PayloadAction<Partial<UIState['tableSettings']>>) => {
      state.tableSettings = { ...state.tableSettings, ...action.payload };
      localStorage.setItem('tableSettings', JSON.stringify(state.tableSettings));
    },
    
    // 更新实时数据设置
    updateRealtimeSettings: (state, action: PayloadAction<Partial<UIState['realtimeSettings']>>) => {
      state.realtimeSettings = { ...state.realtimeSettings, ...action.payload };
      localStorage.setItem('realtimeSettings', JSON.stringify(state.realtimeSettings));
    },
    
    // 重置所有设置
    resetSettings: (state) => {
      // 重置为默认值
      state.theme = 'light';
      state.language = 'zh-CN';
      state.sidebarCollapsed = false;
      state.notifications = {
        desktop: true,
        sound: true,
        email: false,
      };
      state.chartSettings = {
        defaultType: 'kline',
        showVolume: true,
        showIndicators: true,
        autoRefresh: true,
        refreshInterval: 5,
      };
      state.tableSettings = {
        pageSize: 20,
        showBorder: true,
        size: 'middle',
      };
      state.realtimeSettings = {
        enabled: true,
        autoSubscribe: true,
        maxSubscriptions: 50,
      };
      
      // 清除本地存储
      localStorage.removeItem('theme');
      localStorage.removeItem('language');
      localStorage.removeItem('sidebarCollapsed');
      localStorage.removeItem('notifications');
      localStorage.removeItem('chartSettings');
      localStorage.removeItem('tableSettings');
      localStorage.removeItem('realtimeSettings');
    },
  },
});

// 导出 actions
export const {
  toggleTheme,
  setTheme,
  setLanguage,
  toggleSidebar,
  setSidebarCollapsed,
  toggleMobileSidebar,
  setMobileSidebarVisible,
  toggleFullscreen,
  setFullscreen,
  setGlobalLoading,
  updateNotifications,
  updateLayout,
  updateChartSettings,
  updateTableSettings,
  updateRealtimeSettings,
  resetSettings,
} = uiSlice.actions;

// 选择器
export const selectTheme = (state: { ui: UIState }) => state.ui.theme;
export const selectLanguage = (state: { ui: UIState }) => state.ui.language;
export const selectSidebarCollapsed = (state: { ui: UIState }) => state.ui.sidebarCollapsed;
export const selectMobileSidebarVisible = (state: { ui: UIState }) => state.ui.mobileSidebarVisible;
export const selectIsFullscreen = (state: { ui: UIState }) => state.ui.isFullscreen;
export const selectGlobalLoading = (state: { ui: UIState }) => state.ui.globalLoading;
export const selectNotifications = (state: { ui: UIState }) => state.ui.notifications;
export const selectLayout = (state: { ui: UIState }) => state.ui.layout;
export const selectChartSettings = (state: { ui: UIState }) => state.ui.chartSettings;
export const selectTableSettings = (state: { ui: UIState }) => state.ui.tableSettings;
export const selectRealtimeSettings = (state: { ui: UIState }) => state.ui.realtimeSettings;

export default uiSlice.reducer;
