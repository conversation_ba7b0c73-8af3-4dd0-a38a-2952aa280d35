/**
 * 首页
 */

import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Row,
  Col,
  Card,
  Typography,
  Space,
  Button,
  List,
  Tag,
  Statistic,
  Progress,
  Avatar,
  Divider,
  Spin,
} from 'antd';
import {
  StockOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined,
  FileTextOutlined,
  BellOutlined,
  EyeOutlined,
  ArrowRightOutlined,
  RiseOutlined,
  FallOutlined,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '@/store';
import {
  fetchMarketOverviewAsync,
  fetchHotStocksAsync,
  selectMarketOverview,
  selectMarketOverviewLoading,
  selectHotStocks,
  selectHotStocksLoading,
} from '@/store/slices/stockSlice';
import StockPriceDisplay from '@/components/StockPriceDisplay';
import NewsCard from '@/components/NewsCard';

const { Title, Text } = Typography;

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  // 状态
  const marketOverview = useAppSelector(selectMarketOverview);
  const marketOverviewLoading = useAppSelector(selectMarketOverviewLoading);
  const hotStocks = useAppSelector(selectHotStocks);
  const hotStocksLoading = useAppSelector(selectHotStocksLoading);
  const user = useAppSelector(state => state.auth.user);

  // 本地状态
  const [currentTime, setCurrentTime] = useState(new Date());

  // 初始化数据
  useEffect(() => {
    dispatch(fetchMarketOverviewAsync());
    dispatch(fetchHotStocksAsync({ limit: 10 }));
  }, [dispatch]);

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // 格式化时间
  const formatTime = (date: Date) => {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  // 获取市场状态
  const getMarketStatus = () => {
    if (!marketOverview?.market_status) return { text: '未知', color: 'default' };
    
    const { is_trading_time, session } = marketOverview.market_status;
    
    if (is_trading_time) {
      return { text: `交易中 (${session})`, color: 'success' };
    } else {
      return { text: '休市', color: 'default' };
    }
  };

  const marketStatus = getMarketStatus();

  return (
    <div>
      {/* 欢迎区域 */}
      <Card style={{ marginBottom: 24 }}>
        <div className="flex-between">
          <div>
            <Title level={3} style={{ margin: 0 }}>
              欢迎回来，{user?.full_name || user?.username}！
            </Title>
            <Text type="secondary">
              {formatTime(currentTime)} | 市场状态: 
              <Tag color={marketStatus.color} style={{ marginLeft: 8 }}>
                {marketStatus.text}
              </Tag>
            </Text>
          </div>
          <Space>
            <Button
              type="primary"
              icon={<StockOutlined />}
              onClick={() => navigate('/stocks')}
            >
              浏览股票
            </Button>
            <Button
              icon={<FileTextOutlined />}
              onClick={() => navigate('/news')}
            >
              查看新闻
            </Button>
          </Space>
        </div>
      </Card>

      <Row gutter={[24, 24]}>
        {/* 左侧内容 */}
        <Col xs={24} lg={16}>
          {/* 市场概览 */}
          <Card
            title={
              <Space>
                <TrendingUpOutlined />
                <span>市场概览</span>
              </Space>
            }
            extra={
              <Button
                type="link"
                icon={<ArrowRightOutlined />}
                onClick={() => navigate('/stocks')}
              >
                查看更多
              </Button>
            }
            loading={marketOverviewLoading}
            style={{ marginBottom: 24 }}
          >
            {marketOverview && (
              <Row gutter={[16, 16]}>
                {/* 市场指数 */}
                {marketOverview.market_indices?.map((index, i) => (
                  <Col xs={12} sm={8} key={i}>
                    <div className="text-center">
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {index.name}
                      </Text>
                      <div>
                        <Text strong style={{ fontSize: 16 }}>
                          {index.current.toFixed(2)}
                        </Text>
                      </div>
                      <div>
                        <Text
                          className={
                            index.change >= 0
                              ? 'stock-change-positive'
                              : 'stock-change-negative'
                          }
                          style={{ fontSize: 12 }}
                        >
                          {index.change >= 0 ? '+' : ''}
                          {index.change.toFixed(2)} ({index.pct_change.toFixed(2)}%)
                        </Text>
                      </div>
                    </div>
                  </Col>
                ))}

                <Divider />

                {/* 市场统计 */}
                <Col xs={8}>
                  <Statistic
                    title="总股票数"
                    value={marketOverview.statistics?.total_stocks || 0}
                    prefix={<StockOutlined />}
                  />
                </Col>
                <Col xs={8}>
                  <Statistic
                    title="上涨股票"
                    value={65}
                    suffix="%"
                    valueStyle={{ color: '#cf1322' }}
                    prefix={<RiseOutlined />}
                  />
                </Col>
                <Col xs={8}>
                  <Statistic
                    title="下跌股票"
                    value={35}
                    suffix="%"
                    valueStyle={{ color: '#3f8600' }}
                    prefix={<FallOutlined />}
                  />
                </Col>
              </Row>
            )}
          </Card>

          {/* 热门股票 */}
          <Card
            title={
              <Space>
                <TrendingUpOutlined />
                <span>热门股票</span>
              </Space>
            }
            extra={
              <Button
                type="link"
                icon={<ArrowRightOutlined />}
                onClick={() => navigate('/stocks?sort=hot')}
              >
                查看更多
              </Button>
            }
            loading={hotStocksLoading}
          >
            <List
              dataSource={hotStocks.slice(0, 8)}
              renderItem={(stock, index) => (
                <List.Item
                  className="cursor-pointer"
                  onClick={() => navigate(`/stocks/${stock.code}`)}
                  style={{
                    padding: '12px 0',
                    borderBottom: index === hotStocks.length - 1 ? 'none' : undefined,
                  }}
                >
                  <div className="flex-between full-width">
                    <div className="flex-center">
                      <div
                        style={{
                          width: 24,
                          height: 24,
                          borderRadius: '50%',
                          background: index < 3 ? '#ff4d4f' : '#d9d9d9',
                          color: '#fff',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontSize: 12,
                          fontWeight: 'bold',
                          marginRight: 12,
                        }}
                      >
                        {index + 1}
                      </div>
                      <div>
                        <Text strong className="stock-code">
                          {stock.code}
                        </Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {stock.name}
                        </Text>
                      </div>
                    </div>
                    <div className="text-right">
                      <StockPriceDisplay
                        price={stock.close_price}
                        change={stock.change}
                        pctChange={stock.pct_change}
                        size="small"
                      />
                    </div>
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 右侧内容 */}
        <Col xs={24} lg={8}>
          {/* 我的自选股 */}
          <Card
            title={
              <Space>
                <EyeOutlined />
                <span>我的自选股</span>
              </Space>
            }
            extra={
              <Button
                type="link"
                icon={<ArrowRightOutlined />}
                onClick={() => navigate('/watchlist')}
              >
                管理
              </Button>
            }
            style={{ marginBottom: 24 }}
          >
            <div className="text-center p-3">
              <Text type="secondary">
                暂无自选股，
                <Button
                  type="link"
                  size="small"
                  onClick={() => navigate('/stocks')}
                  style={{ padding: 0 }}
                >
                  去添加
                </Button>
              </Text>
            </div>
          </Card>

          {/* 我的预警 */}
          <Card
            title={
              <Space>
                <BellOutlined />
                <span>我的预警</span>
              </Space>
            }
            extra={
              <Button
                type="link"
                icon={<ArrowRightOutlined />}
                onClick={() => navigate('/alerts')}
              >
                管理
              </Button>
            }
            style={{ marginBottom: 24 }}
          >
            <div className="text-center p-3">
              <Text type="secondary">
                暂无预警，
                <Button
                  type="link"
                  size="small"
                  onClick={() => navigate('/alerts')}
                  style={{ padding: 0 }}
                >
                  去创建
                </Button>
              </Text>
            </div>
          </Card>

          {/* 最新新闻 */}
          <Card
            title={
              <Space>
                <FileTextOutlined />
                <span>最新新闻</span>
              </Space>
            }
            extra={
              <Button
                type="link"
                icon={<ArrowRightOutlined />}
                onClick={() => navigate('/news')}
              >
                查看更多
              </Button>
            }
          >
            <div className="text-center p-3">
              <Text type="secondary">正在加载新闻...</Text>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default HomePage;
