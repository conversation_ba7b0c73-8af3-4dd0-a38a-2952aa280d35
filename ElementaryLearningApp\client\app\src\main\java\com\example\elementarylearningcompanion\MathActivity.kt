package com.example.elementarylearningcompanion

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.elementarylearningcompanion.ui.theme.ElementaryLearningCompanionTheme
import com.example.elementarylearningcompanion.viewmodel.MathViewModel

class MathActivity : ComponentActivity() {
    private val viewModel: MathViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ElementaryLearningCompanionTheme {
                Surface(modifier = Modifier.fillMaxSize(), color = MaterialTheme.colorScheme.background) {
                    MathScreen(viewModel)
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MathScreen(viewModel: MathViewModel) {
    val context = LocalContext.current
    val practiceTypes by viewModel.practiceTypes.observeAsState(emptyList())
    val exercises by viewModel.exercises.observeAsState(emptyList())
    val isLoading by viewModel.isLoading.observeAsState(false)
    val errorMessage by viewModel.errorMessage.observeAsState()
    val submitResult by viewModel.submitResult.observeAsState()

    var selectedPracticeType by remember { mutableStateOf<Map<String, Any>?>(null) }
    var currentExerciseIndex by remember { mutableStateOf(0) }
    var userAnswer by remember { mutableStateOf("") }
    var showResult by remember { mutableStateOf(false) }
    var startTime by remember { mutableStateOf(0L) }

    LaunchedEffect(Unit) {
        viewModel.loadPracticeTypes()
    }

    LaunchedEffect(submitResult) {
        submitResult?.let {
            showResult = true
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 顶部返回按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            TextButton(
                onClick = { (context as? ComponentActivity)?.finish() }
            ) {
                Text("← 返回主页")
            }
        }

        Text(
            text = "数学练习",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        when {
            isLoading -> {
                Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                    CircularProgressIndicator()
                }
            }
            
            selectedPracticeType == null -> {
                PracticeTypeSelection(practiceTypes) { practiceType ->
                    selectedPracticeType = practiceType
                    viewModel.generateExercises(
                        practiceType["type"] as String,
                        1, // difficulty level
                        10 // count
                    )
                }
            }
            
            exercises.isNotEmpty() -> {
                ExerciseScreen(
                    exercises = exercises,
                    currentIndex = currentExerciseIndex,
                    userAnswer = userAnswer,
                    onAnswerChange = { userAnswer = it },
                    onSubmit = { answer ->
                        val exercise = exercises[currentExerciseIndex]
                        val timeSpent = ((System.currentTimeMillis() - startTime) / 1000).toInt()
                        exercise.getId()?.let { exerciseId ->
                            viewModel.submitAnswer(1L, exerciseId, answer, timeSpent) // Using dummy student ID
                        }
                    },
                    onNext = {
                        if (currentExerciseIndex < exercises.size - 1) {
                            currentExerciseIndex++
                            userAnswer = ""
                            showResult = false
                            startTime = System.currentTimeMillis()
                            viewModel.clearSubmitResult()
                        }
                    },
                    onBack = {
                        if (selectedPracticeType != null) {
                            // 返回到练习类型选择
                            selectedPracticeType = null
                            currentExerciseIndex = 0
                            userAnswer = ""
                            showResult = false
                            viewModel.clearSubmitResult()
                        } else {
                            // 返回到主界面
                            (context as? ComponentActivity)?.finish()
                        }
                    },
                    showResult = showResult,
                    submitResult = submitResult
                )
            }
        }

        errorMessage?.let { message ->
            LaunchedEffect(message) {
                // Show error message
            }
        }
    }

    LaunchedEffect(selectedPracticeType) {
        if (selectedPracticeType != null) {
            startTime = System.currentTimeMillis()
        }
    }
}

@Composable
fun PracticeTypeSelection(
    practiceTypes: List<Map<String, Any>>,
    onTypeSelected: (Map<String, Any>) -> Unit
) {
    LazyColumn {
        items(practiceTypes) { practiceType ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                onClick = { onTypeSelected(practiceType) }
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = practiceType["name"] as String,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = practiceType["description"] as String,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Composable
fun ExerciseScreen(
    exercises: List<com.example.elementarylearningcompanion.dto.MathExercise>,
    currentIndex: Int,
    userAnswer: String,
    onAnswerChange: (String) -> Unit,
    onSubmit: (String) -> Unit,
    onNext: () -> Unit,
    onBack: () -> Unit,
    showResult: Boolean,
    submitResult: com.example.elementarylearningcompanion.dto.ApiResponse?
) {
    val currentExercise = exercises[currentIndex]

    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            TextButton(onClick = onBack) {
                Text("返回")
            }
            Text(
                text = "${currentIndex + 1} / ${exercises.size}",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        Spacer(modifier = Modifier.height(32.dp))

        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = currentExercise.getQuestion(),
                    style = MaterialTheme.typography.headlineLarge,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(24.dp))

                OutlinedTextField(
                    value = userAnswer,
                    onValueChange = onAnswerChange,
                    label = { Text("请输入答案") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !showResult
                )

                Spacer(modifier = Modifier.height(16.dp))

                if (!showResult) {
                    Button(
                        onClick = { onSubmit(userAnswer) },
                        modifier = Modifier.fillMaxWidth(),
                        enabled = userAnswer.isNotBlank()
                    ) {
                        Text("提交答案")
                    }
                } else {
                    submitResult?.let { result ->
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = if (result.isSuccess())
                                    MaterialTheme.colorScheme.primaryContainer
                                else
                                    MaterialTheme.colorScheme.errorContainer
                            )
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp),
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Text(
                                    text = if (result.isSuccess()) "✓ 正确！" else "✗ 错误",
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.Bold
                                )
                                Text(
                                    text = result.getMessage(),
                                    style = MaterialTheme.typography.bodyMedium
                                )
                                if (!result.isSuccess()) {
                                    Text(
                                        text = "正确答案：${currentExercise.getCorrectAnswer()}",
                                        style = MaterialTheme.typography.bodyMedium,
                                        fontWeight = FontWeight.Bold
                                    )
                                }
                            }
                        }

                        Spacer(modifier = Modifier.height(16.dp))

                        if (currentIndex < exercises.size - 1) {
                            Button(
                                onClick = onNext,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Text("下一题")
                            }
                        } else {
                            Button(
                                onClick = onBack,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Text("完成练习")
                            }
                        }
                    }
                }
            }
        }

        currentExercise.getExplanation()?.let { explanation ->
            if (showResult) {
                Spacer(modifier = Modifier.height(16.dp))
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "解题说明",
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = explanation,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }
    }
}
