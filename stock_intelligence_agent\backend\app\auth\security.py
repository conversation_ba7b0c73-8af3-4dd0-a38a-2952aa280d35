"""
安全认证模块
提供JWT令牌、密码加密、权限验证等安全功能
"""

import jwt
import bcrypt
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Union
from fastapi import HTTPException, status, Depends
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from passlib.context import CryptContext

from app.core.config import settings

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT Bearer 认证
security = HTTPBearer()


class SecurityManager:
    """安全管理器"""
    
    def __init__(self):
        self.algorithm = "HS256"
        self.secret_key = settings.SECRET_KEY
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_days = settings.REFRESH_TOKEN_EXPIRE_DAYS
    
    def create_access_token(
        self,
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        创建访问令牌
        
        Args:
            data: 令牌数据
            expires_delta: 过期时间增量
            
        Returns:
            JWT令牌字符串
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access"
        })
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def create_refresh_token(
        self,
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        创建刷新令牌
        
        Args:
            data: 令牌数据
            expires_delta: 过期时间增量
            
        Returns:
            JWT令牌字符串
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        
        to_encode.update({
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "refresh"
        })
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str, token_type: str = "access") -> Dict[str, Any]:
        """
        验证令牌
        
        Args:
            token: JWT令牌
            token_type: 令牌类型
            
        Returns:
            令牌载荷数据
            
        Raises:
            HTTPException: 令牌无效或过期
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # 检查令牌类型
            if payload.get("type") != token_type:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="令牌类型错误"
                )
            
            # 检查过期时间
            exp = payload.get("exp")
            if exp and datetime.utcfromtimestamp(exp) < datetime.utcnow():
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="令牌已过期"
                )
            
            return payload
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌已过期"
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌无效"
            )
    
    def hash_password(self, password: str) -> str:
        """
        加密密码
        
        Args:
            password: 明文密码
            
        Returns:
            加密后的密码哈希
        """
        return pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """
        验证密码
        
        Args:
            plain_password: 明文密码
            hashed_password: 加密密码
            
        Returns:
            密码是否匹配
        """
        return pwd_context.verify(plain_password, hashed_password)
    
    def generate_reset_token(self) -> str:
        """
        生成密码重置令牌
        
        Returns:
            重置令牌
        """
        return secrets.token_urlsafe(32)
    
    def generate_verification_code(self, length: int = 6) -> str:
        """
        生成验证码
        
        Args:
            length: 验证码长度
            
        Returns:
            数字验证码
        """
        return ''.join([str(secrets.randbelow(10)) for _ in range(length)])
    
    def create_api_key(self, user_id: str, name: str) -> str:
        """
        创建API密钥
        
        Args:
            user_id: 用户ID
            name: API密钥名称
            
        Returns:
            API密钥
        """
        data = {
            "user_id": user_id,
            "name": name,
            "created_at": datetime.utcnow().isoformat()
        }
        
        # 创建永不过期的令牌
        to_encode = data.copy()
        to_encode.update({"type": "api_key"})
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_api_key(self, api_key: str) -> Dict[str, Any]:
        """
        验证API密钥
        
        Args:
            api_key: API密钥
            
        Returns:
            API密钥信息
        """
        try:
            payload = jwt.decode(api_key, self.secret_key, algorithms=[self.algorithm])
            
            if payload.get("type") != "api_key":
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的API密钥"
                )
            
            return payload
            
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的API密钥"
            )


# 全局安全管理器实例
security_manager = SecurityManager()


def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return security_manager.hash_password(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return security_manager.verify_password(plain_password, hashed_password)


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    return security_manager.create_access_token(data, expires_delta)


def create_refresh_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """创建刷新令牌"""
    return security_manager.create_refresh_token(data, expires_delta)


def verify_token(token: str, token_type: str = "access") -> Dict[str, Any]:
    """验证令牌"""
    return security_manager.verify_token(token, token_type)


async def get_current_user_from_token(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """
    从令牌获取当前用户
    
    Args:
        credentials: HTTP认证凭据
        
    Returns:
        用户信息
    """
    token = credentials.credentials
    payload = verify_token(token)
    
    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌中缺少用户信息"
        )
    
    return {
        "user_id": user_id,
        "username": payload.get("username"),
        "email": payload.get("email"),
        "is_superuser": payload.get("is_superuser", False),
        "permissions": payload.get("permissions", [])
    }


class PermissionChecker:
    """权限检查器"""
    
    def __init__(self, required_permissions: list):
        self.required_permissions = required_permissions
    
    def __call__(self, current_user: Dict[str, Any] = Depends(get_current_user_from_token)):
        """
        检查用户权限
        
        Args:
            current_user: 当前用户信息
            
        Raises:
            HTTPException: 权限不足
        """
        user_permissions = current_user.get("permissions", [])
        is_superuser = current_user.get("is_superuser", False)
        
        # 超级用户拥有所有权限
        if is_superuser:
            return current_user
        
        # 检查是否拥有所需权限
        for permission in self.required_permissions:
            if permission not in user_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要权限: {permission}"
                )
        
        return current_user


def require_permissions(permissions: list):
    """
    权限装饰器
    
    Args:
        permissions: 所需权限列表
        
    Returns:
        权限检查依赖
    """
    return Depends(PermissionChecker(permissions))


def require_superuser(current_user: Dict[str, Any] = Depends(get_current_user_from_token)):
    """
    超级用户权限检查
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        用户信息
        
    Raises:
        HTTPException: 非超级用户
    """
    if not current_user.get("is_superuser", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要超级用户权限"
        )
    
    return current_user


class RateLimiter:
    """速率限制器"""
    
    def __init__(self, max_requests: int, time_window: int):
        """
        初始化速率限制器
        
        Args:
            max_requests: 最大请求次数
            time_window: 时间窗口（秒）
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = {}
    
    def is_allowed(self, key: str) -> bool:
        """
        检查是否允许请求
        
        Args:
            key: 限制键（如用户ID、IP地址）
            
        Returns:
            是否允许请求
        """
        now = datetime.utcnow()
        
        if key not in self.requests:
            self.requests[key] = []
        
        # 清理过期请求
        self.requests[key] = [
            req_time for req_time in self.requests[key]
            if (now - req_time).total_seconds() < self.time_window
        ]
        
        # 检查请求次数
        if len(self.requests[key]) >= self.max_requests:
            return False
        
        # 记录当前请求
        self.requests[key].append(now)
        return True


# 常用速率限制器
login_rate_limiter = RateLimiter(max_requests=5, time_window=300)  # 5次/5分钟
api_rate_limiter = RateLimiter(max_requests=100, time_window=60)   # 100次/分钟


def check_rate_limit(
    limiter: RateLimiter,
    key: str,
    error_message: str = "请求过于频繁，请稍后再试"
):
    """
    检查速率限制
    
    Args:
        limiter: 速率限制器
        key: 限制键
        error_message: 错误消息
        
    Raises:
        HTTPException: 超过速率限制
    """
    if not limiter.is_allowed(key):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=error_message
        )
