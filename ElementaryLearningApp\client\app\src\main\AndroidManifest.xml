<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/Theme.ElementaryLearningCompanion"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.ElementaryLearningCompanion">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".OnboardingActivity"
            android:exported="false"
            android:label="初始设置"
            android:theme="@style/Theme.ElementaryLearningCompanion" />
        <activity
            android:name=".LessonActivity"
            android:exported="false"
            android:label="课文学习"
            android:theme="@style/Theme.ElementaryLearningCompanion" />
        <activity
            android:name=".LessonListActivity"
            android:exported="false"
            android:label="课文列表"
            android:theme="@style/Theme.ElementaryLearningCompanion" />
        <activity
            android:name=".MathActivity"
            android:exported="false"
            android:label="数学练习"
            android:theme="@style/Theme.ElementaryLearningCompanion" />
        <activity
            android:name=".EnglishActivity"
            android:exported="false"
            android:label="英语学习"
            android:theme="@style/Theme.ElementaryLearningCompanion" />
        <activity
            android:name=".WrongQuestionActivity"
            android:exported="false"
            android:label="错题本"
            android:theme="@style/Theme.ElementaryLearningCompanion" />
        <activity
            android:name=".ProfileActivity"
            android:exported="false"
            android:label="个人中心"
            android:theme="@style/Theme.ElementaryLearningCompanion" />
        <activity
            android:name=".HomeActivity"
            android:exported="false"
            android:label="主页"
            android:theme="@style/Theme.ElementaryLearningCompanion" />
    </application>

</manifest>