<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.elementarylearningcompanion"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34" />

    <uses-permission android:name="android.permission.INTERNET" />

    <permission
        android:name="com.example.elementarylearningcompanion.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.example.elementarylearningcompanion.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.ElementaryLearningCompanion"
        android:usesCleartextTraffic="true" >
        <activity
            android:name="com.example.elementarylearningcompanion.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.ElementaryLearningCompanion" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.example.elementarylearningcompanion.OnboardingActivity"
            android:exported="false"
            android:label="初始设置"
            android:theme="@style/Theme.ElementaryLearningCompanion" />
        <activity
            android:name="com.example.elementarylearningcompanion.LessonActivity"
            android:exported="false"
            android:label="课文学习"
            android:theme="@style/Theme.ElementaryLearningCompanion" />
        <activity
            android:name="com.example.elementarylearningcompanion.MathActivity"
            android:exported="false"
            android:label="数学练习"
            android:theme="@style/Theme.ElementaryLearningCompanion" />
        <activity
            android:name="com.example.elementarylearningcompanion.EnglishActivity"
            android:exported="false"
            android:label="英语学习"
            android:theme="@style/Theme.ElementaryLearningCompanion" />
        <activity
            android:name="com.example.elementarylearningcompanion.WrongQuestionActivity"
            android:exported="false"
            android:label="错题本"
            android:theme="@style/Theme.ElementaryLearningCompanion" />
        <activity
            android:name="com.example.elementarylearningcompanion.ProfileActivity"
            android:exported="false"
            android:label="个人中心"
            android:theme="@style/Theme.ElementaryLearningCompanion" />
        <activity
            android:name="com.example.elementarylearningcompanion.HomeActivity"
            android:exported="false"
            android:label="主页"
            android:theme="@style/Theme.ElementaryLearningCompanion" />
        <activity
            android:name="androidx.activity.ComponentActivity"
            android:exported="true" />
        <activity
            android:name="androidx.compose.ui.tooling.PreviewActivity"
            android:exported="true" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.example.elementarylearningcompanion.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>