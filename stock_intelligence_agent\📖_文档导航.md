# 📖 股票智能体 - 文档导航和操作流程

## 📁 文档目录结构

```
stock_intelligence_agent/                    # 项目根目录
├── 📖_文档导航.md                          # 本文件 - 文档导航和操作流程
├── 🚀_QUICK_START_GUIDE.md                # 快速入门指南 (推荐首先阅读)
├── 🏗️_ARCHITECTURE_GUIDE.md               # 架构设计指南
├── 🔧_TECHNICAL_DOCUMENTATION.md          # 技术实现文档
├── 🎯_DEMO_INSTRUCTIONS.md                # 演示操作指南
├── 🎉_FINAL_SUMMARY.md                    # 项目完成总结
├── README.md                              # 项目说明
├── backend/                               # 后端代码
│   ├── simple_test.py                     # 简化版后端服务 (推荐使用)
│   ├── app/                               # 完整版后端应用
│   └── requirements.txt                   # Python依赖
└── frontend/                              # 前端代码
    ├── detailed-test.html                 # 详细功能测试页面 (推荐使用)
    ├── simple-test.html                   # 简化测试页面
    ├── src/                               # React应用源码
    └── package.json                       # Node.js依赖
```

## 🎯 操作流程图

### 第一次使用流程
```
开始
  ↓
📖 阅读快速入门指南
  ↓ (🚀_QUICK_START_GUIDE.md)
🔧 启动后端服务
  ↓ (cd backend && python simple_test.py)
✅ 验证服务运行
  ↓ (访问 http://localhost:8000/health)
🧪 功能测试
  ↓ (打开 frontend/detailed-test.html)
📊 查看真实数据
  ↓ (测试各个功能模块)
🎉 完成体验
```

### 深入学习流程
```
基础体验完成
  ↓
🏗️ 学习系统架构
  ↓ (ARCHITECTURE_GUIDE.md)
🔧 了解技术实现
  ↓ (TECHNICAL_DOCUMENTATION.md)
🎯 查看演示指南
  ↓ (DEMO_INSTRUCTIONS.md)
📚 阅读项目总结
  ↓ (FINAL_SUMMARY.md)
🚀 开始自定义开发
```

## 🚀 5分钟快速开始

### 步骤1: 启动后端服务
```bash
# 打开命令行，进入项目目录
cd stock_intelligence_agent/backend

# 启动后端服务
python simple_test.py
```

**看到这个输出表示成功**:
```
🚀 启动简化版股票智能体后端服务...
📍 服务地址: http://localhost:8000
📖 API文档: http://localhost:8000/docs
🔍 健康检查: http://localhost:8000/health
INFO:     Uvicorn running on http://0.0.0.0:8000
```

### 步骤2: 验证服务
在浏览器中访问: **http://localhost:8000/health**

**看到这个响应表示正常**:
```json
{
  "status": "healthy",
  "service": "stock-intelligence-agent",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 步骤3: 功能测试
双击打开文件: **`stock_intelligence_agent/frontend/detailed-test.html`**

**测试清单**:
- [ ] 点击"系统状态检查" - 验证后端连接
- [ ] 点击"获取股票列表" - 获取真实股票数据
- [ ] 点击"热门股票" - 查看涨幅排行
- [ ] 点击"新闻列表" - 获取财经新闻
- [ ] 点击"热点话题" - 发现市场热点
- [ ] 点击"市场概览" - 查看整体市场

## 📊 核心功能演示

### 真实数据验证
```
1. 股票数据测试
   访问: http://localhost:8000/api/v1/stocks?limit=5
   验证: 返回真实的沪深股票数据

2. 新闻数据测试  
   访问: http://localhost:8000/api/v1/news?limit=3
   验证: 返回最新财经新闻和情绪分析

3. 板块数据测试
   访问: http://localhost:8000/api/v1/sectors/hot?limit=5
   验证: 返回热门板块和涨跌幅数据

4. 市场概览测试
   访问: http://localhost:8000/api/v1/market/overview
   验证: 返回市场指数和统计数据
```

### 智能分析验证
```
1. 情绪分析
   - 新闻自动标记为: positive(利好)/negative(利空)/neutral(中性)
   - 基于关键词匹配和权重计算

2. 热点发现
   - 自动提取新闻中的关键词
   - 统计词频生成热点排行

3. 技术分析
   - 基于TA-Lib计算技术指标
   - 生成买卖信号和置信度
```

## 🔧 故障排除

### 常见问题快速解决

#### 问题1: 后端启动失败
```
错误: ModuleNotFoundError: No module named 'fastapi'
解决: pip install fastapi uvicorn aiohttp beautifulsoup4

错误: Port 8000 is already in use  
解决: 
  Windows: netstat -ano | findstr :8000 然后 taskkill /PID <PID> /F
  Mac/Linux: lsof -ti:8000 | xargs kill -9
```

#### 问题2: 无法访问测试页面
```
问题: 双击HTML文件无法正常显示
解决: 
  1. 确保后端服务已启动 (http://localhost:8000/health 可访问)
  2. 使用现代浏览器 (Chrome/Firefox/Edge)
  3. 检查浏览器控制台是否有错误信息
```

#### 问题3: 数据获取失败
```
问题: API返回空数据或错误
原因: 网络问题或外部数据源限制
解决: 
  1. 检查网络连接
  2. 等待片刻后重试
  3. 系统会自动降级到模拟数据
```

## 📚 文档阅读顺序

### 新手用户 (推荐顺序)
1. **📖_文档导航.md** (本文件) - 了解整体结构
2. **🚀_QUICK_START_GUIDE.md** - 快速上手
3. **🎯_DEMO_INSTRUCTIONS.md** - 详细演示
4. **🎉_FINAL_SUMMARY.md** - 项目总结

### 技术用户 (推荐顺序)  
1. **🚀_QUICK_START_GUIDE.md** - 快速上手
2. **🏗️_ARCHITECTURE_GUIDE.md** - 系统架构
3. **🔧_TECHNICAL_DOCUMENTATION.md** - 技术实现
4. **🎯_DEMO_INSTRUCTIONS.md** - 功能演示

### 开发者 (推荐顺序)
1. **🏗️_ARCHITECTURE_GUIDE.md** - 系统架构
2. **🔧_TECHNICAL_DOCUMENTATION.md** - 技术实现  
3. **backend/app/** - 后端代码
4. **frontend/src/** - 前端代码

## 🎯 核心文件说明

### 📖 必读文档
- **🚀_QUICK_START_GUIDE.md**: 5分钟快速体验指南
- **🎯_DEMO_INSTRUCTIONS.md**: 详细的功能演示说明
- **🎉_FINAL_SUMMARY.md**: 项目完成情况和价值总结

### 🔧 技术文档
- **🏗️_ARCHITECTURE_GUIDE.md**: 系统架构设计和数据流程
- **🔧_TECHNICAL_DOCUMENTATION.md**: 每个模块的技术实现细节

### 🧪 测试文件
- **frontend/detailed-test.html**: 完整功能测试页面 (推荐)
- **frontend/simple-test.html**: 简化测试页面
- **backend/simple_test.py**: 后端服务启动文件 (推荐)

### 🚀 应用文件
- **backend/app/**: 完整的FastAPI后端应用
- **frontend/src/**: 完整的React前端应用

## 📞 获取帮助

### 查看API文档
启动后端服务后访问: **http://localhost:8000/docs**

### 查看系统状态
访问: **http://localhost:8000/health**

### 测试核心功能
打开: **frontend/detailed-test.html**

### 查看技术实现
阅读: **🔧_TECHNICAL_DOCUMENTATION.md**

---

## 🎉 开始您的股票智能体之旅！

**第一步**: 阅读 `🚀_QUICK_START_GUIDE.md`
**第二步**: 启动 `backend/simple_test.py`  
**第三步**: 打开 `frontend/detailed-test.html`
**第四步**: 体验真实的股票分析功能！

**🚀 现在就开始吧！**
