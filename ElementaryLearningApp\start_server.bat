@echo off
echo ==========================================
echo 小学学习伴侣 - 服务器启动脚本
echo ==========================================
echo.

echo 检查Java环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Java环境
    echo 请安装Java 17或更高版本
    echo 下载地址：https://adoptium.net/
    pause
    exit /b 1
) else (
    echo ✅ Java环境检查通过
)

echo.
echo 检查MySQL服务...
sc query mysql >nul 2>&1
if errorlevel 1 (
    echo ⚠️  警告：MySQL服务未运行
    echo 请确保MySQL服务已启动
    echo 数据库配置：localhost:3306/elementary_learning_db
    echo 用户名：root，密码：123456
) else (
    echo ✅ MySQL服务检查通过
)

echo.
echo 进入服务器目录...
cd /d "%~dp0server"

echo.
echo 检查Gradle Wrapper...
if exist "gradlew.bat" (
    echo ✅ 找到Gradle Wrapper
    echo 启动Spring Boot服务器...
    echo.
    gradlew.bat bootRun
) else (
    echo ❌ 未找到Gradle Wrapper
    echo 尝试使用系统Gradle...
    gradle bootRun
)

echo.
echo 服务器已停止
pause
