package com.example.elementarylearningcompanion.viewmodel;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.example.elementarylearningcompanion.dto.ApiResponse;
import com.example.elementarylearningcompanion.dto.MathExercise;
import com.example.elementarylearningcompanion.dto.WrongQuestion;
import com.example.elementarylearningcompanion.network.RetrofitClient;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class MathViewModel extends ViewModel {

    private MutableLiveData<List<MathExercise>> exercises = new MutableLiveData<>();
    private MutableLiveData<List<Map<String, Object>>> practiceTypes = new MutableLiveData<>();
    private MutableLiveData<List<WrongQuestion>> wrongQuestions = new MutableLiveData<>();
    private MutableLiveData<Boolean> isLoading = new MutableLiveData<>();
    private MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private MutableLiveData<ApiResponse> submitResult = new MutableLiveData<>();

    public LiveData<List<MathExercise>> getExercises() {
        return exercises;
    }

    public LiveData<List<Map<String, Object>>> getPracticeTypes() {
        return practiceTypes;
    }

    public LiveData<List<WrongQuestion>> getWrongQuestions() {
        return wrongQuestions;
    }

    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public LiveData<ApiResponse> getSubmitResult() {
        return submitResult;
    }

    public void loadPracticeTypes() {
        isLoading.setValue(true);
        RetrofitClient.getApiService().getMathPracticeTypes().enqueue(new Callback<List<Map<String, Object>>>() {
            @Override
            public void onResponse(Call<List<Map<String, Object>>> call, Response<List<Map<String, Object>>> response) {
                isLoading.setValue(false);
                if (response.isSuccessful() && response.body() != null) {
                    practiceTypes.setValue(response.body());
                } else {
                    errorMessage.setValue("加载练习类型失败");
                }
            }

            @Override
            public void onFailure(Call<List<Map<String, Object>>> call, Throwable t) {
                isLoading.setValue(false);
                errorMessage.setValue("网络错误：" + t.getMessage());
            }
        });
    }

    public void generateExercises(String exerciseType, int difficultyLevel, int count) {
        isLoading.setValue(true);
        RetrofitClient.getApiService().generateMathExercises(exerciseType, difficultyLevel, count)
                .enqueue(new Callback<List<MathExercise>>() {
                    @Override
                    public void onResponse(Call<List<MathExercise>> call, Response<List<MathExercise>> response) {
                        isLoading.setValue(false);
                        if (response.isSuccessful() && response.body() != null) {
                            exercises.setValue(response.body());
                        } else {
                            errorMessage.setValue("生成练习题失败");
                        }
                    }

                    @Override
                    public void onFailure(Call<List<MathExercise>> call, Throwable t) {
                        isLoading.setValue(false);
                        errorMessage.setValue("网络错误：" + t.getMessage());
                    }
                });
    }

    public void submitAnswer(long studentId, long exerciseId, String answer, int timeSpent) {
        Map<String, Object> request = new HashMap<>();
        request.put("studentId", studentId);
        request.put("exerciseId", exerciseId);
        request.put("answer", answer);
        request.put("timeSpent", timeSpent);

        RetrofitClient.getApiService().submitMathAnswer(request).enqueue(new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    submitResult.setValue(response.body());
                } else {
                    submitResult.setValue(new ApiResponse(false, "提交答案失败"));
                }
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                submitResult.setValue(new ApiResponse(false, "网络错误：" + t.getMessage()));
            }
        });
    }

    public void loadWrongQuestions(long studentId) {
        isLoading.setValue(true);
        RetrofitClient.getApiService().getWrongQuestions(studentId).enqueue(new Callback<List<WrongQuestion>>() {
            @Override
            public void onResponse(Call<List<WrongQuestion>> call, Response<List<WrongQuestion>> response) {
                isLoading.setValue(false);
                if (response.isSuccessful() && response.body() != null) {
                    wrongQuestions.setValue(response.body());
                } else {
                    errorMessage.setValue("加载错题本失败");
                }
            }

            @Override
            public void onFailure(Call<List<WrongQuestion>> call, Throwable t) {
                isLoading.setValue(false);
                errorMessage.setValue("网络错误：" + t.getMessage());
            }
        });
    }

    public void clearSubmitResult() {
        submitResult.setValue(null);
    }

    public void clearErrorMessage() {
        errorMessage.setValue(null);
    }
}
