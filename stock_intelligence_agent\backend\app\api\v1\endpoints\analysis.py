"""
分析功能 API 端点
"""

from datetime import datetime, date
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.services.analysis_service import AnalysisService
from app.schemas.analysis import (
    TechnicalAnalysisResponse,
    FundamentalAnalysisResponse,
    RiskAssessmentResponse,
    PortfolioAnalysisResponse,
)

router = APIRouter()


@router.get("/technical/{stock_code}", response_model=TechnicalAnalysisResponse)
async def get_technical_analysis(
    stock_code: str,
    period: str = Query("daily", description="分析周期(daily/weekly/monthly)"),
    indicators: Optional[str] = Query(None, description="技术指标,逗号分隔"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取技术分析
    
    支持多种技术指标：MA, MACD, RSI, KDJ, BOLL等
    """
    try:
        analysis_service = AnalysisService(db)
        
        # 解析指标列表
        indicator_list = indicators.split(",") if indicators else None
        
        technical_analysis = await analysis_service.get_technical_analysis(
            stock_code=stock_code,
            period=period,
            indicators=indicator_list,
        )
        
        return technical_analysis
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"技术分析失败: {str(e)}")


@router.get("/fundamental/{stock_code}", response_model=FundamentalAnalysisResponse)
async def get_fundamental_analysis(
    stock_code: str,
    db: AsyncSession = Depends(get_db),
):
    """
    获取基本面分析
    
    包括财务指标、估值分析、盈利能力等
    """
    try:
        analysis_service = AnalysisService(db)
        
        fundamental_analysis = await analysis_service.get_fundamental_analysis(
            stock_code=stock_code,
        )
        
        return fundamental_analysis
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"基本面分析失败: {str(e)}")


@router.get("/risk/{stock_code}", response_model=RiskAssessmentResponse)
async def get_risk_assessment(
    stock_code: str,
    period: int = Query(252, description="分析周期(交易日)"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取风险评估
    
    包括波动率、VaR、最大回撤、夏普比率等
    """
    try:
        analysis_service = AnalysisService(db)
        
        risk_assessment = await analysis_service.get_risk_assessment(
            stock_code=stock_code,
            period=period,
        )
        
        return risk_assessment
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"风险评估失败: {str(e)}")


@router.post("/portfolio", response_model=PortfolioAnalysisResponse)
async def analyze_portfolio(
    portfolio: Dict[str, float],  # 股票代码: 权重
    db: AsyncSession = Depends(get_db),
):
    """
    投资组合分析
    
    分析投资组合的风险收益特征
    """
    try:
        analysis_service = AnalysisService(db)
        
        portfolio_analysis = await analysis_service.analyze_portfolio(
            portfolio=portfolio,
        )
        
        return portfolio_analysis
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"投资组合分析失败: {str(e)}")


@router.get("/market/sentiment")
async def get_market_sentiment(
    db: AsyncSession = Depends(get_db),
):
    """
    获取市场情绪分析
    
    基于多种指标综合判断市场情绪
    """
    try:
        analysis_service = AnalysisService(db)
        
        market_sentiment = await analysis_service.get_market_sentiment()
        
        return {
            "sentiment": market_sentiment,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"市场情绪分析失败: {str(e)}")


@router.get("/market/stage")
async def get_market_stage(
    db: AsyncSession = Depends(get_db),
):
    """
    获取市场阶段判断
    
    判断当前市场处于牛市、熊市还是震荡市
    """
    try:
        analysis_service = AnalysisService(db)
        
        market_stage = await analysis_service.get_market_stage()
        
        return {
            "market_stage": market_stage,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"市场阶段判断失败: {str(e)}")


@router.get("/hotspots")
async def get_market_hotspots(
    limit: int = Query(20, ge=1, le=100, description="返回记录数"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取市场热点
    
    自动发现当前市场热点概念和板块
    """
    try:
        analysis_service = AnalysisService(db)
        
        hotspots = await analysis_service.get_market_hotspots(limit=limit)
        
        return {
            "hotspots": hotspots,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取市场热点失败: {str(e)}")


@router.get("/compare")
async def compare_stocks(
    stock_codes: str = Query(..., description="股票代码列表,逗号分隔"),
    metrics: Optional[str] = Query(None, description="对比指标,逗号分隔"),
    db: AsyncSession = Depends(get_db),
):
    """
    股票对比分析
    
    对比多只股票的各项指标
    """
    try:
        analysis_service = AnalysisService(db)
        
        # 解析股票代码和指标
        stock_list = stock_codes.split(",")
        metric_list = metrics.split(",") if metrics else None
        
        comparison = await analysis_service.compare_stocks(
            stock_codes=stock_list,
            metrics=metric_list,
        )
        
        return {
            "comparison": comparison,
            "stock_codes": stock_list,
            "metrics": metric_list,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"股票对比分析失败: {str(e)}")
