/**
 * Redux Store 配置
 */

import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

// 导入所有 slice
import authSlice from './slices/authSlice';
import stockSlice from './slices/stockSlice';
import newsSlice from './slices/newsSlice';
import alertSlice from './slices/alertSlice';
import watchlistSlice from './slices/watchlistSlice';
import uiSlice from './slices/uiSlice';

// 配置 store
export const store = configureStore({
  reducer: {
    auth: authSlice,
    stock: stockSlice,
    news: newsSlice,
    alert: alertSlice,
    watchlist: watchlistSlice,
    ui: uiSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

// 导出类型
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// 导出类型化的 hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

export default store;
