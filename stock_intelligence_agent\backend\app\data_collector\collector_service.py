"""
数据采集服务
统一管理所有数据采集任务
"""

import asyncio
import logging
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.core.database import get_db, get_redis, get_influxdb
from app.data_collector.akshare_client import AkshareClient
from app.data_collector.data_processor import DataProcessor
from app.models.stock import Stock, StockQuote, RealtimeQuote, FinancialData, Sector
from app.core.config import settings

logger = logging.getLogger(__name__)


class CollectorService:
    """数据采集服务"""
    
    def __init__(self, db: AsyncSession, redis=None, influxdb=None):
        self.db = db
        self.redis = redis
        self.influxdb = influxdb
        self.akshare_client = AkshareClient()
        self.data_processor = DataProcessor()
    
    async def collect_stock_list(self) -> int:
        """
        采集股票列表
        
        Returns:
            采集的股票数量
        """
        try:
            logger.info("开始采集股票列表...")
            
            # 从 akshare 获取股票列表
            raw_stocks = await self.akshare_client.get_stock_list()
            
            collected_count = 0
            for raw_stock in raw_stocks:
                try:
                    # 数据清洗
                    cleaned_data = self.data_processor.clean_stock_data(raw_stock)
                    
                    # 检查股票是否已存在
                    stmt = select(Stock).where(Stock.code == cleaned_data['code'])
                    result = await self.db.execute(stmt)
                    existing_stock = result.scalar_one_or_none()
                    
                    if existing_stock:
                        # 更新现有股票信息
                        for key, value in cleaned_data.items():
                            if hasattr(existing_stock, key):
                                setattr(existing_stock, key, value)
                        existing_stock.updated_at = datetime.utcnow()
                    else:
                        # 创建新股票记录
                        stock = Stock(**cleaned_data)
                        self.db.add(stock)
                    
                    collected_count += 1
                    
                except Exception as e:
                    logger.warning(f"处理股票 {raw_stock.get('code')} 失败: {e}")
                    continue
            
            await self.db.commit()
            logger.info(f"股票列表采集完成，共处理 {collected_count} 只股票")
            return collected_count
            
        except Exception as e:
            logger.error(f"采集股票列表失败: {e}")
            await self.db.rollback()
            raise
    
    async def collect_realtime_quotes(self, stock_codes: Optional[List[str]] = None) -> int:
        """
        采集实时行情
        
        Args:
            stock_codes: 指定股票代码列表，为空则采集所有股票
            
        Returns:
            采集的行情数量
        """
        try:
            logger.info("开始采集实时行情...")
            
            # 获取需要采集的股票代码
            if not stock_codes:
                stmt = select(Stock.code).where(Stock.is_active == True)
                result = await self.db.execute(stmt)
                stock_codes = [row[0] for row in result.fetchall()]
            
            if not stock_codes:
                logger.warning("没有找到需要采集的股票")
                return 0
            
            # 分批采集，避免请求过多
            batch_size = 50
            collected_count = 0
            
            for i in range(0, len(stock_codes), batch_size):
                batch_codes = stock_codes[i:i + batch_size]
                
                try:
                    # 从 akshare 获取实时行情
                    raw_quotes = await self.akshare_client.get_realtime_quotes(batch_codes)
                    
                    for raw_quote in raw_quotes:
                        try:
                            # 数据清洗
                            cleaned_data = self.data_processor.clean_realtime_data(raw_quote)
                            
                            # 获取股票ID
                            stock_code = raw_quote.get('code')
                            if not stock_code:
                                continue
                            
                            stmt = select(Stock.id).where(Stock.code == stock_code)
                            result = await self.db.execute(stmt)
                            stock_id = result.scalar_one_or_none()
                            
                            if not stock_id:
                                logger.warning(f"未找到股票: {stock_code}")
                                continue
                            
                            # 创建实时行情记录
                            realtime_quote = RealtimeQuote(
                                stock_id=stock_id,
                                **cleaned_data
                            )
                            self.db.add(realtime_quote)
                            
                            # 缓存到 Redis
                            if self.redis:
                                await self._cache_realtime_quote(stock_code, cleaned_data)
                            
                            # 存储到 InfluxDB
                            if self.influxdb:
                                await self._store_to_influxdb(stock_code, cleaned_data)
                            
                            collected_count += 1
                            
                        except Exception as e:
                            logger.warning(f"处理实时行情 {raw_quote.get('code')} 失败: {e}")
                            continue
                    
                    # 批量提交
                    await self.db.commit()
                    
                    # 避免请求过快
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.warning(f"采集批次 {i//batch_size + 1} 失败: {e}")
                    await self.db.rollback()
                    continue
            
            logger.info(f"实时行情采集完成，共采集 {collected_count} 条数据")
            return collected_count
            
        except Exception as e:
            logger.error(f"采集实时行情失败: {e}")
            await self.db.rollback()
            raise
    
    async def collect_historical_quotes(
        self,
        stock_code: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> int:
        """
        采集历史行情
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            采集的行情数量
        """
        try:
            logger.info(f"开始采集股票 {stock_code} 历史行情...")
            
            # 获取股票ID
            stmt = select(Stock.id).where(Stock.code == stock_code)
            result = await self.db.execute(stmt)
            stock_id = result.scalar_one_or_none()
            
            if not stock_id:
                raise ValueError(f"未找到股票: {stock_code}")
            
            # 设置默认日期范围
            if not start_date:
                start_date = date.today() - timedelta(days=365)
            if not end_date:
                end_date = date.today()
            
            # 从 akshare 获取历史行情
            raw_quotes = await self.akshare_client.get_historical_quotes(
                stock_code, start_date, end_date
            )
            
            collected_count = 0
            for raw_quote in raw_quotes:
                try:
                    # 数据清洗
                    cleaned_data = self.data_processor.clean_quote_data(raw_quote)
                    
                    # 检查是否已存在
                    trade_date = cleaned_data.get('trade_date')
                    if not trade_date:
                        continue
                    
                    stmt = select(StockQuote).where(
                        and_(
                            StockQuote.stock_id == stock_id,
                            StockQuote.trade_date == trade_date
                        )
                    )
                    result = await self.db.execute(stmt)
                    existing_quote = result.scalar_one_or_none()
                    
                    if existing_quote:
                        # 更新现有记录
                        for key, value in cleaned_data.items():
                            if hasattr(existing_quote, key):
                                setattr(existing_quote, key, value)
                        existing_quote.updated_at = datetime.utcnow()
                    else:
                        # 创建新记录
                        quote = StockQuote(
                            stock_id=stock_id,
                            **cleaned_data
                        )
                        self.db.add(quote)
                    
                    collected_count += 1
                    
                except Exception as e:
                    logger.warning(f"处理历史行情数据失败: {e}")
                    continue
            
            await self.db.commit()
            logger.info(f"历史行情采集完成，共采集 {collected_count} 条数据")
            return collected_count
            
        except Exception as e:
            logger.error(f"采集历史行情失败: {e}")
            await self.db.rollback()
            raise
    
    async def collect_financial_data(self, stock_code: str) -> bool:
        """
        采集财务数据
        
        Args:
            stock_code: 股票代码
            
        Returns:
            是否采集成功
        """
        try:
            logger.info(f"开始采集股票 {stock_code} 财务数据...")
            
            # 获取股票ID
            stmt = select(Stock.id).where(Stock.code == stock_code)
            result = await self.db.execute(stmt)
            stock_id = result.scalar_one_or_none()
            
            if not stock_id:
                raise ValueError(f"未找到股票: {stock_code}")
            
            # 从 akshare 获取财务数据
            raw_financial = await self.akshare_client.get_financial_data(stock_code)
            
            if not raw_financial:
                logger.warning(f"未获取到股票 {stock_code} 的财务数据")
                return False
            
            # 数据清洗
            cleaned_data = self.data_processor.clean_financial_data(raw_financial)
            
            # 创建财务数据记录
            financial_data = FinancialData(
                stock_id=stock_id,
                **cleaned_data
            )
            self.db.add(financial_data)
            
            await self.db.commit()
            logger.info(f"财务数据采集完成")
            return True
            
        except Exception as e:
            logger.error(f"采集财务数据失败: {e}")
            await self.db.rollback()
            return False
    
    async def collect_sector_data(self) -> int:
        """
        采集板块数据
        
        Returns:
            采集的板块数量
        """
        try:
            logger.info("开始采集板块数据...")
            
            # 从 akshare 获取板块数据
            raw_sectors = await self.akshare_client.get_sector_data()
            
            collected_count = 0
            for raw_sector in raw_sectors:
                try:
                    # 检查板块是否已存在
                    code = raw_sector.get('code')
                    if not code:
                        continue
                    
                    stmt = select(Sector).where(Sector.code == code)
                    result = await self.db.execute(stmt)
                    existing_sector = result.scalar_one_or_none()
                    
                    if existing_sector:
                        # 更新现有板块
                        existing_sector.name = raw_sector.get('name', existing_sector.name)
                        existing_sector.category = raw_sector.get('category', existing_sector.category)
                        existing_sector.updated_at = datetime.utcnow()
                    else:
                        # 创建新板块
                        sector = Sector(
                            code=code,
                            name=raw_sector.get('name', ''),
                            category=raw_sector.get('category', 'industry')
                        )
                        self.db.add(sector)
                    
                    collected_count += 1
                    
                except Exception as e:
                    logger.warning(f"处理板块数据失败: {e}")
                    continue
            
            await self.db.commit()
            logger.info(f"板块数据采集完成，共采集 {collected_count} 个板块")
            return collected_count
            
        except Exception as e:
            logger.error(f"采集板块数据失败: {e}")
            await self.db.rollback()
            raise
    
    async def _cache_realtime_quote(self, stock_code: str, quote_data: Dict[str, Any]):
        """缓存实时行情到 Redis"""
        try:
            if not self.redis:
                return
            
            cache_key = f"realtime_quote:{stock_code}"
            cache_data = {
                'current_price': str(quote_data.get('current_price', 0)),
                'change': str(quote_data.get('change', 0)),
                'pct_change': str(quote_data.get('pct_change', 0)),
                'volume': str(quote_data.get('volume', 0)),
                'amount': str(quote_data.get('amount', 0)),
                'timestamp': quote_data.get('timestamp', datetime.now()).isoformat(),
            }
            
            await self.redis.hset(cache_key, mapping=cache_data)
            await self.redis.expire(cache_key, settings.CACHE_TTL_REALTIME)
            
        except Exception as e:
            logger.warning(f"缓存实时行情失败: {e}")
    
    async def _store_to_influxdb(self, stock_code: str, quote_data: Dict[str, Any]):
        """存储数据到 InfluxDB"""
        try:
            if not self.influxdb:
                return
            
            from influxdb_client import Point
            
            point = Point("stock_realtime") \
                .tag("stock_code", stock_code) \
                .field("current_price", float(quote_data.get('current_price', 0))) \
                .field("volume", float(quote_data.get('volume', 0))) \
                .field("amount", float(quote_data.get('amount', 0))) \
                .field("pct_change", float(quote_data.get('pct_change', 0))) \
                .time(quote_data.get('timestamp', datetime.now()))
            
            write_api = self.influxdb.write_api()
            await write_api.write(
                bucket=settings.INFLUXDB_BUCKET,
                org=settings.INFLUXDB_ORG,
                record=point
            )
            
        except Exception as e:
            logger.warning(f"存储到 InfluxDB 失败: {e}")
    
    async def close(self):
        """关闭采集服务"""
        await self.akshare_client.close()
        logger.info("数据采集服务已关闭")
