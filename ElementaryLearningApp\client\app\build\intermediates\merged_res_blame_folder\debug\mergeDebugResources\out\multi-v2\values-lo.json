{"logs": [{"outputFile": "com.example.elementarylearningcompanion.app-mergeDebugResources-58:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fd01bc6fe90ff89257de9b3fddbd915b\\transformed\\foundation-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "190,191", "startColumns": "4,4", "startOffsets": "17210,17296", "endColumns": "85,84", "endOffsets": "17291,17376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a90387b92ad49178bbd95d8e99c39d57\\transformed\\core-1.13.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "38,39,40,41,42,43,44,186", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3381,3477,3580,3679,3777,3878,3976,16844", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "3472,3575,3674,3772,3873,3971,4082,16940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a4df73b4c2d29b2ee6d6405fd9c7e463\\transformed\\ui-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,272,381,479,568,657,747,833,916,981,1047,1127,1211,1285,1363,1429", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "190,267,376,474,563,652,742,828,911,976,1042,1122,1206,1280,1358,1424,1545"}, "to": {"startLines": "48,49,50,51,52,57,58,174,175,176,177,179,180,184,187,188,189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4404,4494,4571,4680,4778,5158,5247,15907,15993,16076,16141,16286,16366,16694,16945,17023,17089", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "4489,4566,4675,4773,4862,5242,5332,15988,16071,16136,16202,16361,16445,16763,17018,17084,17205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\586aaef9cc7b931892e6d835ad6bc99e\\transformed\\material3-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,391,502,599,695,808,937,1058,1189,1274,1374,1464,1564,1682,1802,1907,2034,2159,2289,2437,2558,2672,2791,2903,2994,3093,3206,3331,3425,3541,3647,3774,3908,4018,4115,4195,4293,4389,4475,4561,4666,4752,4839,4942,5044,5139,5242,5328,5429,5527,5629,5756,5842,5942", "endColumns": "113,111,109,110,96,95,112,128,120,130,84,99,89,99,117,119,104,126,124,129,147,120,113,118,111,90,98,112,124,93,115,105,126,133,109,96,79,97,95,85,85,104,85,86,102,101,94,102,85,100,97,101,126,85,99,94", "endOffsets": "164,276,386,497,594,690,803,932,1053,1184,1269,1369,1459,1559,1677,1797,1902,2029,2154,2284,2432,2553,2667,2786,2898,2989,3088,3201,3326,3420,3536,3642,3769,3903,4013,4110,4190,4288,4384,4470,4556,4661,4747,4834,4937,5039,5134,5237,5323,5424,5522,5624,5751,5837,5937,6032"}, "to": {"startLines": "60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5396,5510,5622,5732,5843,5940,6036,6149,6278,6399,6530,6615,6715,6805,6905,7023,7143,7248,7375,7500,7630,7778,7899,8013,8132,8244,8335,8434,8547,8672,8766,8882,8988,9115,9249,9359,9456,9536,9634,9730,9816,9902,10007,10093,10180,10283,10385,10480,10583,10669,10770,10868,10970,11097,11183,11283", "endColumns": "113,111,109,110,96,95,112,128,120,130,84,99,89,99,117,119,104,126,124,129,147,120,113,118,111,90,98,112,124,93,115,105,126,133,109,96,79,97,95,85,85,104,85,86,102,101,94,102,85,100,97,101,126,85,99,94", "endOffsets": "5505,5617,5727,5838,5935,6031,6144,6273,6394,6525,6610,6710,6800,6900,7018,7138,7243,7370,7495,7625,7773,7894,8008,8127,8239,8330,8429,8542,8667,8761,8877,8983,9110,9244,9354,9451,9531,9629,9725,9811,9897,10002,10088,10175,10278,10380,10475,10578,10664,10765,10863,10965,11092,11178,11278,11373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\95602d9e58c05302d7b199e835d43608\\transformed\\appcompat-1.6.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,422,525,638,723,827,938,1016,1093,1184,1277,1369,1463,1563,1656,1751,1847,1938,2029,2110,2217,2321,2419,2522,2626,2730,2887,16450", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "417,520,633,718,822,933,1011,1088,1179,1272,1364,1458,1558,1651,1746,1842,1933,2024,2105,2212,2316,2414,2517,2621,2725,2882,2981,16527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0a92ec79585cffeb394139b023740469\\transformed\\material-1.12.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,343,414,495,581,664,779,898,981,1048,1114,1203,1272,1331,1426,1492,1557,1615,1680,1741,1801,1907,1968,2028,2086,2157,2276,2362,2439,2529,2614,2696,2839,2914,2990,3121,3211,3289,3344,3399,3465,3534,3608,3679,3758,3831,3908,3977,4047,4144,4229,4304,4397,4490,4564,4633,4727,4779,4862,4929,5013,5097,5159,5223,5286,5356,5455,5553,5648,5742,5801,5860,5939,6024,6101", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,73,70,80,85,82,114,118,82,66,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,76,89,84,81,142,74,75,130,89,77,54,54,65,68,73,70,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78,84,76,75", "endOffsets": "264,338,409,490,576,659,774,893,976,1043,1109,1198,1267,1326,1421,1487,1552,1610,1675,1736,1796,1902,1963,2023,2081,2152,2271,2357,2434,2524,2609,2691,2834,2909,2985,3116,3206,3284,3339,3394,3460,3529,3603,3674,3753,3826,3903,3972,4042,4139,4224,4299,4392,4485,4559,4628,4722,4774,4857,4924,5008,5092,5154,5218,5281,5351,5450,5548,5643,5737,5796,5855,5934,6019,6096,6172"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,56,59,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,182,183,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2986,3060,3131,3212,3298,4087,4202,4321,4867,4934,5000,5089,5337,11378,11473,11539,11604,11662,11727,11788,11848,11954,12015,12075,12133,12204,12323,12409,12486,12576,12661,12743,12886,12961,13037,13168,13258,13336,13391,13446,13512,13581,13655,13726,13805,13878,13955,14024,14094,14191,14276,14351,14444,14537,14611,14680,14774,14826,14909,14976,15060,15144,15206,15270,15333,15403,15502,15600,15695,15789,15848,16207,16532,16617,16768", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,56,59,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,182,183,185", "endColumns": "12,73,70,80,85,82,114,118,82,66,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,76,89,84,81,142,74,75,130,89,77,54,54,65,68,73,70,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78,84,76,75", "endOffsets": "314,3055,3126,3207,3293,3376,4197,4316,4399,4929,4995,5084,5153,5391,11468,11534,11599,11657,11722,11783,11843,11949,12010,12070,12128,12199,12318,12404,12481,12571,12656,12738,12881,12956,13032,13163,13253,13331,13386,13441,13507,13576,13650,13721,13800,13873,13950,14019,14089,14186,14271,14346,14439,14532,14606,14675,14769,14821,14904,14971,15055,15139,15201,15265,15328,15398,15497,15595,15690,15784,15843,15902,16281,16612,16689,16839"}}]}]}