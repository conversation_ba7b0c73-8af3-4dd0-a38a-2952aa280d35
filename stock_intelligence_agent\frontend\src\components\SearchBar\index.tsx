/**
 * 搜索栏组件
 */

import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Input, AutoComplete, Typography, Space, Tag } from 'antd';
import { SearchOutlined, StockOutlined } from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '@/store';
import { searchStocksAsync, clearSearchResults, selectSearchResults, selectSearchLoading } from '@/store/slices/stockSlice';
import { SearchSuggestion } from '@/types';

const { Text } = Typography;

interface SearchOption {
  value: string;
  label: React.ReactNode;
  data: SearchSuggestion;
}

const SearchBar: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  // 状态
  const searchResults = useAppSelector(selectSearchResults);
  const searchLoading = useAppSelector(selectSearchLoading);
  
  // 本地状态
  const [searchValue, setSearchValue] = useState('');
  const [options, setOptions] = useState<SearchOption[]>([]);
  const [open, setOpen] = useState(false);
  
  // 防抖定时器
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  // 处理搜索结果
  useEffect(() => {
    if (searchResults?.suggestions) {
      const newOptions: SearchOption[] = searchResults.suggestions.map(suggestion => ({
        value: suggestion.code,
        label: (
          <div className="flex-between">
            <Space>
              <StockOutlined style={{ color: '#1890ff' }} />
              <div>
                <Text strong>{suggestion.code}</Text>
                <br />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  {suggestion.name}
                </Text>
              </div>
            </Space>
            <Space>
              <Tag color="blue" size="small">
                {suggestion.market}
              </Tag>
              {suggestion.industry && (
                <Tag size="small">
                  {suggestion.industry}
                </Tag>
              )}
            </Space>
          </div>
        ),
        data: suggestion,
      }));
      
      setOptions(newOptions);
      setOpen(newOptions.length > 0);
    } else {
      setOptions([]);
      setOpen(false);
    }
  }, [searchResults]);

  // 处理搜索输入
  const handleSearch = (value: string) => {
    setSearchValue(value);
    
    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    if (value.trim()) {
      // 防抖搜索
      searchTimeoutRef.current = setTimeout(() => {
        dispatch(searchStocksAsync(value.trim()));
      }, 300);
    } else {
      dispatch(clearSearchResults());
      setOptions([]);
      setOpen(false);
    }
  };

  // 处理选择
  const handleSelect = (value: string, option: SearchOption) => {
    const stockCode = option.data.code;
    setSearchValue('');
    setOpen(false);
    dispatch(clearSearchResults());
    
    // 跳转到股票详情页
    navigate(`/stocks/${stockCode}`);
  };

  // 处理回车搜索
  const handlePressEnter = () => {
    if (searchValue.trim()) {
      // 如果有搜索结果，选择第一个
      if (options.length > 0) {
        handleSelect(options[0].value, options[0]);
      } else {
        // 否则跳转到股票列表页面进行搜索
        navigate(`/stocks?search=${encodeURIComponent(searchValue.trim())}`);
        setSearchValue('');
      }
    }
  };

  // 处理焦点
  const handleFocus = () => {
    if (options.length > 0) {
      setOpen(true);
    }
  };

  const handleBlur = () => {
    // 延迟关闭，允许点击选项
    setTimeout(() => {
      setOpen(false);
    }, 200);
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return (
    <AutoComplete
      value={searchValue}
      options={options}
      onSearch={handleSearch}
      onSelect={handleSelect}
      onFocus={handleFocus}
      onBlur={handleBlur}
      open={open}
      style={{ width: '100%' }}
      dropdownStyle={{ zIndex: 1050 }}
      notFoundContent={
        searchLoading ? (
          <div className="text-center p-2">
            <Text type="secondary">搜索中...</Text>
          </div>
        ) : searchValue.trim() ? (
          <div className="text-center p-2">
            <Text type="secondary">未找到相关股票</Text>
          </div>
        ) : null
      }
    >
      <Input
        placeholder="搜索股票代码或名称"
        prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
        onPressEnter={handlePressEnter}
        allowClear
      />
    </AutoComplete>
  );
};

export default SearchBar;
