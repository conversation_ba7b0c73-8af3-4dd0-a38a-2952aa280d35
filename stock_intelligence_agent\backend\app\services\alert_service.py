"""
预警服务层
提供股票预警、触发检测、通知推送等功能
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, func
from uuid import UUID
import asyncio

from app.models.user import Alert, AlertTrigger
from app.models.stock import Stock, RealtimeQuote, StockQuote
from app.services.stock_service import StockService
from app.websocket.connection_manager import connection_manager

logger = logging.getLogger(__name__)


class AlertService:
    """预警服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_alert(
        self,
        user_id: UUID,
        name: str,
        stock_code: str,
        alert_type: str,
        condition: Dict[str, Any],
        notification_methods: List[str] = None
    ) -> Alert:
        """
        创建预警
        
        Args:
            user_id: 用户ID
            name: 预警名称
            stock_code: 股票代码
            alert_type: 预警类型
            condition: 预警条件
            notification_methods: 通知方式
            
        Returns:
            创建的预警对象
        """
        try:
            # 验证股票代码
            stock_service = StockService(self.db)
            stock = await stock_service.get_stock_by_code(stock_code)
            if not stock:
                raise ValueError(f"股票代码不存在: {stock_code}")
            
            # 验证预警条件
            self._validate_alert_condition(alert_type, condition)
            
            # 创建预警
            alert = Alert(
                user_id=user_id,
                name=name,
                stock_code=stock_code,
                alert_type=alert_type,
                condition=condition,
                notification_methods=notification_methods or ["websocket"],
                is_active=True,
                trigger_count=0
            )
            
            self.db.add(alert)
            await self.db.commit()
            
            logger.info(f"预警创建成功: {name} ({stock_code})")
            return alert
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建预警失败: {e}")
            raise
    
    async def get_user_alerts(
        self,
        user_id: UUID,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None,
        stock_code: Optional[str] = None
    ) -> Tuple[List[Alert], int]:
        """
        获取用户预警列表
        
        Args:
            user_id: 用户ID
            skip: 跳过数量
            limit: 限制数量
            is_active: 是否活跃
            stock_code: 股票代码
            
        Returns:
            (预警列表, 总数)
        """
        try:
            conditions = [Alert.user_id == user_id]
            
            if is_active is not None:
                conditions.append(Alert.is_active == is_active)
            
            if stock_code:
                conditions.append(Alert.stock_code == stock_code)
            
            # 查询总数
            count_stmt = select(func.count(Alert.id)).where(and_(*conditions))
            count_result = await self.db.execute(count_stmt)
            total = count_result.scalar()
            
            # 查询数据
            stmt = (
                select(Alert)
                .where(and_(*conditions))
                .order_by(desc(Alert.created_at))
                .offset(skip)
                .limit(limit)
            )
            
            result = await self.db.execute(stmt)
            alerts = result.scalars().all()
            
            return list(alerts), total
            
        except Exception as e:
            logger.error(f"获取用户预警失败: {e}")
            raise
    
    async def get_alert_by_id(self, alert_id: UUID, user_id: UUID) -> Optional[Alert]:
        """获取预警详情"""
        try:
            stmt = select(Alert).where(
                and_(Alert.id == alert_id, Alert.user_id == user_id)
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取预警详情失败: {e}")
            return None
    
    async def update_alert(
        self,
        alert_id: UUID,
        user_id: UUID,
        **kwargs
    ) -> Optional[Alert]:
        """
        更新预警
        
        Args:
            alert_id: 预警ID
            user_id: 用户ID
            **kwargs: 更新字段
            
        Returns:
            更新后的预警对象
        """
        try:
            alert = await self.get_alert_by_id(alert_id, user_id)
            if not alert:
                return None
            
            # 更新字段
            for key, value in kwargs.items():
                if hasattr(alert, key) and value is not None:
                    if key == "condition":
                        # 验证预警条件
                        self._validate_alert_condition(alert.alert_type, value)
                    setattr(alert, key, value)
            
            alert.updated_at = datetime.utcnow()
            await self.db.commit()
            
            return alert
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新预警失败: {e}")
            raise
    
    async def delete_alert(self, alert_id: UUID, user_id: UUID) -> bool:
        """删除预警"""
        try:
            alert = await self.get_alert_by_id(alert_id, user_id)
            if not alert:
                return False
            
            await self.db.delete(alert)
            await self.db.commit()
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除预警失败: {e}")
            return False
    
    async def toggle_alert(self, alert_id: UUID, user_id: UUID) -> Optional[Alert]:
        """切换预警状态"""
        try:
            alert = await self.get_alert_by_id(alert_id, user_id)
            if not alert:
                return None
            
            alert.is_active = not alert.is_active
            alert.updated_at = datetime.utcnow()
            await self.db.commit()
            
            return alert
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"切换预警状态失败: {e}")
            raise
    
    async def check_alerts(self) -> Dict[str, Any]:
        """
        检查所有活跃预警
        
        Returns:
            检查结果统计
        """
        try:
            logger.info("开始检查预警")
            start_time = datetime.utcnow()
            
            # 获取所有活跃预警
            stmt = select(Alert).where(Alert.is_active == True)
            result = await self.db.execute(stmt)
            active_alerts = result.scalars().all()
            
            if not active_alerts:
                return {
                    "checked_count": 0,
                    "triggered_count": 0,
                    "duration": 0
                }
            
            triggered_count = 0
            
            # 按股票代码分组，减少数据查询次数
            stock_alerts = {}
            for alert in active_alerts:
                if alert.stock_code not in stock_alerts:
                    stock_alerts[alert.stock_code] = []
                stock_alerts[alert.stock_code].append(alert)
            
            # 检查每个股票的预警
            for stock_code, alerts in stock_alerts.items():
                try:
                    # 获取股票最新数据
                    stock_data = await self._get_stock_data(stock_code)
                    if not stock_data:
                        continue
                    
                    # 检查该股票的所有预警
                    for alert in alerts:
                        if await self._check_single_alert(alert, stock_data):
                            triggered_count += 1
                            
                except Exception as e:
                    logger.error(f"检查股票预警失败 {stock_code}: {e}")
            
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            result = {
                "checked_count": len(active_alerts),
                "triggered_count": triggered_count,
                "duration": duration,
                "check_time": start_time.isoformat()
            }
            
            logger.info(f"预警检查完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"检查预警失败: {e}")
            raise
    
    async def get_alert_triggers(
        self,
        user_id: UUID,
        skip: int = 0,
        limit: int = 50,
        is_read: Optional[bool] = None
    ) -> Tuple[List[AlertTrigger], int]:
        """
        获取用户预警触发记录
        
        Args:
            user_id: 用户ID
            skip: 跳过数量
            limit: 限制数量
            is_read: 是否已读
            
        Returns:
            (触发记录列表, 总数)
        """
        try:
            # 通过Alert关联查询
            conditions = [Alert.user_id == user_id]
            
            if is_read is not None:
                conditions.append(AlertTrigger.is_read == is_read)
            
            # 查询总数
            count_stmt = (
                select(func.count(AlertTrigger.id))
                .join(Alert, AlertTrigger.alert_id == Alert.id)
                .where(and_(*conditions))
            )
            count_result = await self.db.execute(count_stmt)
            total = count_result.scalar()
            
            # 查询数据
            stmt = (
                select(AlertTrigger)
                .join(Alert, AlertTrigger.alert_id == Alert.id)
                .where(and_(*conditions))
                .order_by(desc(AlertTrigger.trigger_time))
                .offset(skip)
                .limit(limit)
            )
            
            result = await self.db.execute(stmt)
            triggers = result.scalars().all()
            
            return list(triggers), total
            
        except Exception as e:
            logger.error(f"获取预警触发记录失败: {e}")
            raise
    
    async def mark_trigger_as_read(self, trigger_id: UUID, user_id: UUID) -> bool:
        """标记触发记录为已读"""
        try:
            stmt = (
                select(AlertTrigger)
                .join(Alert, AlertTrigger.alert_id == Alert.id)
                .where(
                    and_(
                        AlertTrigger.id == trigger_id,
                        Alert.user_id == user_id
                    )
                )
            )
            result = await self.db.execute(stmt)
            trigger = result.scalar_one_or_none()
            
            if not trigger:
                return False
            
            trigger.is_read = True
            await self.db.commit()
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"标记触发记录为已读失败: {e}")
            return False
    
    def _validate_alert_condition(self, alert_type: str, condition: Dict[str, Any]):
        """验证预警条件"""
        required_fields = {
            "price": ["operator", "value"],
            "change": ["operator", "value"],
            "pct_change": ["operator", "value"],
            "volume": ["operator", "value"],
            "technical": ["indicator", "operator", "value"]
        }
        
        if alert_type not in required_fields:
            raise ValueError(f"不支持的预警类型: {alert_type}")
        
        for field in required_fields[alert_type]:
            if field not in condition:
                raise ValueError(f"预警条件缺少必需字段: {field}")
        
        # 验证操作符
        valid_operators = [">=", "<=", ">", "<", "=="]
        if condition.get("operator") not in valid_operators:
            raise ValueError(f"无效的操作符: {condition.get('operator')}")
    
    async def _get_stock_data(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """获取股票数据"""
        try:
            stock_service = StockService(self.db)
            
            # 获取股票基本信息
            stock = await stock_service.get_stock_by_code(stock_code)
            if not stock:
                return None
            
            # 获取最新实时行情
            realtime_quote = await stock_service.get_latest_realtime_quote(stock.id)
            
            # 获取最新日行情
            quotes = await stock_service.get_stock_quotes(stock.id, limit=1)
            latest_quote = quotes[0] if quotes else None
            
            return {
                "stock": stock,
                "realtime_quote": realtime_quote,
                "latest_quote": latest_quote
            }
            
        except Exception as e:
            logger.error(f"获取股票数据失败 {stock_code}: {e}")
            return None
    
    async def _check_single_alert(self, alert: Alert, stock_data: Dict[str, Any]) -> bool:
        """检查单个预警"""
        try:
            condition = alert.condition
            alert_type = alert.alert_type
            
            # 获取比较值
            current_value = self._get_current_value(alert_type, condition, stock_data)
            if current_value is None:
                return False
            
            # 执行比较
            target_value = float(condition["value"])
            operator = condition["operator"]
            
            triggered = False
            if operator == ">=":
                triggered = current_value >= target_value
            elif operator == "<=":
                triggered = current_value <= target_value
            elif operator == ">":
                triggered = current_value > target_value
            elif operator == "<":
                triggered = current_value < target_value
            elif operator == "==":
                triggered = abs(current_value - target_value) < 0.001
            
            if triggered:
                await self._trigger_alert(alert, current_value, target_value)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查预警失败 {alert.id}: {e}")
            return False
    
    def _get_current_value(
        self,
        alert_type: str,
        condition: Dict[str, Any],
        stock_data: Dict[str, Any]
    ) -> Optional[float]:
        """获取当前值"""
        realtime_quote = stock_data.get("realtime_quote")
        latest_quote = stock_data.get("latest_quote")
        
        # 优先使用实时行情
        quote = realtime_quote or latest_quote
        if not quote:
            return None
        
        if alert_type == "price":
            return float(quote.current_price or quote.close_price or 0)
        elif alert_type == "change":
            return float(quote.change or 0)
        elif alert_type == "pct_change":
            return float(quote.pct_change or 0)
        elif alert_type == "volume":
            return float(quote.volume or 0)
        elif alert_type == "technical":
            # TODO: 实现技术指标预警
            return None
        
        return None
    
    async def _trigger_alert(self, alert: Alert, current_value: float, target_value: float):
        """触发预警"""
        try:
            # 创建触发记录
            trigger = AlertTrigger(
                alert_id=alert.id,
                trigger_value=current_value,
                trigger_time=datetime.utcnow(),
                message=f"{alert.name}: 当前值 {current_value} {alert.condition['operator']} 目标值 {target_value}",
                is_read=False
            )
            
            self.db.add(trigger)
            
            # 更新预警触发次数和最后触发时间
            alert.trigger_count += 1
            alert.last_triggered = datetime.utcnow()
            
            await self.db.commit()
            
            # 发送通知
            await self._send_alert_notification(alert, trigger)
            
            logger.info(f"预警触发: {alert.name} ({alert.stock_code})")
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"触发预警失败: {e}")
    
    async def _send_alert_notification(self, alert: Alert, trigger: AlertTrigger):
        """发送预警通知"""
        try:
            notification_data = {
                "alert_id": str(alert.id),
                "alert_name": alert.name,
                "stock_code": alert.stock_code,
                "trigger_value": float(trigger.trigger_value),
                "message": trigger.message,
                "trigger_time": trigger.trigger_time.isoformat(),
                "alert_type": alert.alert_type
            }
            
            # WebSocket 推送
            if "websocket" in alert.notification_methods:
                await connection_manager.push_alert(str(alert.user_id), notification_data)
            
            # TODO: 实现其他通知方式（邮件、短信等）
            
        except Exception as e:
            logger.error(f"发送预警通知失败: {e}")
