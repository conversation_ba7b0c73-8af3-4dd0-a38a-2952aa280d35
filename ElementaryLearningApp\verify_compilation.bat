@echo off
echo ==========================================
echo 小学学习伴侣 - 编译验证脚本
echo ==========================================
echo.

echo 进入客户端目录...
cd /d "%~dp0client"

echo.
echo 检查Gradle环境...
if exist "gradlew.bat" (
    echo ✅ 找到Gradle Wrapper
    echo.
    echo 开始编译验证...
    echo ==========================================
    gradlew.bat assembleDebug
    
    if errorlevel 1 (
        echo.
        echo ❌ 编译失败！请检查错误信息
        echo.
        echo 常见问题解决方案：
        echo 1. 确保Android SDK已正确安装
        echo 2. 检查网络连接（下载依赖）
        echo 3. 清理项目：gradlew.bat clean
        echo 4. 重新同步项目
    ) else (
        echo.
        echo ✅ 编译成功！
        echo.
        echo 项目状态：
        echo - 所有编译错误已修复
        echo - 可以正常运行应用
        echo - 建议在模拟器中测试功能
    )
) else (
    echo ❌ 未找到Gradle Wrapper
    echo 请在Android Studio中打开项目
)

echo.
echo ==========================================
echo 验证完成
pause
