package com.example.elementarylearningcompanion.repository;

import com.example.elementarylearningcompanion.model.WrongQuestion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface WrongQuestionRepository extends JpaRepository<WrongQuestion, Long> {
    
    List<WrongQuestion> findByStudentIdAndIsMasteredFalseOrderByCreatedAtDesc(Long studentId);
    
    List<WrongQuestion> findByStudentIdAndContentTypeAndIsMasteredFalse(Long studentId, String contentType);
    
    Optional<WrongQuestion> findByStudentIdAndContentTypeAndContentId(Long studentId, String contentType, Long contentId);
    
    @Query("SELECT COUNT(wq) FROM WrongQuestion wq WHERE wq.studentId = :studentId " +
           "AND wq.contentType = :contentType AND wq.isMastered = false")
    Long countUnmasteredQuestions(@Param("studentId") Long studentId, @Param("contentType") String contentType);
    
    @Query("SELECT wq FROM WrongQuestion wq WHERE wq.studentId = :studentId " +
           "AND wq.isMastered = false ORDER BY wq.retryCount ASC, wq.createdAt ASC")
    List<WrongQuestion> findQuestionsForReview(@Param("studentId") Long studentId);
}
