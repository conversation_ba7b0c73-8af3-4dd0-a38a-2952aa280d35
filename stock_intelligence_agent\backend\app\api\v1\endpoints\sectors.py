"""
板块分析 API 端点
"""

from datetime import datetime, date
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db, get_redis
from app.services.stock_service import StockService

router = APIRouter()


@router.get("/hot")
async def get_hot_sectors(
    limit: int = Query(20, ge=1, le=100, description="返回记录数"),
    sort_by: str = Query("pct_change", description="排序字段"),
    order: str = Query("desc", description="排序方向(asc/desc)"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取热门板块
    
    按涨跌幅、成交量等排序
    """
    try:
        stock_service = StockService(db)
        
        hot_sectors = await stock_service.get_hot_sectors(
            limit=limit,
            sort_by=sort_by,
            order=order,
        )
        
        return {
            "hot_sectors": hot_sectors,
            "sort_by": sort_by,
            "order": order,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取热门板块失败: {str(e)}")


@router.get("/funds")
async def get_sector_funds_flow(
    sector: Optional[str] = Query(None, description="板块名称"),
    date_filter: Optional[date] = Query(None, description="日期筛选"),
    limit: int = Query(20, ge=1, le=100, description="返回记录数"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取板块资金流向
    """
    try:
        stock_service = StockService(db)
        
        funds_flow = await stock_service.get_sector_funds_flow(
            sector=sector,
            date_filter=date_filter,
            limit=limit,
        )
        
        return {
            "funds_flow": funds_flow,
            "sector": sector,
            "date": date_filter.isoformat() if date_filter else None,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取资金流向失败: {str(e)}")


@router.get("/ranking")
async def get_sector_ranking(
    sort_by: str = Query("pct_change", description="排序字段"),
    order: str = Query("desc", description="排序方向(asc/desc)"),
    limit: int = Query(50, ge=1, le=200, description="返回记录数"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取板块排行榜
    """
    try:
        stock_service = StockService(db)
        
        sector_ranking = await stock_service.get_sector_ranking(
            sort_by=sort_by,
            order=order,
            limit=limit,
        )
        
        return {
            "sector_ranking": sector_ranking,
            "sort_by": sort_by,
            "order": order,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取板块排行失败: {str(e)}")


@router.get("/{sector_name}/stocks")
async def get_sector_stocks(
    sector_name: str,
    limit: int = Query(50, ge=1, le=200, description="返回记录数"),
    sort_by: str = Query("pct_change", description="排序字段"),
    order: str = Query("desc", description="排序方向(asc/desc)"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取板块内股票列表
    """
    try:
        stock_service = StockService(db)
        
        sector_stocks = await stock_service.get_stocks_by_sector(
            sector_name=sector_name,
            limit=limit,
            sort_by=sort_by,
            order=order,
        )
        
        return {
            "sector_name": sector_name,
            "stocks": sector_stocks,
            "sort_by": sort_by,
            "order": order,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取板块股票失败: {str(e)}")


@router.get("/{sector_name}/analysis")
async def get_sector_analysis(
    sector_name: str,
    period: str = Query("daily", description="分析周期"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取板块分析报告
    """
    try:
        stock_service = StockService(db)
        
        sector_analysis = await stock_service.get_sector_analysis(
            sector_name=sector_name,
            period=period,
        )
        
        return {
            "sector_name": sector_name,
            "period": period,
            "analysis": sector_analysis,
            "timestamp": datetime.utcnow().isoformat(),
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取板块分析失败: {str(e)}")
