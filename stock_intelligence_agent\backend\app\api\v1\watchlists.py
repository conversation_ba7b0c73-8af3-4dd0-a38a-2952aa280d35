"""
自选股相关 API 端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.services.watchlist_service import WatchlistService
from app.auth.security import get_current_user_from_token
from app.schemas.watchlist import (
    WatchlistResponse,
    WatchlistCreateRequest,
    WatchlistUpdateRequest,
    WatchlistWithItemsResponse,
    WatchlistItemResponse,
    AddStockRequest,
    UpdateStockRequest,
    ReorderRequest
)
from app.schemas.base import BaseResponse, PaginationResponse

router = APIRouter()


@router.get("/", response_model=List[WatchlistResponse])
async def get_user_watchlists(
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取用户自选股列表"""
    watchlist_service = WatchlistService(db)
    watchlists = await watchlist_service.get_user_watchlists(
        user_id=current_user["user_id"]
    )
    
    return [WatchlistResponse.from_orm(watchlist) for watchlist in watchlists]


@router.post("/", response_model=WatchlistResponse)
async def create_watchlist(
    watchlist_data: WatchlistCreateRequest,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """创建自选股列表"""
    watchlist_service = WatchlistService(db)
    
    try:
        watchlist = await watchlist_service.create_watchlist(
            user_id=current_user["user_id"],
            name=watchlist_data.name,
            description=watchlist_data.description,
            is_public=watchlist_data.is_public
        )
        
        return WatchlistResponse.from_orm(watchlist)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建自选股列表失败"
        )


@router.get("/public", response_model=PaginationResponse[WatchlistResponse])
async def get_public_watchlists(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取公开的自选股列表"""
    watchlist_service = WatchlistService(db)
    
    watchlists, total = await watchlist_service.get_public_watchlists(
        skip=skip,
        limit=limit
    )
    
    return PaginationResponse(
        data=[WatchlistResponse.from_orm(watchlist) for watchlist in watchlists],
        total=total,
        skip=skip,
        limit=limit,
        has_more=skip + len(watchlists) < total
    )


@router.get("/{watchlist_id}", response_model=WatchlistWithItemsResponse)
async def get_watchlist_detail(
    watchlist_id: str,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取自选股列表详情（包含股票和行情）"""
    watchlist_service = WatchlistService(db)
    
    watchlist_data = await watchlist_service.get_watchlist_with_quotes(
        watchlist_id=watchlist_id,
        user_id=current_user["user_id"]
    )
    
    if not watchlist_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="自选股列表不存在"
        )
    
    return WatchlistWithItemsResponse(**watchlist_data)


@router.put("/{watchlist_id}", response_model=WatchlistResponse)
async def update_watchlist(
    watchlist_id: str,
    watchlist_data: WatchlistUpdateRequest,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """更新自选股列表"""
    watchlist_service = WatchlistService(db)
    
    # 过滤掉空值
    update_data = {k: v for k, v in watchlist_data.dict().items() if v is not None}
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="没有提供更新数据"
        )
    
    try:
        watchlist = await watchlist_service.update_watchlist(
            watchlist_id=watchlist_id,
            user_id=current_user["user_id"],
            **update_data
        )
        
        if not watchlist:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="自选股列表不存在"
            )
        
        return WatchlistResponse.from_orm(watchlist)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新自选股列表失败"
        )


@router.delete("/{watchlist_id}", response_model=BaseResponse)
async def delete_watchlist(
    watchlist_id: str,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """删除自选股列表"""
    watchlist_service = WatchlistService(db)
    success = await watchlist_service.delete_watchlist(
        watchlist_id=watchlist_id,
        user_id=current_user["user_id"]
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="自选股列表不存在"
        )
    
    return BaseResponse(
        success=True,
        message="自选股列表已删除"
    )


@router.post("/{watchlist_id}/stocks", response_model=BaseResponse)
async def add_stock_to_watchlist(
    watchlist_id: str,
    stock_data: AddStockRequest,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """添加股票到自选股列表"""
    watchlist_service = WatchlistService(db)
    
    try:
        await watchlist_service.add_stock_to_watchlist(
            watchlist_id=watchlist_id,
            user_id=current_user["user_id"],
            stock_code=stock_data.stock_code,
            notes=stock_data.notes
        )
        
        return BaseResponse(
            success=True,
            message="股票已添加到自选股"
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="添加股票失败"
        )


@router.delete("/{watchlist_id}/stocks/{stock_code}", response_model=BaseResponse)
async def remove_stock_from_watchlist(
    watchlist_id: str,
    stock_code: str,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """从自选股列表移除股票"""
    watchlist_service = WatchlistService(db)
    success = await watchlist_service.remove_stock_from_watchlist(
        watchlist_id=watchlist_id,
        user_id=current_user["user_id"],
        stock_code=stock_code
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="股票不在自选股列表中"
        )
    
    return BaseResponse(
        success=True,
        message="股票已从自选股移除"
    )


@router.put("/{watchlist_id}/stocks/{stock_code}", response_model=BaseResponse)
async def update_watchlist_stock(
    watchlist_id: str,
    stock_code: str,
    stock_data: UpdateStockRequest,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """更新自选股项目"""
    watchlist_service = WatchlistService(db)
    
    # 过滤掉空值
    update_data = {k: v for k, v in stock_data.dict().items() if v is not None}
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="没有提供更新数据"
        )
    
    try:
        item = await watchlist_service.update_watchlist_item(
            watchlist_id=watchlist_id,
            user_id=current_user["user_id"],
            stock_code=stock_code,
            **update_data
        )
        
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="股票不在自选股列表中"
            )
        
        return BaseResponse(
            success=True,
            message="自选股项目已更新"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新自选股项目失败"
        )


@router.post("/{watchlist_id}/reorder", response_model=BaseResponse)
async def reorder_watchlist_stocks(
    watchlist_id: str,
    reorder_data: ReorderRequest,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """重新排序自选股"""
    watchlist_service = WatchlistService(db)
    
    success = await watchlist_service.reorder_watchlist_items(
        watchlist_id=watchlist_id,
        user_id=current_user["user_id"],
        item_orders=reorder_data.items
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="自选股列表不存在"
        )
    
    return BaseResponse(
        success=True,
        message="自选股排序已更新"
    )


@router.post("/{watchlist_id}/copy", response_model=WatchlistResponse)
async def copy_watchlist(
    watchlist_id: str,
    new_name: Optional[str] = Query(None, description="新列表名称"),
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """复制自选股列表"""
    watchlist_service = WatchlistService(db)
    
    try:
        new_watchlist = await watchlist_service.copy_watchlist(
            source_watchlist_id=watchlist_id,
            target_user_id=current_user["user_id"],
            new_name=new_name
        )
        
        if not new_watchlist:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="自选股列表不存在或无权访问"
            )
        
        return WatchlistResponse.from_orm(new_watchlist)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="复制自选股列表失败"
        )
