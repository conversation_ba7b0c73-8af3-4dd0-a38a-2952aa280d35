@echo off
setlocal enabledelayedexpansion

REM Elementary Learning App Build Script for Windows
REM 小学学习伴侣Windows构建脚本

echo ==========================================
echo Elementary Learning App Build Script
echo 小学学习伴侣构建脚本
echo ==========================================

REM Set variables
set PROJECT_ROOT=%CD%
set SERVER_DIR=%PROJECT_ROOT%\server
set CLIENT_DIR=%PROJECT_ROOT%\client
set BUILD_DIR=%PROJECT_ROOT%\build
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set TIMESTAMP=%dt:~0,8%_%dt:~8,6%

REM Create build directory
echo Creating build directory...
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"

REM Check prerequisites
echo Checking prerequisites...

java -version >nul 2>&1
if errorlevel 1 (
    echo Error: Java is not installed or not in PATH
    exit /b 1
)

REM Java is already checked above

echo Prerequisites check passed!

REM Build Spring Boot Server
echo ==========================================
echo Building Spring Boot Server...
echo ==========================================

cd /d "%SERVER_DIR%"

REM Clean and build
echo Cleaning previous builds...
call gradlew.bat clean

echo Building server JAR...
call gradlew.bat bootJar -x test

if errorlevel 1 (
    echo Server build failed!
    exit /b 1
)

echo Server build successful!

REM Copy JAR to build directory
for %%f in (build\libs\*.jar) do (
    copy "%%f" "%BUILD_DIR%\elementary-learning-server-%TIMESTAMP%.jar"
    echo Server JAR copied to build directory
)

REM Build Android Client
echo ==========================================
echo Building Android Client...
echo ==========================================

cd /d "%CLIENT_DIR%"

REM Check Android SDK
if "%ANDROID_HOME%"=="" (
    echo Warning: ANDROID_HOME is not set. Trying to detect Android SDK...
    
    REM Check common locations
    if exist "%USERPROFILE%\AppData\Local\Android\Sdk" (
        set ANDROID_HOME=%USERPROFILE%\AppData\Local\Android\Sdk
        echo Found Android SDK at: !ANDROID_HOME!
    ) else if exist "C:\Android\Sdk" (
        set ANDROID_HOME=C:\Android\Sdk
        echo Found Android SDK at: !ANDROID_HOME!
    ) else (
        echo Error: Android SDK not found. Please set ANDROID_HOME environment variable.
        exit /b 1
    )
)

REM Add Android tools to PATH
set PATH=%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools;%PATH%

echo Cleaning previous builds...
if exist "gradlew.bat" (
    call gradlew.bat clean
) else (
    gradle clean
)

echo Building debug APK...
if exist "gradlew.bat" (
    call gradlew.bat assembleDebug
) else (
    gradle assembleDebug
)

if errorlevel 1 (
    echo Android build failed!
    exit /b 1
)

echo Android build successful!

REM Copy APK to build directory
for /r app\build\outputs\apk\debug %%f in (*.apk) do (
    copy "%%f" "%BUILD_DIR%\elementary-learning-app-%TIMESTAMP%.apk"
    echo APK copied to build directory
)

REM Create deployment package
echo ==========================================
echo Creating deployment package...
echo ==========================================

cd /d "%BUILD_DIR%"

REM Create deployment directory
set DEPLOY_DIR=elementary-learning-%TIMESTAMP%
mkdir "%DEPLOY_DIR%"

REM Copy files
if exist "elementary-learning-server-%TIMESTAMP%.jar" (
    copy "elementary-learning-server-%TIMESTAMP%.jar" "%DEPLOY_DIR%\"
)

if exist "elementary-learning-app-%TIMESTAMP%.apk" (
    copy "elementary-learning-app-%TIMESTAMP%.apk" "%DEPLOY_DIR%\"
)

REM Copy documentation and scripts
if exist "%PROJECT_ROOT%\README.md" copy "%PROJECT_ROOT%\README.md" "%DEPLOY_DIR%\"
if exist "%PROJECT_ROOT%\server\database_schema.sql" copy "%PROJECT_ROOT%\server\database_schema.sql" "%DEPLOY_DIR%\"
if exist "%PROJECT_ROOT%\server\sample_data.sql" copy "%PROJECT_ROOT%\server\sample_data.sql" "%DEPLOY_DIR%\"

REM Create deployment instructions
echo # Elementary Learning App Deployment Guide > "%DEPLOY_DIR%\DEPLOYMENT.md"
echo # 小学学习伴侣部署指南 >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo ## 服务器部署 (Server Deployment) >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo ### 前置条件 (Prerequisites) >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo - Java 17 或更高版本 >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo - MySQL 8.0 或更高版本 >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo ### 部署步骤 (Deployment Steps) >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo 1. 创建数据库 (Create Database) >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo    ```sql >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo    CREATE DATABASE elementary_learning CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo    ``` >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo 2. 导入数据库结构 (Import Database Schema) >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo    ```bash >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo    mysql -u root -p elementary_learning ^< database_schema.sql >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo    ``` >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo 3. 导入示例数据 (Import Sample Data) >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo    ```bash >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo    mysql -u root -p elementary_learning ^< sample_data.sql >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo    ``` >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo 4. 启动服务器 (Start Server) >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo    ```bash >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo    java -jar elementary-learning-server-%TIMESTAMP%.jar >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo    ``` >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo 服务器将在 http://localhost:8080 启动 >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo ## Android应用安装 (Android App Installation) >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo 1. 在Android设备上启用"未知来源"安装 >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo 2. 将APK文件传输到设备 >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo 3. 安装 elementary-learning-app-%TIMESTAMP%.apk >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo. >> "%DEPLOY_DIR%\DEPLOYMENT.md"
echo 构建时间：%TIMESTAMP% >> "%DEPLOY_DIR%\DEPLOYMENT.md"

REM Create archive (if 7zip is available)
where 7z >nul 2>&1
if not errorlevel 1 (
    echo Creating deployment archive...
    7z a "elementary-learning-%TIMESTAMP%.zip" "%DEPLOY_DIR%"
) else (
    echo 7zip not found, skipping archive creation
)

echo ==========================================
echo Build completed successfully!
echo ==========================================
echo Build artifacts:
echo - Server JAR: %BUILD_DIR%\elementary-learning-server-%TIMESTAMP%.jar
echo - Android APK: %BUILD_DIR%\elementary-learning-app-%TIMESTAMP%.apk
if exist "elementary-learning-%TIMESTAMP%.zip" echo - Deployment package: %BUILD_DIR%\elementary-learning-%TIMESTAMP%.zip
echo.
echo Deployment directory: %BUILD_DIR%\%DEPLOY_DIR%
echo ==========================================

cd /d "%PROJECT_ROOT%"
pause
