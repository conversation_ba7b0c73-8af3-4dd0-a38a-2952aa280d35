"""
数据处理模块
负责数据清洗、标准化和验证
"""

import logging
from datetime import datetime, date
from typing import List, Dict, Any, Optional
from decimal import Decimal, InvalidOperation
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)


class DataProcessor:
    """数据处理器"""
    
    @staticmethod
    def clean_stock_data(raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗股票数据
        
        Args:
            raw_data: 原始股票数据
            
        Returns:
            清洗后的股票数据
        """
        try:
            cleaned_data = {}
            
            # 股票代码处理
            code = raw_data.get('code', '').strip()
            if code:
                # 确保股票代码格式正确
                if len(code) == 6 and code.isdigit():
                    cleaned_data['code'] = code
                else:
                    raise ValueError(f"无效的股票代码: {code}")
            
            # 股票名称处理
            name = raw_data.get('name', '').strip()
            if name:
                cleaned_data['name'] = name
            
            # 市场处理
            market = raw_data.get('market', '').upper().strip()
            if market in ['SH', 'SZ']:
                cleaned_data['market'] = market
            elif code:
                # 根据代码推断市场
                if code.startswith(('60', '68', '11', '13')):
                    cleaned_data['market'] = 'SH'
                elif code.startswith(('00', '30', '12')):
                    cleaned_data['market'] = 'SZ'
                else:
                    cleaned_data['market'] = 'SH'  # 默认上海
            
            # 行业处理
            industry = raw_data.get('industry', '').strip()
            if industry:
                cleaned_data['industry'] = industry
            
            # 板块处理
            sector = raw_data.get('sector', '').strip()
            if sector:
                cleaned_data['sector'] = sector
            
            # 上市日期处理
            list_date = raw_data.get('list_date')
            if list_date:
                cleaned_data['list_date'] = DataProcessor._parse_date(list_date)
            
            # 股本数据处理
            total_share = raw_data.get('total_share')
            if total_share is not None:
                cleaned_data['total_share'] = DataProcessor._parse_decimal(total_share)
            
            float_share = raw_data.get('float_share')
            if float_share is not None:
                cleaned_data['float_share'] = DataProcessor._parse_decimal(float_share)
            
            return cleaned_data
            
        except Exception as e:
            logger.error(f"清洗股票数据失败: {e}")
            raise
    
    @staticmethod
    def clean_quote_data(raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗行情数据
        
        Args:
            raw_data: 原始行情数据
            
        Returns:
            清洗后的行情数据
        """
        try:
            cleaned_data = {}
            
            # 交易日期处理
            trade_date = raw_data.get('trade_date')
            if trade_date:
                cleaned_data['trade_date'] = DataProcessor._parse_date(trade_date)
            
            # 价格数据处理
            price_fields = [
                'open_price', 'high_price', 'low_price', 'close_price', 'pre_close'
            ]
            for field in price_fields:
                value = raw_data.get(field)
                if value is not None:
                    cleaned_price = DataProcessor._parse_decimal(value, precision=3)
                    if cleaned_price is not None and cleaned_price > 0:
                        cleaned_data[field] = cleaned_price
            
            # 成交数据处理
            volume = raw_data.get('volume')
            if volume is not None:
                cleaned_volume = DataProcessor._parse_decimal(volume, precision=2)
                if cleaned_volume is not None and cleaned_volume >= 0:
                    cleaned_data['volume'] = cleaned_volume
            
            amount = raw_data.get('amount')
            if amount is not None:
                cleaned_amount = DataProcessor._parse_decimal(amount, precision=2)
                if cleaned_amount is not None and cleaned_amount >= 0:
                    cleaned_data['amount'] = cleaned_amount
            
            # 涨跌数据处理
            change = raw_data.get('change')
            if change is not None:
                cleaned_data['change'] = DataProcessor._parse_decimal(change, precision=3)
            
            pct_change = raw_data.get('pct_change')
            if pct_change is not None:
                cleaned_pct = DataProcessor._parse_decimal(pct_change, precision=4)
                # 限制涨跌幅范围 (-20%, +20%)
                if cleaned_pct is not None and -20 <= cleaned_pct <= 20:
                    cleaned_data['pct_change'] = cleaned_pct
            
            # 换手率处理
            turnover_rate = raw_data.get('turnover_rate')
            if turnover_rate is not None:
                cleaned_turnover = DataProcessor._parse_decimal(turnover_rate, precision=4)
                if cleaned_turnover is not None and 0 <= cleaned_turnover <= 100:
                    cleaned_data['turnover_rate'] = cleaned_turnover
            
            # 估值数据处理
            pe_ratio = raw_data.get('pe_ratio')
            if pe_ratio is not None:
                cleaned_pe = DataProcessor._parse_decimal(pe_ratio, precision=4)
                if cleaned_pe is not None and 0 < cleaned_pe < 1000:
                    cleaned_data['pe_ratio'] = cleaned_pe
            
            pb_ratio = raw_data.get('pb_ratio')
            if pb_ratio is not None:
                cleaned_pb = DataProcessor._parse_decimal(pb_ratio, precision=4)
                if cleaned_pb is not None and 0 < cleaned_pb < 100:
                    cleaned_data['pb_ratio'] = cleaned_pb
            
            # 市值数据处理
            market_cap = raw_data.get('market_cap')
            if market_cap is not None:
                cleaned_cap = DataProcessor._parse_decimal(market_cap, precision=2)
                if cleaned_cap is not None and cleaned_cap > 0:
                    cleaned_data['market_cap'] = cleaned_cap
            
            float_cap = raw_data.get('float_cap')
            if float_cap is not None:
                cleaned_float_cap = DataProcessor._parse_decimal(float_cap, precision=2)
                if cleaned_float_cap is not None and cleaned_float_cap > 0:
                    cleaned_data['float_cap'] = cleaned_float_cap
            
            return cleaned_data
            
        except Exception as e:
            logger.error(f"清洗行情数据失败: {e}")
            raise
    
    @staticmethod
    def clean_realtime_data(raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗实时数据
        
        Args:
            raw_data: 原始实时数据
            
        Returns:
            清洗后的实时数据
        """
        try:
            cleaned_data = {}
            
            # 基础价格数据
            price_fields = [
                'current_price', 'open_price', 'high_price', 'low_price', 'pre_close'
            ]
            for field in price_fields:
                value = raw_data.get(field)
                if value is not None:
                    cleaned_price = DataProcessor._parse_decimal(value, precision=3)
                    if cleaned_price is not None and cleaned_price > 0:
                        cleaned_data[field] = cleaned_price
            
            # 买卖盘数据
            bid_ask_fields = [
                'bid1_price', 'ask1_price'
            ]
            for field in bid_ask_fields:
                value = raw_data.get(field)
                if value is not None:
                    cleaned_price = DataProcessor._parse_decimal(value, precision=3)
                    if cleaned_price is not None and cleaned_price > 0:
                        cleaned_data[field] = cleaned_price
            
            volume_fields = [
                'bid1_volume', 'ask1_volume'
            ]
            for field in volume_fields:
                value = raw_data.get(field)
                if value is not None:
                    cleaned_volume = DataProcessor._parse_decimal(value, precision=0)
                    if cleaned_volume is not None and cleaned_volume >= 0:
                        cleaned_data[field] = cleaned_volume
            
            # 成交数据
            volume = raw_data.get('volume')
            if volume is not None:
                cleaned_volume = DataProcessor._parse_decimal(volume, precision=2)
                if cleaned_volume is not None and cleaned_volume >= 0:
                    cleaned_data['volume'] = cleaned_volume
            
            amount = raw_data.get('amount')
            if amount is not None:
                cleaned_amount = DataProcessor._parse_decimal(amount, precision=2)
                if cleaned_amount is not None and cleaned_amount >= 0:
                    cleaned_data['amount'] = cleaned_amount
            
            # 涨跌数据
            change = raw_data.get('change')
            if change is not None:
                cleaned_data['change'] = DataProcessor._parse_decimal(change, precision=3)
            
            pct_change = raw_data.get('pct_change')
            if pct_change is not None:
                cleaned_pct = DataProcessor._parse_decimal(pct_change, precision=4)
                if cleaned_pct is not None and -20 <= cleaned_pct <= 20:
                    cleaned_data['pct_change'] = cleaned_pct
            
            # 时间戳处理
            timestamp = raw_data.get('timestamp')
            if timestamp:
                if isinstance(timestamp, datetime):
                    cleaned_data['timestamp'] = timestamp
                else:
                    cleaned_data['timestamp'] = DataProcessor._parse_datetime(timestamp)
            else:
                cleaned_data['timestamp'] = datetime.now()
            
            return cleaned_data
            
        except Exception as e:
            logger.error(f"清洗实时数据失败: {e}")
            raise
    
    @staticmethod
    def clean_financial_data(raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗财务数据
        
        Args:
            raw_data: 原始财务数据
            
        Returns:
            清洗后的财务数据
        """
        try:
            cleaned_data = {}
            
            # 报告期处理
            report_date = raw_data.get('report_date')
            if report_date:
                cleaned_data['report_date'] = DataProcessor._parse_date(report_date)
            
            # 报告类型处理
            report_type = raw_data.get('report_type', '').upper().strip()
            if report_type in ['Q1', 'Q2', 'Q3', 'A']:
                cleaned_data['report_type'] = report_type
            
            # 财务指标处理
            financial_fields = [
                'revenue', 'net_profit', 'gross_profit', 'operating_profit',
                'total_assets', 'total_liabilities', 'total_equity',
                'operating_cash_flow', 'investing_cash_flow', 'financing_cash_flow'
            ]
            
            for field in financial_fields:
                value = raw_data.get(field)
                if value is not None:
                    cleaned_value = DataProcessor._parse_decimal(value, precision=2)
                    if cleaned_value is not None:
                        cleaned_data[field] = cleaned_value
            
            # 比率指标处理
            ratio_fields = [
                'revenue_growth', 'profit_growth', 'roe', 'roa',
                'gross_margin', 'net_margin', 'debt_ratio', 'current_ratio',
                'inventory_turnover', 'receivable_turnover', 'total_asset_turnover'
            ]
            
            for field in ratio_fields:
                value = raw_data.get(field)
                if value is not None:
                    cleaned_value = DataProcessor._parse_decimal(value, precision=4)
                    # 限制比率范围
                    if cleaned_value is not None and -100 <= cleaned_value <= 1000:
                        cleaned_data[field] = cleaned_value
            
            # 每股指标处理
            per_share_fields = ['eps', 'bps']
            for field in per_share_fields:
                value = raw_data.get(field)
                if value is not None:
                    cleaned_value = DataProcessor._parse_decimal(value, precision=4)
                    if cleaned_value is not None:
                        cleaned_data[field] = cleaned_value
            
            return cleaned_data
            
        except Exception as e:
            logger.error(f"清洗财务数据失败: {e}")
            raise
    
    @staticmethod
    def validate_data_integrity(data: Dict[str, Any], required_fields: List[str]) -> bool:
        """
        验证数据完整性
        
        Args:
            data: 数据字典
            required_fields: 必需字段列表
            
        Returns:
            是否通过验证
        """
        try:
            for field in required_fields:
                if field not in data or data[field] is None:
                    logger.warning(f"缺少必需字段: {field}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"数据完整性验证失败: {e}")
            return False
    
    @staticmethod
    def detect_anomalies(data: List[Dict[str, Any]], field: str, threshold: float = 3.0) -> List[int]:
        """
        检测异常数据
        
        Args:
            data: 数据列表
            field: 检测字段
            threshold: 异常阈值(标准差倍数)
            
        Returns:
            异常数据索引列表
        """
        try:
            values = []
            for item in data:
                value = item.get(field)
                if value is not None and isinstance(value, (int, float, Decimal)):
                    values.append(float(value))
            
            if len(values) < 3:
                return []
            
            # 计算统计指标
            mean_val = np.mean(values)
            std_val = np.std(values)
            
            if std_val == 0:
                return []
            
            # 检测异常值
            anomalies = []
            for i, item in enumerate(data):
                value = item.get(field)
                if value is not None:
                    z_score = abs((float(value) - mean_val) / std_val)
                    if z_score > threshold:
                        anomalies.append(i)
            
            return anomalies
            
        except Exception as e:
            logger.error(f"异常检测失败: {e}")
            return []
    
    @staticmethod
    def _parse_decimal(value: Any, precision: int = 2) -> Optional[Decimal]:
        """解析 Decimal 值"""
        try:
            if value is None or value == '' or pd.isna(value):
                return None
            
            if isinstance(value, Decimal):
                return value
            
            # 处理字符串
            if isinstance(value, str):
                value = value.strip().replace(',', '').replace('%', '')
                if value == '' or value == '-':
                    return None
            
            decimal_value = Decimal(str(value))
            return decimal_value.quantize(Decimal('0.' + '0' * precision))
            
        except (InvalidOperation, ValueError, TypeError):
            return None
    
    @staticmethod
    def _parse_date(value: Any) -> Optional[date]:
        """解析日期值"""
        try:
            if value is None:
                return None
            
            if isinstance(value, date):
                return value
            
            if isinstance(value, datetime):
                return value.date()
            
            if isinstance(value, str):
                # 尝试多种日期格式
                formats = ['%Y-%m-%d', '%Y%m%d', '%Y/%m/%d']
                for fmt in formats:
                    try:
                        return datetime.strptime(value, fmt).date()
                    except ValueError:
                        continue
            
            return None
            
        except Exception:
            return None
    
    @staticmethod
    def _parse_datetime(value: Any) -> Optional[datetime]:
        """解析日期时间值"""
        try:
            if value is None:
                return None
            
            if isinstance(value, datetime):
                return value
            
            if isinstance(value, str):
                # 尝试多种日期时间格式
                formats = [
                    '%Y-%m-%d %H:%M:%S',
                    '%Y-%m-%d %H:%M:%S.%f',
                    '%Y%m%d %H:%M:%S',
                    '%Y/%m/%d %H:%M:%S'
                ]
                for fmt in formats:
                    try:
                        return datetime.strptime(value, fmt)
                    except ValueError:
                        continue
            
            return None
            
        except Exception:
            return None
