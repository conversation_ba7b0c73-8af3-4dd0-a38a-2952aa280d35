"""
WebSocket 数据推送器
负责主动推送实时数据到客户端
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import redis.asyncio as redis
from sqlalchemy.ext.asyncio import AsyncSession

from app.websocket.connection_manager import ConnectionManager
from app.core.database import AsyncSessionLocal, get_redis
from app.services.stock_service import StockService
from app.data_collector.akshare_client import AkshareClient
from app.core.config import settings

logger = logging.getLogger(__name__)


class DataPusher:
    """数据推送器"""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager
        self.redis: Optional[redis.Redis] = None
        self.akshare_client = AkshareClient()
        
        # 推送任务
        self.realtime_task: Optional[asyncio.Task] = None
        self.market_overview_task: Optional[asyncio.Task] = None
        self.news_task: Optional[asyncio.Task] = None
        
        # 推送状态
        self.is_running = False
        
        logger.info("数据推送器已初始化")
    
    async def start(self):
        """启动数据推送"""
        if self.is_running:
            return
        
        self.is_running = True
        self.redis = await get_redis()
        
        # 启动各种推送任务
        self.realtime_task = asyncio.create_task(self._realtime_data_pusher())
        self.market_overview_task = asyncio.create_task(self._market_overview_pusher())
        self.news_task = asyncio.create_task(self._news_pusher())
        
        logger.info("数据推送器已启动")
    
    async def stop(self):
        """停止数据推送"""
        self.is_running = False
        
        # 取消所有任务
        tasks = [self.realtime_task, self.market_overview_task, self.news_task]
        for task in tasks:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        await self.akshare_client.close()
        logger.info("数据推送器已停止")
    
    async def _realtime_data_pusher(self):
        """实时数据推送任务"""
        logger.info("实时数据推送任务已启动")
        
        while self.is_running:
            try:
                # 获取所有订阅的股票
                subscribed_stocks = list(self.connection_manager.stock_subscriptions.keys())
                
                if subscribed_stocks:
                    # 批量获取实时行情
                    realtime_quotes = await self.akshare_client.get_realtime_quotes(subscribed_stocks)
                    
                    # 推送数据到订阅的客户端
                    for quote in realtime_quotes:
                        stock_code = quote.get('code')
                        if stock_code:
                            # 缓存到 Redis
                            await self._cache_realtime_data(stock_code, quote)
                            
                            # 推送到 WebSocket 客户端
                            await self.connection_manager.push_stock_data(stock_code, quote)
                
                # 等待下一次推送
                await asyncio.sleep(settings.DATA_COLLECTION_INTERVAL)
                
            except Exception as e:
                logger.error(f"实时数据推送失败: {e}")
                await asyncio.sleep(10)  # 错误时等待更长时间
    
    async def _market_overview_pusher(self):
        """市场概览推送任务"""
        logger.info("市场概览推送任务已启动")
        
        while self.is_running:
            try:
                # 每分钟推送一次市场概览
                await asyncio.sleep(60)
                
                if not self.connection_manager.active_connections:
                    continue
                
                # 获取市场概览数据
                market_overview = await self._get_market_overview()
                
                # 广播市场概览
                message = {
                    "type": "market_overview",
                    "data": market_overview,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                await self.connection_manager.broadcast_message(message)
                
            except Exception as e:
                logger.error(f"市场概览推送失败: {e}")
                await asyncio.sleep(60)
    
    async def _news_pusher(self):
        """新闻推送任务"""
        logger.info("新闻推送任务已启动")
        
        last_check_time = datetime.utcnow()
        
        while self.is_running:
            try:
                # 每30秒检查一次新闻
                await asyncio.sleep(30)
                
                if not self.connection_manager.news_subscriptions:
                    continue
                
                # 获取最新新闻
                latest_news = await self._get_latest_news(last_check_time)
                
                # 推送新闻到订阅的客户端
                for news_item in latest_news:
                    category = news_item.get('category', 'general')
                    await self.connection_manager.push_news_data(category, news_item)
                
                last_check_time = datetime.utcnow()
                
            except Exception as e:
                logger.error(f"新闻推送失败: {e}")
                await asyncio.sleep(60)
    
    async def _cache_realtime_data(self, stock_code: str, quote_data: Dict[str, Any]):
        """缓存实时数据到 Redis"""
        try:
            if not self.redis:
                return
            
            cache_key = f"realtime_quote:{stock_code}"
            cache_data = {
                'current_price': str(quote_data.get('current_price', 0)),
                'change': str(quote_data.get('change', 0)),
                'pct_change': str(quote_data.get('pct_change', 0)),
                'volume': str(quote_data.get('volume', 0)),
                'amount': str(quote_data.get('amount', 0)),
                'timestamp': quote_data.get('timestamp', datetime.now()).isoformat(),
            }
            
            await self.redis.hset(cache_key, mapping=cache_data)
            await self.redis.expire(cache_key, settings.CACHE_TTL_REALTIME)
            
        except Exception as e:
            logger.warning(f"缓存实时数据失败: {e}")
    
    async def _get_market_overview(self) -> Dict[str, Any]:
        """获取市场概览数据"""
        try:
            async with AsyncSessionLocal() as db:
                stock_service = StockService(db, self.redis)
                
                # 获取热门股票
                hot_stocks = await stock_service.get_hot_stocks(limit=10)
                
                # 获取统计信息
                statistics = await stock_service.get_stock_statistics()
                
                # 获取市场指数（模拟数据）
                market_indices = await self._get_market_indices()
                
                return {
                    "hot_stocks": hot_stocks,
                    "statistics": statistics,
                    "market_indices": market_indices,
                    "market_status": self._get_market_status()
                }
                
        except Exception as e:
            logger.error(f"获取市场概览失败: {e}")
            return {}
    
    async def _get_market_indices(self) -> List[Dict[str, Any]]:
        """获取市场指数数据"""
        try:
            # 这里可以集成真实的指数数据
            # 目前返回模拟数据
            indices = [
                {
                    "code": "000001",
                    "name": "上证指数",
                    "current": 3200.50,
                    "change": 15.30,
                    "pct_change": 0.48
                },
                {
                    "code": "399001",
                    "name": "深证成指",
                    "current": 12500.80,
                    "change": -25.60,
                    "pct_change": -0.20
                },
                {
                    "code": "399006",
                    "name": "创业板指",
                    "current": 2800.90,
                    "change": 8.70,
                    "pct_change": 0.31
                }
            ]
            
            return indices
            
        except Exception as e:
            logger.error(f"获取市场指数失败: {e}")
            return []
    
    async def _get_latest_news(self, since_time: datetime) -> List[Dict[str, Any]]:
        """获取最新新闻"""
        try:
            # 这里应该从新闻数据库或API获取最新新闻
            # 目前返回模拟数据
            news_items = []
            
            # 模拟一些新闻数据
            if datetime.utcnow().minute % 5 == 0:  # 每5分钟模拟一条新闻
                news_items.append({
                    "id": f"news_{datetime.utcnow().timestamp()}",
                    "title": "市场重要资讯",
                    "summary": "这是一条模拟的重要市场资讯...",
                    "category": "market",
                    "importance": 3,
                    "published_at": datetime.utcnow().isoformat(),
                    "related_stocks": ["000001", "000002"]
                })
            
            return news_items
            
        except Exception as e:
            logger.error(f"获取最新新闻失败: {e}")
            return []
    
    def _get_market_status(self) -> Dict[str, Any]:
        """获取市场状态"""
        now = datetime.now()
        hour = now.hour
        minute = now.minute
        weekday = now.weekday()  # 0=Monday, 6=Sunday
        
        # 判断是否为交易日（周一到周五）
        is_trading_day = weekday < 5
        
        # 判断是否为交易时间
        is_trading_time = False
        session = "closed"
        
        if is_trading_day:
            # 上午交易时间 9:30-11:30
            if (hour == 9 and minute >= 30) or (hour == 10) or (hour == 11 and minute <= 30):
                is_trading_time = True
                session = "morning"
            # 下午交易时间 13:00-15:00
            elif (hour == 13) or (hour == 14) or (hour == 15 and minute == 0):
                is_trading_time = True
                session = "afternoon"
            # 集合竞价时间
            elif (hour == 9 and minute < 30):
                session = "call_auction"
        
        return {
            "is_trading_day": is_trading_day,
            "is_trading_time": is_trading_time,
            "session": session,
            "current_time": now.isoformat()
        }
    
    async def push_alert_to_user(self, user_id: str, alert_data: Dict[str, Any]):
        """推送预警给特定用户"""
        try:
            await self.connection_manager.push_alert(user_id, alert_data)
            logger.info(f"预警已推送给用户 {user_id}")
            
        except Exception as e:
            logger.error(f"推送预警失败: {e}")
    
    async def push_system_notification(self, notification: Dict[str, Any]):
        """推送系统通知"""
        try:
            message = {
                "type": "system_notification",
                "data": notification,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await self.connection_manager.broadcast_message(message)
            logger.info("系统通知已广播")
            
        except Exception as e:
            logger.error(f"推送系统通知失败: {e}")
    
    async def push_trading_signal(self, signal_data: Dict[str, Any]):
        """推送交易信号"""
        try:
            stock_code = signal_data.get('stock_code')
            if not stock_code:
                return
            
            message = {
                "type": "trading_signal",
                "stock_code": stock_code,
                "data": signal_data,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # 推送给订阅该股票的用户
            await self.connection_manager.push_stock_data(stock_code, message)
            logger.info(f"交易信号已推送: {stock_code}")
            
        except Exception as e:
            logger.error(f"推送交易信号失败: {e}")
    
    def get_pusher_stats(self) -> Dict[str, Any]:
        """获取推送器统计信息"""
        return {
            "is_running": self.is_running,
            "active_tasks": {
                "realtime": self.realtime_task and not self.realtime_task.done(),
                "market_overview": self.market_overview_task and not self.market_overview_task.done(),
                "news": self.news_task and not self.news_task.done()
            },
            "subscribed_stocks": len(self.connection_manager.stock_subscriptions),
            "news_subscriptions": len(self.connection_manager.news_subscriptions),
            "total_connections": len(self.connection_manager.active_connections)
        }


# 全局数据推送器实例
data_pusher = None

def get_data_pusher(connection_manager: ConnectionManager) -> DataPusher:
    """获取数据推送器实例"""
    global data_pusher
    if data_pusher is None:
        data_pusher = DataPusher(connection_manager)
    return data_pusher
