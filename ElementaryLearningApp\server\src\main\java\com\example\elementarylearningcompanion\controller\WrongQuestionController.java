package com.example.elementarylearningcompanion.controller;

import com.example.elementarylearningcompanion.dto.ApiResponse;
import com.example.elementarylearningcompanion.model.WrongQuestion;
import com.example.elementarylearningcompanion.service.WrongQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/wrong-questions")
public class WrongQuestionController {

    @Autowired
    private WrongQuestionService wrongQuestionService;

    @GetMapping("/{studentId}")
    public ResponseEntity<List<WrongQuestion>> getAllWrongQuestions(@PathVariable Long studentId) {
        List<WrongQuestion> wrongQuestions = wrongQuestionService.getAllWrongQuestions(studentId);
        return ResponseEntity.ok(wrongQuestions);
    }

    @GetMapping("/{studentId}/by-type")
    public ResponseEntity<List<WrongQuestion>> getWrongQuestionsByType(
            @PathVariable Long studentId,
            @RequestParam String contentType) {
        List<WrongQuestion> wrongQuestions = wrongQuestionService.getWrongQuestionsByType(studentId, contentType);
        return ResponseEntity.ok(wrongQuestions);
    }

    @GetMapping("/{studentId}/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics(@PathVariable Long studentId) {
        Map<String, Object> stats = wrongQuestionService.getWrongQuestionStatistics(studentId);
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/{studentId}/detailed")
    public ResponseEntity<List<Map<String, Object>>> getWrongQuestionsWithDetails(@PathVariable Long studentId) {
        List<Map<String, Object>> detailedQuestions = wrongQuestionService.getWrongQuestionsWithDetails(studentId);
        return ResponseEntity.ok(detailedQuestions);
    }

    @GetMapping("/{studentId}/review")
    public ResponseEntity<List<WrongQuestion>> getQuestionsForReview(
            @PathVariable Long studentId,
            @RequestParam(defaultValue = "10") Integer limit) {
        List<WrongQuestion> reviewQuestions = wrongQuestionService.getQuestionsForReview(studentId, limit);
        return ResponseEntity.ok(reviewQuestions);
    }

    @GetMapping("/{studentId}/suggestions")
    public ResponseEntity<Map<String, Object>> getReviewSuggestions(@PathVariable Long studentId) {
        Map<String, Object> suggestions = wrongQuestionService.getReviewSuggestions(studentId);
        return ResponseEntity.ok(suggestions);
    }

    @PostMapping("/{studentId}/mark-mastered/{wrongQuestionId}")
    public ResponseEntity<ApiResponse> markAsMastered(
            @PathVariable Long studentId,
            @PathVariable Long wrongQuestionId) {
        boolean success = wrongQuestionService.markAsMastered(studentId, wrongQuestionId);
        if (success) {
            return ResponseEntity.ok(new ApiResponse(true, "已标记为掌握"));
        } else {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "标记失败"));
        }
    }

    @PostMapping("/{studentId}/retry/{wrongQuestionId}")
    public ResponseEntity<ApiResponse> incrementRetryCount(
            @PathVariable Long studentId,
            @PathVariable Long wrongQuestionId) {
        boolean success = wrongQuestionService.incrementRetryCount(studentId, wrongQuestionId);
        if (success) {
            return ResponseEntity.ok(new ApiResponse(true, "重做次数已更新"));
        } else {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "更新失败"));
        }
    }

    @DeleteMapping("/{studentId}/{wrongQuestionId}")
    public ResponseEntity<ApiResponse> deleteWrongQuestion(
            @PathVariable Long studentId,
            @PathVariable Long wrongQuestionId) {
        boolean success = wrongQuestionService.deleteWrongQuestion(studentId, wrongQuestionId);
        if (success) {
            return ResponseEntity.ok(new ApiResponse(true, "错题已删除"));
        } else {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "删除失败"));
        }
    }

    @GetMapping("/content-types")
    public ResponseEntity<List<Map<String, Object>>> getContentTypes() {
        List<Map<String, Object>> types = List.of(
            Map.of("type", "math_exercise", "name", "数学练习", "description", "数学计算题目"),
            Map.of("type", "english_word", "name", "英语单词", "description", "英语词汇学习"),
            Map.of("type", "question", "name", "综合题目", "description", "各学科综合题目")
        );
        return ResponseEntity.ok(types);
    }
}
