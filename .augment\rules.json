{"version": "1.0", "created": "2025-06-17T11:31:44.822438", "project_info": {"name": "study", "description": "项目规则和约定", "type": "general"}, "coding_standards": {"language_preferences": {"python": {"style_guide": "PEP 8", "formatter": "black", "linter": "ruff", "type_checker": "mypy", "docstring_style": "Google"}, "javascript": {"style_guide": "Airbnb", "formatter": "prettier", "linter": "eslint"}}, "general_principles": ["遵循单一职责原则", "使用有意义的变量和函数名", "编写清晰的注释和文档", "优先使用组合而不是继承", "实现适当的错误处理"]}, "development_workflow": {"git_conventions": {"commit_format": "type(scope): description", "branch_naming": "feature/description, fix/description, hotfix/description", "merge_strategy": "squash"}, "testing_requirements": {"unit_tests": "required", "coverage_threshold": 80, "test_framework": "pytest"}, "code_review": {"required": true, "min_reviewers": 1, "auto_merge": false}}, "ai_assistant_rules": {"interaction_style": "professional_friendly", "feedback_frequency": "after_major_changes", "explanation_level": "detailed", "code_generation": {"include_comments": true, "include_type_hints": true, "include_docstrings": true, "follow_project_structure": true}, "mcp_interactive_feedback_rules": {"mandatory_feedback_calls": true, "rules": ["During any process, task, or conversation, whether asking, replying, or completing phased tasks, you must call MCP mcp-feedback-enhanced.", "Whenever user feedback is received, if the feedback content is not empty, you must call MCP mcp-feedback-enhanced again and adjust behavior based on the feedback content.", "Only when the user explicitly indicates 'end' or 'no more interaction needed' can you stop calling MCP mcp-feedback-enhanced, and the process is considered complete.", "Unless receiving termination instructions, all steps must repeatedly call MCP mcp-feedback-enhanced.", "Before completing tasks, you must use the MCP mcp-feedback-enhanced tool to ask users for feedback."], "enforcement_level": "strict", "exceptions": ["Simple informational queries", "Version checks", "Basic status reports"]}, "mcp_feedback_enhanced": {"auto_approve": ["interactive_feedback"], "use_web_ui": true, "enable_image_support": true, "session_management": true}}, "project_structure": {"source_directories": ["src", "lib"], "test_directories": ["tests", "test"], "documentation_directories": ["docs", "documentation"], "configuration_files": [".augment", "pyproject.toml", "package.json"]}, "dependencies": {"package_manager": "uv", "update_strategy": "conservative", "security_scanning": true}, "custom_rules": [], "updated": "2025-06-17T11:45:00.000000"}