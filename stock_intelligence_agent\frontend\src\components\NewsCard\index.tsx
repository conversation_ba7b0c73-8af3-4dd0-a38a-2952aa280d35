/**
 * 新闻卡片组件
 */

import React from 'react';
import { Card, Typography, Space, Tag, Avatar, Button } from 'antd';
import {
  FileTextOutlined,
  EyeOutlined,
  LikeOutlined,
  MessageOutlined,
  ClockCircleOutlined,
  StarOutlined,
} from '@ant-design/icons';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

import { News } from '@/types';

const { Text, Paragraph } = Typography;

interface NewsCardProps {
  news: News;
  onClick?: () => void;
  showActions?: boolean;
  compact?: boolean;
}

const NewsCard: React.FC<NewsCardProps> = ({
  news,
  onClick,
  showActions = true,
  compact = false,
}) => {
  // 获取重要性颜色
  const getImportanceColor = (importance: number) => {
    if (importance >= 5) return 'red';
    if (importance >= 4) return 'orange';
    if (importance >= 3) return 'blue';
    return 'default';
  };

  // 获取情感颜色
  const getSentimentColor = (sentiment?: string) => {
    switch (sentiment) {
      case 'positive':
        return 'green';
      case 'negative':
        return 'red';
      default:
        return 'default';
    }
  };

  // 获取情感文本
  const getSentimentText = (sentiment?: string) => {
    switch (sentiment) {
      case 'positive':
        return '正面';
      case 'negative':
        return '负面';
      case 'neutral':
        return '中性';
      default:
        return '';
    }
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), {
        addSuffix: true,
        locale: zhCN,
      });
    } catch {
      return dateString;
    }
  };

  return (
    <Card
      className={`news-card ${onClick ? 'card-hover' : ''}`}
      onClick={onClick}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
      bodyStyle={{ padding: compact ? 16 : 20 }}
    >
      {/* 头部信息 */}
      <div className="flex-between mb-2">
        <Space size="small">
          <Avatar
            size="small"
            icon={<FileTextOutlined />}
            style={{ backgroundColor: '#1890ff' }}
          />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {news.source}
          </Text>
          {news.author && (
            <>
              <Text type="secondary" style={{ fontSize: 12 }}>
                •
              </Text>
              <Text type="secondary" style={{ fontSize: 12 }}>
                {news.author}
              </Text>
            </>
          )}
        </Space>
        
        <Space size="small">
          {/* 重要性标签 */}
          {news.importance >= 3 && (
            <Tag
              color={getImportanceColor(news.importance)}
              size="small"
              icon={<StarOutlined />}
            >
              重要
            </Tag>
          )}
          
          {/* 情感标签 */}
          {news.sentiment_label && getSentimentText(news.sentiment_label) && (
            <Tag
              color={getSentimentColor(news.sentiment_label)}
              size="small"
            >
              {getSentimentText(news.sentiment_label)}
            </Tag>
          )}
        </Space>
      </div>

      {/* 标题 */}
      <div className="news-title">
        {news.title}
      </div>

      {/* 摘要 */}
      {news.summary && !compact && (
        <Paragraph
          className="news-summary"
          ellipsis={{ rows: 2, expandable: false }}
        >
          {news.summary}
        </Paragraph>
      )}

      {/* 相关股票 */}
      {news.related_stocks && news.related_stocks.length > 0 && (
        <div className="mb-2">
          <Space size={4} wrap>
            <Text type="secondary" style={{ fontSize: 12 }}>
              相关股票:
            </Text>
            {news.related_stocks.slice(0, 3).map(stockCode => (
              <Tag
                key={stockCode}
                size="small"
                style={{ fontSize: 11, margin: '0 2px' }}
              >
                {stockCode}
              </Tag>
            ))}
            {news.related_stocks.length > 3 && (
              <Text type="secondary" style={{ fontSize: 11 }}>
                +{news.related_stocks.length - 3}
              </Text>
            )}
          </Space>
        </div>
      )}

      {/* 标签 */}
      {news.tags && news.tags.length > 0 && !compact && (
        <div className="mb-2">
          <Space size={4} wrap>
            {news.tags.slice(0, 4).map(tag => (
              <Tag key={tag} size="small" style={{ fontSize: 11 }}>
                {tag}
              </Tag>
            ))}
            {news.tags.length > 4 && (
              <Text type="secondary" style={{ fontSize: 11 }}>
                +{news.tags.length - 4}
              </Text>
            )}
          </Space>
        </div>
      )}

      {/* 底部信息 */}
      <div className="news-meta">
        <Space size="middle">
          <Space size={4}>
            <ClockCircleOutlined />
            <Text type="secondary" style={{ fontSize: 12 }}>
              {formatTime(news.published_at)}
            </Text>
          </Space>
          
          {showActions && (
            <>
              <Space size={4}>
                <EyeOutlined />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  {news.view_count || 0}
                </Text>
              </Space>
              
              <Space size={4}>
                <LikeOutlined />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  {news.like_count || 0}
                </Text>
              </Space>
              
              <Space size={4}>
                <MessageOutlined />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  {news.comment_count || 0}
                </Text>
              </Space>
            </>
          )}
        </Space>

        {/* 操作按钮 */}
        {showActions && !compact && (
          <Space size="small">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                // 处理查看详情
              }}
            >
              详情
            </Button>
            
            <Button
              type="text"
              size="small"
              icon={<LikeOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                // 处理点赞
              }}
            />
          </Space>
        )}
      </div>
    </Card>
  );
};

export default NewsCard;
