/**
 * WebSocket 服务
 * 管理实时数据连接和消息处理
 */

import { io, Socket } from 'socket.io-client';
import { message } from 'antd';
import { 
  WebSocketMessage, 
  StockDataMessage, 
  NewsDataMessage, 
  AlertMessage 
} from '@/types';

type MessageHandler = (message: WebSocketMessage) => void;
type StockDataHandler = (message: StockDataMessage) => void;
type NewsDataHandler = (message: NewsDataMessage) => void;
type AlertHandler = (message: AlertMessage) => void;

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;
  private isConnecting = false;
  
  // 事件处理器
  private messageHandlers: MessageHandler[] = [];
  private stockDataHandlers: StockDataHandler[] = [];
  private newsDataHandlers: NewsDataHandler[] = [];
  private alertHandlers: AlertHandler[] = [];
  
  // 订阅状态
  private subscribedStocks = new Set<string>();
  private subscribedNewsCategories = new Set<string>();

  constructor() {
    this.connect();
  }

  /**
   * 建立 WebSocket 连接
   */
  connect(): void {
    if (this.isConnecting || this.socket?.connected) {
      return;
    }

    this.isConnecting = true;
    const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:8000';
    const token = localStorage.getItem('access_token');
    const userId = this.getCurrentUserId();

    this.socket = io(wsUrl, {
      transports: ['websocket'],
      query: {
        user_id: userId,
        client_type: 'web',
        version: '1.0'
      },
      auth: {
        token: token
      },
      reconnection: true,
      reconnectionAttempts: this.maxReconnectAttempts,
      reconnectionDelay: this.reconnectInterval,
    });

    this.setupEventHandlers();
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    // 连接成功
    this.socket.on('connect', () => {
      console.log('WebSocket 连接成功');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      
      // 重新订阅之前的股票和新闻
      this.resubscribe();
    });

    // 连接断开
    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket 连接断开:', reason);
      this.isConnecting = false;
      
      if (reason === 'io server disconnect') {
        // 服务器主动断开，需要重新连接
        this.reconnect();
      }
    });

    // 连接错误
    this.socket.on('connect_error', (error) => {
      console.error('WebSocket 连接错误:', error);
      this.isConnecting = false;
      this.handleConnectionError();
    });

    // 重连失败
    this.socket.on('reconnect_failed', () => {
      console.error('WebSocket 重连失败');
      message.error('连接服务器失败，请检查网络连接');
    });

    // 消息处理
    this.socket.on('message', (data: WebSocketMessage) => {
      this.handleMessage(data);
    });

    // 股票数据
    this.socket.on('stock_data', (data: StockDataMessage) => {
      this.handleStockData(data);
    });

    // 新闻数据
    this.socket.on('news_data', (data: NewsDataMessage) => {
      this.handleNewsData(data);
    });

    // 预警消息
    this.socket.on('alert', (data: AlertMessage) => {
      this.handleAlert(data);
    });

    // 系统通知
    this.socket.on('system_notification', (data: any) => {
      message.info(data.message || '系统通知');
    });

    // 错误消息
    this.socket.on('error', (data: any) => {
      console.error('WebSocket 错误:', data);
      message.error(data.message || '连接错误');
    });
  }

  /**
   * 处理连接错误
   */
  private handleConnectionError(): void {
    this.reconnectAttempts++;
    
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      message.error('无法连接到服务器，请刷新页面重试');
      return;
    }

    setTimeout(() => {
      if (!this.socket?.connected) {
        this.connect();
      }
    }, this.reconnectInterval * this.reconnectAttempts);
  }

  /**
   * 重新连接
   */
  private reconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    
    setTimeout(() => {
      this.connect();
    }, 1000);
  }

  /**
   * 重新订阅
   */
  private resubscribe(): void {
    // 重新订阅股票
    this.subscribedStocks.forEach(stockCode => {
      this.subscribeStock(stockCode);
    });

    // 重新订阅新闻
    this.subscribedNewsCategories.forEach(category => {
      this.subscribeNews(category);
    });
  }

  /**
   * 获取当前用户ID
   */
  private getCurrentUserId(): string | null {
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        return user.id;
      }
    } catch (error) {
      console.error('获取用户ID失败:', error);
    }
    return null;
  }

  /**
   * 发送消息
   */
  send(message: any): void {
    if (this.socket?.connected) {
      this.socket.emit('message', message);
    } else {
      console.warn('WebSocket 未连接，无法发送消息');
    }
  }

  /**
   * 订阅股票实时数据
   */
  subscribeStock(stockCode: string): void {
    this.subscribedStocks.add(stockCode);
    this.send({
      type: 'subscribe_stock',
      stock_code: stockCode,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 取消订阅股票
   */
  unsubscribeStock(stockCode: string): void {
    this.subscribedStocks.delete(stockCode);
    this.send({
      type: 'unsubscribe_stock',
      stock_code: stockCode,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 订阅新闻推送
   */
  subscribeNews(category: string = 'all'): void {
    this.subscribedNewsCategories.add(category);
    this.send({
      type: 'subscribe_news',
      category: category,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 取消订阅新闻
   */
  unsubscribeNews(category: string = 'all'): void {
    this.subscribedNewsCategories.delete(category);
    this.send({
      type: 'unsubscribe_news',
      category: category,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 获取股票数据
   */
  getStockData(stockCode: string): void {
    this.send({
      type: 'get_stock_data',
      stock_code: stockCode,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 获取市场概览
   */
  getMarketOverview(): void {
    this.send({
      type: 'get_market_overview',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 搜索股票
   */
  searchStocks(query: string): void {
    this.send({
      type: 'search_stocks',
      query: query,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 心跳检测
   */
  ping(): void {
    this.send({
      type: 'ping',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 处理通用消息
   */
  private handleMessage(message: WebSocketMessage): void {
    this.messageHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('消息处理器错误:', error);
      }
    });
  }

  /**
   * 处理股票数据
   */
  private handleStockData(message: StockDataMessage): void {
    this.stockDataHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('股票数据处理器错误:', error);
      }
    });
  }

  /**
   * 处理新闻数据
   */
  private handleNewsData(message: NewsDataMessage): void {
    this.newsDataHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('新闻数据处理器错误:', error);
      }
    });
  }

  /**
   * 处理预警消息
   */
  private handleAlert(message: AlertMessage): void {
    // 显示预警通知
    message.warning({
      content: `预警触发: ${message.data.alert_name} - ${message.data.message}`,
      duration: 10,
    });

    this.alertHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('预警处理器错误:', error);
      }
    });
  }

  /**
   * 添加消息处理器
   */
  onMessage(handler: MessageHandler): () => void {
    this.messageHandlers.push(handler);
    return () => {
      const index = this.messageHandlers.indexOf(handler);
      if (index > -1) {
        this.messageHandlers.splice(index, 1);
      }
    };
  }

  /**
   * 添加股票数据处理器
   */
  onStockData(handler: StockDataHandler): () => void {
    this.stockDataHandlers.push(handler);
    return () => {
      const index = this.stockDataHandlers.indexOf(handler);
      if (index > -1) {
        this.stockDataHandlers.splice(index, 1);
      }
    };
  }

  /**
   * 添加新闻数据处理器
   */
  onNewsData(handler: NewsDataHandler): () => void {
    this.newsDataHandlers.push(handler);
    return () => {
      const index = this.newsDataHandlers.indexOf(handler);
      if (index > -1) {
        this.newsDataHandlers.splice(index, 1);
      }
    };
  }

  /**
   * 添加预警处理器
   */
  onAlert(handler: AlertHandler): () => void {
    this.alertHandlers.push(handler);
    return () => {
      const index = this.alertHandlers.indexOf(handler);
      if (index > -1) {
        this.alertHandlers.splice(index, 1);
      }
    };
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.subscribedStocks.clear();
    this.subscribedNewsCategories.clear();
  }

  /**
   * 获取连接状态
   */
  get isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * 获取订阅的股票列表
   */
  get subscribedStockList(): string[] {
    return Array.from(this.subscribedStocks);
  }
}

// 导出单例实例
export const wsService = new WebSocketService();
export default wsService;
