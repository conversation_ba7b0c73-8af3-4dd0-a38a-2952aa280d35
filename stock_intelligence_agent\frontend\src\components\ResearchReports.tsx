import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  Building2, 
  User, 
  Target, 
  BarChart3,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface ResearchReport {
  institution: string;
  analyst: string;
  rating: string;
  target_price: number;
  publish_date: string;
  report_title: string;
  key_points: string[];
  data_source: string;
}

interface RatingSummary {
  total_reports: number;
  rating_distribution: Record<string, number>;
  avg_target_price: number;
  consensus_rating: string;
}

interface ResearchData {
  stock_code: string;
  research_reports: ResearchReport[];
  rating_summary: RatingSummary;
  analysis_time: string;
  data_source: string;
  source_details: {
    successful_sources: string[];
    failed_sources: string[];
    total_sources_tried: number;
  };
}

interface DataSource {
  name: string;
  priority: number;
  reliability: number;
  failure_count: number;
  last_success: string | null;
  update_frequency: string;
}

const ResearchReports: React.FC = () => {
  const [stockCode, setStockCode] = useState('300750');
  const [researchData, setResearchData] = useState<ResearchData | null>(null);
  const [dataSources, setDataSources] = useState<Record<string, DataSource>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [maxSources, setMaxSources] = useState(3);

  // 获取研报数据
  const fetchResearchData = async () => {
    if (!stockCode || stockCode.length !== 6) {
      setError('请输入6位股票代码');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/research/reports/${stockCode}?max_sources=${maxSources}`);
      const result = await response.json();

      if (result.success) {
        setResearchData(result.data);
      } else {
        setError(result.message || '获取研报数据失败');
      }
    } catch (err) {
      setError('网络请求失败，请稍后重试');
      console.error('获取研报数据失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 获取数据源状态
  const fetchDataSources = async () => {
    try {
      const response = await fetch('/api/research/sources/status');
      const result = await response.json();

      if (result.success) {
        setDataSources(result.data.sources);
      }
    } catch (err) {
      console.error('获取数据源状态失败:', err);
    }
  };

  // 刷新数据源
  const refreshDataSources = async () => {
    try {
      await fetch('/api/research/sources/refresh', { method: 'POST' });
      await fetchDataSources();
    } catch (err) {
      console.error('刷新数据源失败:', err);
    }
  };

  useEffect(() => {
    fetchDataSources();
  }, []);

  // 评级颜色映射
  const getRatingColor = (rating: string) => {
    const ratingMap: Record<string, string> = {
      '买入': 'bg-green-100 text-green-800',
      '强烈推荐': 'bg-green-100 text-green-800',
      '强推': 'bg-green-100 text-green-800',
      '增持': 'bg-blue-100 text-blue-800',
      '中性': 'bg-gray-100 text-gray-800',
      '持有': 'bg-gray-100 text-gray-800',
      '减持': 'bg-orange-100 text-orange-800',
      '卖出': 'bg-red-100 text-red-800'
    };
    return ratingMap[rating] || 'bg-gray-100 text-gray-800';
  };

  // 数据源状态图标
  const getSourceStatusIcon = (source: DataSource) => {
    if (source.failure_count === 0 && source.last_success) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    } else if (source.failure_count < 3) {
      return <Clock className="h-4 w-4 text-yellow-500" />;
    } else {
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* 搜索区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            研报分析
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <label className="text-sm font-medium mb-2 block">股票代码</label>
              <Input
                placeholder="请输入6位股票代码，如：300750"
                value={stockCode}
                onChange={(e) => setStockCode(e.target.value)}
                maxLength={6}
              />
            </div>
            <div className="w-32">
              <label className="text-sm font-medium mb-2 block">数据源数量</label>
              <Select value={maxSources.toString()} onValueChange={(value) => setMaxSources(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1个</SelectItem>
                  <SelectItem value="2">2个</SelectItem>
                  <SelectItem value="3">3个</SelectItem>
                  <SelectItem value="4">4个</SelectItem>
                  <SelectItem value="5">5个</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button onClick={fetchResearchData} disabled={loading}>
              {loading ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : null}
              {loading ? '分析中...' : '开始分析'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 研报数据展示 */}
      {researchData && (
        <Tabs defaultValue="reports" className="space-y-4">
          <TabsList>
            <TabsTrigger value="reports">研报列表</TabsTrigger>
            <TabsTrigger value="summary">评级汇总</TabsTrigger>
            <TabsTrigger value="sources">数据源状态</TabsTrigger>
          </TabsList>

          {/* 研报列表 */}
          <TabsContent value="reports" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">
                {researchData.stock_code} 研报列表 ({researchData.research_reports.length}条)
              </h3>
              <Badge variant="outline">
                数据来源: {researchData.data_source}
              </Badge>
            </div>

            <div className="grid gap-4">
              {researchData.research_reports.map((report, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex-1">
                        <h4 className="font-medium text-sm mb-2 line-clamp-2">
                          {report.report_title}
                        </h4>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Building2 className="h-3 w-3" />
                            {report.institution}
                          </div>
                          <div className="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            {report.analyst}
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {report.publish_date}
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end gap-2">
                        <Badge className={getRatingColor(report.rating)}>
                          {report.rating}
                        </Badge>
                        {report.target_price > 0 && (
                          <div className="flex items-center gap-1 text-sm text-gray-600">
                            <Target className="h-3 w-3" />
                            ¥{report.target_price}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {report.key_points && report.key_points.length > 0 && (
                      <div className="mt-3 pt-3 border-t">
                        <div className="text-xs text-gray-500 mb-1">核心观点:</div>
                        <div className="flex flex-wrap gap-1">
                          {report.key_points.map((point, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              {point}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* 评级汇总 */}
          <TabsContent value="summary" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold">{researchData.rating_summary.total_reports}</div>
                  <div className="text-sm text-gray-600">研报总数</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-green-600">
                    {researchData.rating_summary.consensus_rating}
                  </div>
                  <div className="text-sm text-gray-600">一致性评级</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold">
                    {researchData.rating_summary.avg_target_price > 0 
                      ? `¥${researchData.rating_summary.avg_target_price}` 
                      : '--'}
                  </div>
                  <div className="text-sm text-gray-600">平均目标价</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold">{researchData.source_details.successful_sources.length}</div>
                  <div className="text-sm text-gray-600">成功数据源</div>
                </CardContent>
              </Card>
            </div>

            {/* 评级分布 */}
            <Card>
              <CardHeader>
                <CardTitle>评级分布</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(researchData.rating_summary.rating_distribution).map(([rating, count]) => {
                    const percentage = (count / researchData.rating_summary.total_reports) * 100;
                    return (
                      <div key={rating} className="flex items-center gap-3">
                        <div className="w-16 text-sm">{rating}</div>
                        <div className="flex-1">
                          <Progress value={percentage} className="h-2" />
                        </div>
                        <div className="w-12 text-sm text-right">{count}条</div>
                        <div className="w-12 text-xs text-gray-500 text-right">
                          {percentage.toFixed(0)}%
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 数据源状态 */}
          <TabsContent value="sources" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">数据源状态</h3>
              <Button variant="outline" size="sm" onClick={refreshDataSources}>
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新状态
              </Button>
            </div>

            <div className="grid gap-4">
              {Object.entries(dataSources).map(([sourceId, source]) => (
                <Card key={sourceId}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getSourceStatusIcon(source)}
                        <div>
                          <div className="font-medium">{source.name}</div>
                          <div className="text-sm text-gray-600">
                            优先级: {source.priority} | 可靠性: {(source.reliability * 100).toFixed(0)}%
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm">
                          失败次数: <span className={source.failure_count > 2 ? 'text-red-600' : 'text-gray-600'}>
                            {source.failure_count}
                          </span>
                        </div>
                        <div className="text-xs text-gray-500">
                          更新频率: {source.update_frequency}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

export default ResearchReports;
