"""
新闻爬虫
负责从各种新闻源采集新闻数据
"""

import asyncio
import aiohttp
import logging
import json
import re
import feedparser
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import xml.etree.ElementTree as ET

from app.news_collector.news_sources import NewsSource, NewsSourceType, news_source_manager
from app.core.config import settings

logger = logging.getLogger(__name__)


class NewsCrawler:
    """新闻爬虫"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limiters = {}  # 速率限制器
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(
            limit=100,
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True
        )
        
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }
        )
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def crawl_all_sources(self, max_articles_per_source: int = 50) -> List[Dict[str, Any]]:
        """
        爬取所有活跃新闻源
        
        Args:
            max_articles_per_source: 每个源最大文章数
            
        Returns:
            新闻文章列表
        """
        all_articles = []
        active_sources = news_source_manager.get_active_sources()
        
        # 按优先级排序
        active_sources.sort(key=lambda x: x.priority, reverse=True)
        
        tasks = []
        for source in active_sources:
            task = asyncio.create_task(
                self.crawl_source(source, max_articles_per_source)
            )
            tasks.append(task)
        
        # 并发执行爬取任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"爬取新闻源失败 {active_sources[i].name}: {result}")
            elif isinstance(result, list):
                all_articles.extend(result)
        
        # 去重和排序
        all_articles = self._deduplicate_articles(all_articles)
        all_articles.sort(key=lambda x: x.get('published_at', datetime.min), reverse=True)
        
        logger.info(f"总共爬取到 {len(all_articles)} 篇文章")
        return all_articles
    
    async def crawl_source(self, source: NewsSource, max_articles: int = 50) -> List[Dict[str, Any]]:
        """
        爬取单个新闻源
        
        Args:
            source: 新闻源配置
            max_articles: 最大文章数
            
        Returns:
            新闻文章列表
        """
        try:
            # 检查速率限制
            if not await self._check_rate_limit(source):
                logger.warning(f"新闻源 {source.name} 达到速率限制")
                return []
            
            logger.info(f"开始爬取新闻源: {source.name}")
            
            if source.source_type == NewsSourceType.RSS:
                articles = await self._crawl_rss(source, max_articles)
            elif source.source_type == NewsSourceType.API:
                articles = await self._crawl_api(source, max_articles)
            elif source.source_type == NewsSourceType.WEB_SCRAPING:
                articles = await self._crawl_web(source, max_articles)
            else:
                logger.warning(f"不支持的新闻源类型: {source.source_type}")
                return []
            
            # 添加源信息
            for article in articles:
                article['source_name'] = source.name
                article['source_category'] = source.category.value
                article['source_priority'] = source.priority
                article['crawled_at'] = datetime.utcnow()
            
            logger.info(f"从 {source.name} 爬取到 {len(articles)} 篇文章")
            return articles
            
        except Exception as e:
            logger.error(f"爬取新闻源 {source.name} 失败: {e}")
            return []
    
    async def _crawl_rss(self, source: NewsSource, max_articles: int) -> List[Dict[str, Any]]:
        """爬取RSS源"""
        try:
            headers = source.headers or {}
            
            async with self.session.get(source.url, headers=headers) as response:
                if response.status != 200:
                    logger.warning(f"RSS请求失败 {source.name}: {response.status}")
                    return []
                
                content = await response.text(encoding=source.encoding)
                
                # 解析RSS
                feed = feedparser.parse(content)
                articles = []
                
                for entry in feed.entries[:max_articles]:
                    article = {
                        'title': self._clean_text(entry.get('title', '')),
                        'url': entry.get('link', ''),
                        'summary': self._clean_text(entry.get('summary', '')),
                        'content': self._clean_text(entry.get('content', [{}])[0].get('value', '') if entry.get('content') else ''),
                        'author': entry.get('author', ''),
                        'published_at': self._parse_datetime(entry.get('published', '')),
                        'tags': [tag.get('term', '') for tag in entry.get('tags', [])],
                    }
                    
                    # 如果没有内容，尝试获取全文
                    if not article['content'] and article['url']:
                        article['content'] = await self._fetch_full_content(article['url'], source)
                    
                    articles.append(article)
                
                return articles
                
        except Exception as e:
            logger.error(f"RSS爬取失败 {source.name}: {e}")
            return []
    
    async def _crawl_api(self, source: NewsSource, max_articles: int) -> List[Dict[str, Any]]:
        """爬取API源"""
        try:
            headers = source.headers or {}
            params = source.params or {}
            
            # 限制返回数量
            if 'limit' in params:
                params['limit'] = min(params['limit'], max_articles)
            elif 'page_size' in params:
                params['page_size'] = min(params['page_size'], max_articles)
            elif 'num' in params:
                params['num'] = min(params['num'], max_articles)
            
            async with self.session.get(source.url, headers=headers, params=params) as response:
                if response.status != 200:
                    logger.warning(f"API请求失败 {source.name}: {response.status}")
                    return []
                
                content = await response.text()
                
                # 处理JSONP回调
                if content.startswith('jsonpCallback(') or content.startswith('jQuery'):
                    content = re.sub(r'^[^(]*\(', '', content)
                    content = re.sub(r'\);?$', '', content)
                
                try:
                    data = json.loads(content)
                except json.JSONDecodeError:
                    logger.error(f"API响应不是有效JSON {source.name}")
                    return []
                
                articles = []
                
                # 根据不同API格式解析数据
                if source.name == "eastmoney":
                    items = data.get('data', {}).get('list', [])
                    for item in items[:max_articles]:
                        article = {
                            'title': self._clean_text(item.get('title', '')),
                            'url': item.get('url', ''),
                            'summary': self._clean_text(item.get('summary', '')),
                            'content': '',
                            'author': item.get('source', ''),
                            'published_at': self._parse_datetime(item.get('date', '')),
                            'tags': [],
                        }
                        articles.append(article)
                
                elif source.name == "wallstreetcn":
                    items = data.get('data', {}).get('items', [])
                    for item in items[:max_articles]:
                        article = {
                            'title': self._clean_text(item.get('title', '')),
                            'url': f"https://wallstreetcn.com/articles/{item.get('id', '')}",
                            'summary': self._clean_text(item.get('summary', '')),
                            'content': self._clean_text(item.get('content', '')),
                            'author': item.get('author', {}).get('display_name', ''),
                            'published_at': self._parse_datetime(item.get('display_time', '')),
                            'tags': [tag.get('name', '') for tag in item.get('themes', [])],
                        }
                        articles.append(article)
                
                elif source.name in ["sse", "szse"]:
                    # 交易所公告格式
                    items = data.get('result', []) or data.get('data', [])
                    for item in items[:max_articles]:
                        article = {
                            'title': self._clean_text(item.get('TITLE', '') or item.get('title', '')),
                            'url': item.get('URL', '') or item.get('url', ''),
                            'summary': '',
                            'content': '',
                            'author': item.get('COMPANY_ABBR', '') or item.get('company', ''),
                            'published_at': self._parse_datetime(item.get('DECLAREDATE', '') or item.get('date', '')),
                            'tags': [item.get('SECURITY_CODE', '') or item.get('code', '')],
                        }
                        articles.append(article)
                
                return articles
                
        except Exception as e:
            logger.error(f"API爬取失败 {source.name}: {e}")
            return []
    
    async def _crawl_web(self, source: NewsSource, max_articles: int) -> List[Dict[str, Any]]:
        """爬取网页源"""
        try:
            headers = source.headers or {}
            
            async with self.session.get(source.url, headers=headers) as response:
                if response.status != 200:
                    logger.warning(f"网页请求失败 {source.name}: {response.status}")
                    return []
                
                content = await response.text(encoding=source.encoding)
                soup = BeautifulSoup(content, 'html.parser')
                
                articles = []
                selectors = source.selectors or {}
                
                # 获取文章列表
                list_selector = selectors.get('list', '')
                if list_selector:
                    items = soup.select(list_selector)[:max_articles]
                    
                    for item in items:
                        title_elem = item.select_one(selectors.get('title', ''))
                        link_elem = item.select_one(selectors.get('link', ''))
                        time_elem = item.select_one(selectors.get('time', ''))
                        summary_elem = item.select_one(selectors.get('summary', ''))
                        
                        title = title_elem.get_text(strip=True) if title_elem else ''
                        link = link_elem.get('href', '') if link_elem else ''
                        time_text = time_elem.get_text(strip=True) if time_elem else ''
                        summary = summary_elem.get_text(strip=True) if summary_elem else ''
                        
                        # 处理相对链接
                        if link and not link.startswith('http'):
                            link = urljoin(source.url, link)
                        
                        article = {
                            'title': self._clean_text(title),
                            'url': link,
                            'summary': self._clean_text(summary),
                            'content': '',
                            'author': '',
                            'published_at': self._parse_datetime(time_text),
                            'tags': [],
                        }
                        
                        # 获取全文内容
                        if link:
                            article['content'] = await self._fetch_full_content(link, source)
                        
                        articles.append(article)
                
                else:
                    # 单页面内容提取
                    title_elem = soup.select_one(selectors.get('title', 'h1'))
                    content_elem = soup.select_one(selectors.get('content', '.content'))
                    time_elem = soup.select_one(selectors.get('time', '.time'))
                    source_elem = soup.select_one(selectors.get('source', '.source'))
                    
                    if title_elem:
                        article = {
                            'title': self._clean_text(title_elem.get_text(strip=True)),
                            'url': source.url,
                            'summary': '',
                            'content': self._clean_text(content_elem.get_text(strip=True)) if content_elem else '',
                            'author': source_elem.get_text(strip=True) if source_elem else '',
                            'published_at': self._parse_datetime(time_elem.get_text(strip=True)) if time_elem else datetime.utcnow(),
                            'tags': [],
                        }
                        articles.append(article)
                
                return articles
                
        except Exception as e:
            logger.error(f"网页爬取失败 {source.name}: {e}")
            return []
    
    async def _fetch_full_content(self, url: str, source: NewsSource) -> str:
        """获取文章全文内容"""
        try:
            headers = source.headers or {}
            
            async with self.session.get(url, headers=headers) as response:
                if response.status != 200:
                    return ''
                
                content = await response.text(encoding=source.encoding)
                soup = BeautifulSoup(content, 'html.parser')
                
                # 移除脚本和样式
                for script in soup(["script", "style"]):
                    script.decompose()
                
                # 尝试多种内容选择器
                content_selectors = [
                    '.article-content', '.content', '.post-content',
                    '.news-content', '.detail-content', '.main-content',
                    'article', '.article', '#content'
                ]
                
                for selector in content_selectors:
                    content_elem = soup.select_one(selector)
                    if content_elem:
                        return self._clean_text(content_elem.get_text())
                
                # 如果没有找到特定选择器，返回body内容
                body = soup.find('body')
                if body:
                    return self._clean_text(body.get_text())
                
                return ''
                
        except Exception as e:
            logger.warning(f"获取全文内容失败 {url}: {e}")
            return ''
    
    def _clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ''
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 移除特殊字符
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)
        
        return text
    
    def _parse_datetime(self, date_str: str) -> datetime:
        """解析日期时间字符串"""
        if not date_str:
            return datetime.utcnow()
        
        # 常见日期格式
        date_formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%Y-%m-%d',
            '%Y/%m/%d %H:%M:%S',
            '%Y/%m/%d %H:%M',
            '%Y/%m/%d',
            '%m-%d %H:%M',
            '%m/%d %H:%M',
        ]
        
        # 清理日期字符串
        date_str = re.sub(r'[^\d\-/:\s]', '', date_str).strip()
        
        for fmt in date_formats:
            try:
                dt = datetime.strptime(date_str, fmt)
                # 如果没有年份，使用当前年份
                if dt.year == 1900:
                    dt = dt.replace(year=datetime.now().year)
                return dt
            except ValueError:
                continue
        
        # 如果都解析失败，返回当前时间
        logger.warning(f"无法解析日期: {date_str}")
        return datetime.utcnow()
    
    def _deduplicate_articles(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重文章"""
        seen_titles = set()
        seen_urls = set()
        unique_articles = []
        
        for article in articles:
            title = article.get('title', '').strip()
            url = article.get('url', '').strip()
            
            # 基于标题和URL去重
            title_key = re.sub(r'\s+', ' ', title.lower())
            
            if title_key and title_key not in seen_titles:
                if not url or url not in seen_urls:
                    seen_titles.add(title_key)
                    if url:
                        seen_urls.add(url)
                    unique_articles.append(article)
        
        return unique_articles
    
    async def _check_rate_limit(self, source: NewsSource) -> bool:
        """检查速率限制"""
        now = datetime.utcnow()
        source_name = source.name
        
        if source_name not in self.rate_limiters:
            self.rate_limiters[source_name] = {
                'last_request': now - timedelta(minutes=1),
                'request_count': 0
            }
        
        limiter = self.rate_limiters[source_name]
        
        # 重置计数器（每分钟）
        if (now - limiter['last_request']).total_seconds() >= 60:
            limiter['request_count'] = 0
            limiter['last_request'] = now
        
        # 检查是否超过限制
        if limiter['request_count'] >= source.rate_limit:
            return False
        
        limiter['request_count'] += 1
        return True


# 便捷函数
async def crawl_news(max_articles_per_source: int = 50) -> List[Dict[str, Any]]:
    """爬取新闻的便捷函数"""
    async with NewsCrawler() as crawler:
        return await crawler.crawl_all_sources(max_articles_per_source)
