package com.example.elementarylearningcompanion.repository;

import com.example.elementarylearningcompanion.model.EnglishWord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnglishWordRepository extends JpaRepository<EnglishWord, Long> {
    
    List<EnglishWord> findByTextbookVersionIdAndGrade(Integer textbookVersionId, Integer grade);
    
    List<EnglishWord> findByTextbookVersionIdAndGradeAndUnit(Integer textbookVersionId, Integer grade, String unit);
    
    List<EnglishWord> findByTextbookVersionIdAndGradeAndDifficultyLevel(
        Integer textbookVersionId, Integer grade, Integer difficultyLevel);
    
    @Query("SELECT DISTINCT ew.unit FROM EnglishWord ew WHERE ew.textbookVersionId = :textbookVersionId AND ew.grade = :grade ORDER BY ew.unit")
    List<String> findDistinctUnits(@Param("textbookVersionId") Integer textbookVersionId,
                                  @Param("grade") Integer grade);
    
    @Query("SELECT ew FROM EnglishWord ew WHERE ew.textbookVersionId = :textbookVersionId " +
           "AND ew.grade = :grade ORDER BY RAND() LIMIT :limit")
    List<EnglishWord> findRandomWords(@Param("textbookVersionId") Integer textbookVersionId,
                                     @Param("grade") Integer grade,
                                     @Param("limit") Integer limit);
    
    List<EnglishWord> findByWordContainingIgnoreCase(String word);
    
    @Query("SELECT ew FROM EnglishWord ew WHERE ew.textbookVersionId = :textbookVersionId " +
           "AND ew.grade = :grade AND ew.difficultyLevel = :difficultyLevel " +
           "ORDER BY RAND() LIMIT :limit")
    List<EnglishWord> findRandomWordsByDifficulty(@Param("textbookVersionId") Integer textbookVersionId,
                                                 @Param("grade") Integer grade,
                                                 @Param("difficultyLevel") Integer difficultyLevel,
                                                 @Param("limit") Integer limit);
}
