# Elementary Learning Companion - Technical Documentation
# 小学学习伴侣 - 技术文档

## 🎯 项目概述

小学学习伴侣是一个全面的教育应用，旨在为小学生提供有趣、互动的学习体验。应用涵盖语文、数学、英语三大核心学科，提供课文阅读、数学练习、英语单词学习等功能。

## 📁 项目结构

```
ElementaryLearningApp/
├── server/                 # Spring Boot 后端服务
│   ├── src/main/java/     # Java 源代码
│   │   └── com/example/elementarylearningcompanion/
│   │       ├── controller/    # REST API 控制器
│   │       ├── service/       # 业务逻辑服务
│   │       ├── model/         # 数据模型
│   │       ├── repository/    # 数据访问层
│   │       └── dto/           # 数据传输对象
│   ├── src/main/resources/    # 配置文件
│   ├── database_schema.sql    # 数据库结构
│   ├── sample_data.sql        # 示例数据
│   └── build.gradle           # 构建配置
├── client/                    # Android 客户端应用
│   ├── app/src/main/         # Android 源代码
│   │   ├── java/com/example/elementarylearningcompanion/
│   │   │   ├── dto/              # 数据传输对象
│   │   │   ├── network/          # 网络层
│   │   │   ├── viewmodel/        # MVVM 视图模型
│   │   │   ├── *Activity.kt      # 各功能页面
│   │   │   └── MainActivity.kt   # 主入口
│   │   └── res/                  # 资源文件
│   ├── app/build.gradle.kts      # 应用构建配置
│   └── build.gradle.kts          # 项目构建配置
├── build.sh                      # Linux/Mac 构建脚本
├── build.bat                     # Windows 构建脚本
├── README.md                     # 项目需求文档
└── TECHNICAL_README.md           # 技术文档
```

## 🛠️ 技术栈

### 后端 (Backend)
- **框架**: Spring Boot 3.x
- **数据库**: MySQL 8.0
- **ORM**: Spring Data JPA
- **构建工具**: Gradle
- **API**: RESTful API
- **Java版本**: 17+

### 前端 (Frontend)
- **平台**: Android (API 24+)
- **语言**: Kotlin + Java
- **UI框架**: Jetpack Compose
- **网络**: Retrofit2
- **架构**: MVVM + LiveData
- **依赖注入**: 手动依赖管理

## ✨ 已实现功能

### 🎓 学习模块
- **语文学习**
  - [x] 课文阅读界面
  - [x] 生字展示
  - [x] 教材版本选择
  
- **数学练习**
  - [x] 口算训练（加减乘除）
  - [x] 动态题目生成
  - [x] 难度分级（1-5级）
  - [x] 实时答题反馈
  
- **英语学习**
  - [x] 单词卡片学习
  - [x] 拼写练习
  - [x] 听力模式
  - [x] 词汇测验

### 📊 学习管理
- [x] 错题本功能
- [x] 学习统计
- [x] 进度跟踪
- [x] 个人中心

### 🧪 测试系统
- [x] 通用测试框架
- [x] 单选题/多选题/判断题/填空题
- [x] 自动批改
- [x] 成绩统计

### 👤 用户系统
- [x] 用户注册登录
- [x] 学生档案管理
- [x] 家长账户
- [x] 个人设置

## 🏗️ 系统架构

### 后端架构
```
Controller Layer (REST API)
    ↓
Service Layer (Business Logic)
    ↓
Repository Layer (Data Access)
    ↓
Database (MySQL)
```

### 前端架构
```
Activity/Compose UI
    ↓
ViewModel (MVVM)
    ↓
Repository/ApiService
    ↓
Retrofit (HTTP Client)
    ↓
Backend API
```

## 🗄️ 数据库设计

### 核心表结构
- `parents` - 家长信息
- `students` - 学生信息
- `textbook_versions` - 教材版本
- `lessons` - 课文内容
- `characters` - 生字信息
- `math_exercises` - 数学练习题
- `english_words` - 英语单词
- `questions` - 通用题目
- `test_papers` - 测试试卷
- `learning_records` - 学习记录
- `wrong_questions` - 错题记录

## 🚀 快速开始

### 环境要求
- **Java**: 17 或更高版本
- **MySQL**: 8.0 或更高版本
- **Android Studio**: 最新版本
- **Gradle**: 7.0 或更高版本

### 一键构建
```bash
# Linux/Mac
chmod +x build.sh
./build.sh

# Windows
build.bat
```

### 手动部署

#### 后端部署
1. **创建数据库**
   ```sql
   CREATE DATABASE elementary_learning CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **配置数据库连接**
   编辑 `server/src/main/resources/application.properties`
   ```properties
   spring.datasource.url=***********************************************
   spring.datasource.username=root
   spring.datasource.password=your_password
   ```

3. **导入数据库结构**
   ```bash
   mysql -u root -p elementary_learning < server/database_schema.sql
   ```

4. **导入示例数据**
   ```bash
   mysql -u root -p elementary_learning < server/sample_data.sql
   ```

5. **启动服务器**
   ```bash
   cd server
   ./gradlew bootRun
   ```

#### 客户端部署
1. 打开 Android Studio
2. 导入 `client` 目录
3. 配置服务器地址（在 `RetrofitClient.java` 中修改 `BASE_URL`）
4. 构建并运行应用

## 🔌 API 文档

### 认证接口
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录

### 内容管理
- `GET /api/content/textbooks` - 获取教材列表
- `GET /api/content/lessons` - 获取课文列表
- `GET /api/content/lessons/{id}` - 获取课文详情
- `GET /api/content/lessons/{lessonId}/characters` - 获取生字

### 数学模块
- `GET /api/math/exercises/generate` - 生成数学练习
- `GET /api/math/practice-types` - 获取练习类型
- `POST /api/math/submit-answer` - 提交答案
- `GET /api/math/wrong-questions/{studentId}` - 获取错题

### 英语模块
- `GET /api/english/words` - 获取单词列表
- `GET /api/english/words/random` - 获取随机单词
- `GET /api/english/spelling-test` - 获取拼写测试
- `GET /api/english/learning-modes` - 获取学习模式
- `POST /api/english/record-learning` - 记录学习

### 测试系统
- `GET /api/test/questions` - 获取题目
- `GET /api/test/question-types` - 获取题目类型
- `GET /api/test/papers` - 获取试卷
- `POST /api/test/submit-answer` - 提交答案
- `GET /api/test/result/{studentId}/{testPaperId}` - 获取测试结果

### 错题本
- `GET /api/wrong-questions/{studentId}` - 获取错题列表
- `GET /api/wrong-questions/{studentId}/statistics` - 获取错题统计
- `GET /api/wrong-questions/{studentId}/detailed` - 获取详细错题
- `POST /api/wrong-questions/{studentId}/mark-mastered/{wrongQuestionId}` - 标记掌握
- `DELETE /api/wrong-questions/{studentId}/{wrongQuestionId}` - 删除错题

### 个人中心
- `GET /api/profile/student/{studentId}` - 获取学生信息
- `PUT /api/profile/student/{studentId}` - 更新学生信息
- `GET /api/profile/student/{studentId}/statistics` - 获取学习统计
- `GET /api/profile/settings/{userId}` - 获取应用设置
- `PUT /api/profile/settings/{userId}` - 更新应用设置

## 🧪 测试账号

- **手机号**: 13800138001，**密码**: password
- **手机号**: 13800138002，**密码**: password
- **手机号**: 13800138003，**密码**: password

## 📦 部署包结构

构建完成后，将生成以下文件：
```
build/
├── elementary-learning-server-{timestamp}.jar  # 服务器JAR包
├── elementary-learning-app-{timestamp}.apk     # Android APK
└── elementary-learning-{timestamp}/             # 部署目录
    ├── elementary-learning-server-{timestamp}.jar
    ├── elementary-learning-app-{timestamp}.apk
    ├── database_schema.sql
    ├── sample_data.sql
    └── DEPLOYMENT.md
```

## 🔧 配置说明

### 服务器配置
- **端口**: 默认8080，可通过 `server.port` 修改
- **数据库**: MySQL连接配置在 `application.properties`
- **CORS**: 支持跨域请求
- **日志**: 开发模式下显示SQL语句

### 客户端配置
- **服务器地址**: 在 `RetrofitClient.java` 中配置
- **网络超时**: 默认30秒
- **最低Android版本**: API 24 (Android 7.0)

## 🤝 开发指南

### 添加新功能
1. 后端：创建 Model → Repository → Service → Controller
2. 前端：创建 DTO → 更新 ApiService → 创建 ViewModel → 实现 UI

### 代码规范
- 后端：遵循Spring Boot最佳实践
- 前端：使用MVVM架构，Compose UI
- 数据库：使用JPA注解，遵循命名规范

## 📄 许可证

本项目采用 MIT 许可证。

---

**版本**: 1.0.0-BETA  
**最后更新**: 2025-06-16  
**构建状态**: ✅ 完成
