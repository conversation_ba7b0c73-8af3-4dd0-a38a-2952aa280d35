"""
股票相关数据模型
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional
from sqlalchemy import Column, Integer, String, DateTime, Date, Numeric, Boolean, Text, Index, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid

from app.core.database import Base


class Stock(Base):
    """股票基础信息表"""
    __tablename__ = "stocks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    code = Column(String(10), unique=True, index=True, nullable=False, comment="股票代码")
    name = Column(String(50), nullable=False, comment="股票名称")
    market = Column(String(10), nullable=False, comment="市场(SH/SZ)")
    industry = Column(String(50), comment="所属行业")
    sector = Column(String(50), comment="所属板块")
    list_date = Column(Date, comment="上市日期")
    delist_date = Column(Date, comment="退市日期")
    is_active = Column(Boolean, default=True, comment="是否活跃")
    
    # 基本信息
    total_share = Column(Numeric(20, 2), comment="总股本(万股)")
    float_share = Column(Numeric(20, 2), comment="流通股本(万股)")
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    quotes = relationship("StockQuote", back_populates="stock")
    financials = relationship("FinancialData", back_populates="stock")
    
    def __repr__(self):
        return f"<Stock(code={self.code}, name={self.name})>"


class StockQuote(Base):
    """股票行情数据表"""
    __tablename__ = "stock_quotes"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    stock_id = Column(UUID(as_uuid=True), ForeignKey("stocks.id"), nullable=False)
    trade_date = Column(Date, nullable=False, comment="交易日期")
    
    # 价格数据
    open_price = Column(Numeric(10, 3), comment="开盘价")
    high_price = Column(Numeric(10, 3), comment="最高价")
    low_price = Column(Numeric(10, 3), comment="最低价")
    close_price = Column(Numeric(10, 3), comment="收盘价")
    pre_close = Column(Numeric(10, 3), comment="昨收价")
    
    # 成交数据
    volume = Column(Numeric(20, 2), comment="成交量(股)")
    amount = Column(Numeric(20, 2), comment="成交额(元)")
    turnover_rate = Column(Numeric(8, 4), comment="换手率(%)")
    
    # 涨跌数据
    change = Column(Numeric(10, 3), comment="涨跌额")
    pct_change = Column(Numeric(8, 4), comment="涨跌幅(%)")
    
    # 估值数据
    pe_ratio = Column(Numeric(10, 4), comment="市盈率")
    pb_ratio = Column(Numeric(10, 4), comment="市净率")
    ps_ratio = Column(Numeric(10, 4), comment="市销率")
    market_cap = Column(Numeric(20, 2), comment="总市值(元)")
    float_cap = Column(Numeric(20, 2), comment="流通市值(元)")
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    stock = relationship("Stock", back_populates="quotes")
    
    # 索引
    __table_args__ = (
        Index("idx_stock_quote_date", "stock_id", "trade_date"),
        Index("idx_quote_date", "trade_date"),
    )
    
    def __repr__(self):
        return f"<StockQuote(stock_id={self.stock_id}, date={self.trade_date}, close={self.close_price})>"


class RealtimeQuote(Base):
    """实时行情数据表"""
    __tablename__ = "realtime_quotes"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    stock_id = Column(UUID(as_uuid=True), ForeignKey("stocks.id"), nullable=False)
    
    # 实时价格
    current_price = Column(Numeric(10, 3), comment="当前价")
    open_price = Column(Numeric(10, 3), comment="开盘价")
    high_price = Column(Numeric(10, 3), comment="最高价")
    low_price = Column(Numeric(10, 3), comment="最低价")
    pre_close = Column(Numeric(10, 3), comment="昨收价")
    
    # 买卖盘
    bid1_price = Column(Numeric(10, 3), comment="买一价")
    bid1_volume = Column(Numeric(15, 0), comment="买一量")
    ask1_price = Column(Numeric(10, 3), comment="卖一价")
    ask1_volume = Column(Numeric(15, 0), comment="卖一量")
    
    # 成交数据
    volume = Column(Numeric(20, 2), comment="成交量")
    amount = Column(Numeric(20, 2), comment="成交额")
    
    # 涨跌数据
    change = Column(Numeric(10, 3), comment="涨跌额")
    pct_change = Column(Numeric(8, 4), comment="涨跌幅")
    
    # 时间戳
    timestamp = Column(DateTime, default=datetime.utcnow, comment="数据时间")
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 索引
    __table_args__ = (
        Index("idx_realtime_stock", "stock_id"),
        Index("idx_realtime_timestamp", "timestamp"),
    )
    
    def __repr__(self):
        return f"<RealtimeQuote(stock_id={self.stock_id}, price={self.current_price}, time={self.timestamp})>"


class Sector(Base):
    """板块信息表"""
    __tablename__ = "sectors"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    code = Column(String(20), unique=True, index=True, nullable=False, comment="板块代码")
    name = Column(String(50), nullable=False, comment="板块名称")
    category = Column(String(20), nullable=False, comment="板块类别(行业/概念/地域)")
    parent_id = Column(UUID(as_uuid=True), ForeignKey("sectors.id"), comment="父板块ID")
    
    # 描述信息
    description = Column(Text, comment="板块描述")
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    parent = relationship("Sector", remote_side=[id])
    children = relationship("Sector")
    
    def __repr__(self):
        return f"<Sector(code={self.code}, name={self.name})>"


class SectorQuote(Base):
    """板块行情数据表"""
    __tablename__ = "sector_quotes"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sector_id = Column(UUID(as_uuid=True), ForeignKey("sectors.id"), nullable=False)
    trade_date = Column(Date, nullable=False, comment="交易日期")
    
    # 价格数据
    open_price = Column(Numeric(10, 3), comment="开盘价")
    high_price = Column(Numeric(10, 3), comment="最高价")
    low_price = Column(Numeric(10, 3), comment="最低价")
    close_price = Column(Numeric(10, 3), comment="收盘价")
    
    # 涨跌数据
    change = Column(Numeric(10, 3), comment="涨跌额")
    pct_change = Column(Numeric(8, 4), comment="涨跌幅")
    
    # 成交数据
    volume = Column(Numeric(20, 2), comment="成交量")
    amount = Column(Numeric(20, 2), comment="成交额")
    
    # 统计数据
    up_count = Column(Integer, comment="上涨家数")
    down_count = Column(Integer, comment="下跌家数")
    flat_count = Column(Integer, comment="平盘家数")
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 索引
    __table_args__ = (
        Index("idx_sector_quote_date", "sector_id", "trade_date"),
    )
    
    def __repr__(self):
        return f"<SectorQuote(sector_id={self.sector_id}, date={self.trade_date})>"


class FinancialData(Base):
    """财务数据表"""
    __tablename__ = "financial_data"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    stock_id = Column(UUID(as_uuid=True), ForeignKey("stocks.id"), nullable=False)
    report_date = Column(Date, nullable=False, comment="报告期")
    report_type = Column(String(10), nullable=False, comment="报告类型(Q1/Q2/Q3/A)")
    
    # 盈利能力
    revenue = Column(Numeric(20, 2), comment="营业收入")
    net_profit = Column(Numeric(20, 2), comment="净利润")
    gross_profit = Column(Numeric(20, 2), comment="毛利润")
    operating_profit = Column(Numeric(20, 2), comment="营业利润")
    
    # 成长能力
    revenue_growth = Column(Numeric(8, 4), comment="营收增长率")
    profit_growth = Column(Numeric(8, 4), comment="净利润增长率")
    
    # 盈利能力指标
    roe = Column(Numeric(8, 4), comment="净资产收益率")
    roa = Column(Numeric(8, 4), comment="总资产收益率")
    gross_margin = Column(Numeric(8, 4), comment="毛利率")
    net_margin = Column(Numeric(8, 4), comment="净利率")
    
    # 偿债能力
    total_assets = Column(Numeric(20, 2), comment="总资产")
    total_liabilities = Column(Numeric(20, 2), comment="总负债")
    total_equity = Column(Numeric(20, 2), comment="股东权益")
    debt_ratio = Column(Numeric(8, 4), comment="资产负债率")
    current_ratio = Column(Numeric(8, 4), comment="流动比率")
    
    # 营运能力
    inventory_turnover = Column(Numeric(8, 4), comment="存货周转率")
    receivable_turnover = Column(Numeric(8, 4), comment="应收账款周转率")
    total_asset_turnover = Column(Numeric(8, 4), comment="总资产周转率")
    
    # 现金流
    operating_cash_flow = Column(Numeric(20, 2), comment="经营现金流")
    investing_cash_flow = Column(Numeric(20, 2), comment="投资现金流")
    financing_cash_flow = Column(Numeric(20, 2), comment="筹资现金流")
    
    # 每股指标
    eps = Column(Numeric(10, 4), comment="每股收益")
    bps = Column(Numeric(10, 4), comment="每股净资产")
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    stock = relationship("Stock", back_populates="financials")
    
    # 索引
    __table_args__ = (
        Index("idx_financial_stock_date", "stock_id", "report_date"),
    )
    
    def __repr__(self):
        return f"<FinancialData(stock_id={self.stock_id}, date={self.report_date})>"


class TechnicalIndicator(Base):
    """技术指标数据表"""
    __tablename__ = "technical_indicators"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    stock_id = Column(UUID(as_uuid=True), ForeignKey("stocks.id"), nullable=False)
    trade_date = Column(Date, nullable=False, comment="交易日期")
    
    # 移动平均线
    ma5 = Column(Numeric(10, 3), comment="5日均线")
    ma10 = Column(Numeric(10, 3), comment="10日均线")
    ma20 = Column(Numeric(10, 3), comment="20日均线")
    ma60 = Column(Numeric(10, 3), comment="60日均线")
    
    # MACD
    macd_dif = Column(Numeric(10, 6), comment="MACD DIF")
    macd_dea = Column(Numeric(10, 6), comment="MACD DEA")
    macd_histogram = Column(Numeric(10, 6), comment="MACD 柱状图")
    
    # RSI
    rsi6 = Column(Numeric(8, 4), comment="6日RSI")
    rsi12 = Column(Numeric(8, 4), comment="12日RSI")
    rsi24 = Column(Numeric(8, 4), comment="24日RSI")
    
    # KDJ
    kdj_k = Column(Numeric(8, 4), comment="KDJ K值")
    kdj_d = Column(Numeric(8, 4), comment="KDJ D值")
    kdj_j = Column(Numeric(8, 4), comment="KDJ J值")
    
    # 布林带
    boll_upper = Column(Numeric(10, 3), comment="布林带上轨")
    boll_mid = Column(Numeric(10, 3), comment="布林带中轨")
    boll_lower = Column(Numeric(10, 3), comment="布林带下轨")
    
    # 成交量指标
    volume_ma5 = Column(Numeric(20, 2), comment="5日成交量均线")
    volume_ma10 = Column(Numeric(20, 2), comment="10日成交量均线")
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 索引
    __table_args__ = (
        Index("idx_technical_stock_date", "stock_id", "trade_date"),
    )
    
    def __repr__(self):
        return f"<TechnicalIndicator(stock_id={self.stock_id}, date={self.trade_date})>"
