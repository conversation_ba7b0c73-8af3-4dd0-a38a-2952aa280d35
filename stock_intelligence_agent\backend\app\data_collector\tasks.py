"""
数据采集 Celery 任务
"""

import asyncio
import logging
from datetime import datetime, date, timedelta
from typing import List, Optional
from celery import Celery
from celery.schedules import crontab
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings, CELERY_CONFIG
from app.core.database import Async<PERSON>essionLocal, get_redis, get_influxdb
from app.data_collector.collector_service import CollectorService

logger = logging.getLogger(__name__)

# 创建 Celery 应用
celery_app = Celery("stock_intelligence_collector")
celery_app.config_from_object(CELERY_CONFIG)


async def get_collector_service() -> CollectorService:
    """获取数据采集服务实例"""
    db = AsyncSessionLocal()
    redis = await get_redis()
    influxdb = await get_influxdb()

    return CollectorService(db, redis, influxdb)


@celery_app.task(bind=True, max_retries=3)
def collect_stock_list_task(self):
    """采集股票列表任务"""
    try:
        logger.info("开始执行股票列表采集任务...")

        async def _collect():
            collector = await get_collector_service()
            try:
                count = await collector.collect_stock_list()
                return count
            finally:
                await collector.close()

        # 运行异步任务
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        count = loop.run_until_complete(_collect())
        loop.close()

        logger.info(f"股票列表采集任务完成，采集 {count} 只股票")
        return {"status": "success", "count": count}

    except Exception as e:
        logger.error(f"股票列表采集任务失败: {e}")

        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试，第 {self.request.retries + 1} 次")
            raise self.retry(countdown=60 * (self.request.retries + 1))

        return {"status": "failed", "error": str(e)}


@celery_app.task(bind=True, max_retries=3)
def collect_realtime_quotes_task(self, stock_codes: Optional[List[str]] = None):
    """采集实时行情任务"""
    try:
        logger.info("开始执行实时行情采集任务...")

        async def _collect():
            collector = await get_collector_service()
            try:
                count = await collector.collect_realtime_quotes(stock_codes)
                return count
            finally:
                await collector.close()

        # 运行异步任务
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        count = loop.run_until_complete(_collect())
        loop.close()

        logger.info(f"实时行情采集任务完成，采集 {count} 条数据")
        return {"status": "success", "count": count}

    except Exception as e:
        logger.error(f"实时行情采集任务失败: {e}")

        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试，第 {self.request.retries + 1} 次")
            raise self.retry(countdown=30 * (self.request.retries + 1))

        return {"status": "failed", "error": str(e)}


@celery_app.task(bind=True, max_retries=3)
def collect_historical_quotes_task(
    self,
    stock_code: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
):
    """采集历史行情任务"""
    try:
        logger.info(f"开始执行股票 {stock_code} 历史行情采集任务...")

        # 转换日期格式
        start_date_obj = None
        end_date_obj = None

        if start_date:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
        if end_date:
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()

        async def _collect():
            collector = await get_collector_service()
            try:
                count = await collector.collect_historical_quotes(
                    stock_code, start_date_obj, end_date_obj
                )
                return count
            finally:
                await collector.close()

        # 运行异步任务
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        count = loop.run_until_complete(_collect())
        loop.close()

        logger.info(f"历史行情采集任务完成，采集 {count} 条数据")
        return {"status": "success", "count": count, "stock_code": stock_code}

    except Exception as e:
        logger.error(f"历史行情采集任务失败: {e}")

        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试，第 {self.request.retries + 1} 次")
            raise self.retry(countdown=60 * (self.request.retries + 1))

        return {"status": "failed", "error": str(e), "stock_code": stock_code}


@celery_app.task(bind=True, max_retries=3)
def collect_financial_data_task(self, stock_code: str):
    """采集财务数据任务"""
    try:
        logger.info(f"开始执行股票 {stock_code} 财务数据采集任务...")

        async def _collect():
            collector = await get_collector_service()
            try:
                success = await collector.collect_financial_data(stock_code)
                return success
            finally:
                await collector.close()

        # 运行异步任务
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        success = loop.run_until_complete(_collect())
        loop.close()

        if success:
            logger.info(f"财务数据采集任务完成")
            return {"status": "success", "stock_code": stock_code}
        else:
            logger.warning(f"财务数据采集任务未获取到数据")
            return {"status": "no_data", "stock_code": stock_code}

    except Exception as e:
        logger.error(f"财务数据采集任务失败: {e}")

        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试，第 {self.request.retries + 1} 次")
            raise self.retry(countdown=120 * (self.request.retries + 1))

        return {"status": "failed", "error": str(e), "stock_code": stock_code}


@celery_app.task(bind=True, max_retries=3)
def collect_sector_data_task(self):
    """采集板块数据任务"""
    try:
        logger.info("开始执行板块数据采集任务...")

        async def _collect():
            collector = await get_collector_service()
            try:
                count = await collector.collect_sector_data()
                return count
            finally:
                await collector.close()

        # 运行异步任务
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        count = loop.run_until_complete(_collect())
        loop.close()

        logger.info(f"板块数据采集任务完成，采集 {count} 个板块")
        return {"status": "success", "count": count}

    except Exception as e:
        logger.error(f"板块数据采集任务失败: {e}")

        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试，第 {self.request.retries + 1} 次")
            raise self.retry(countdown=60 * (self.request.retries + 1))

        return {"status": "failed", "error": str(e)}


# 定时任务配置
@celery_app.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    """设置定时任务"""

    # 每天凌晨1点采集股票列表
    sender.add_periodic_task(
        crontab(hour=1, minute=0),
        collect_stock_list_task.s(),
        name="daily_stock_list_collection"
    )

    # 交易时间内每5分钟采集实时行情
    # 周一到周五 9:30-11:30, 13:00-15:00
    sender.add_periodic_task(
        crontab(
            minute="*/5",
            hour="9-11,13-15",
            day_of_week="1-5"
        ),
        collect_realtime_quotes_task.s(),
        name="realtime_quotes_collection"
    )

    # 每天收盘后采集板块数据
    sender.add_periodic_task(
        crontab(hour=16, minute=0, day_of_week="1-5"),
        collect_sector_data_task.s(),
        name="daily_sector_data_collection"
    )


# 批量任务
@celery_app.task
def batch_collect_historical_quotes(stock_codes: List[str], days: int = 30):
    """批量采集历史行情"""
    try:
        logger.info(f"开始批量采集 {len(stock_codes)} 只股票的历史行情...")

        end_date = date.today()
        start_date = end_date - timedelta(days=days)

        # 创建子任务
        job = celery_app.group([
            collect_historical_quotes_task.s(
                stock_code,
                start_date.strftime("%Y-%m-%d"),
                end_date.strftime("%Y-%m-%d")
            )
            for stock_code in stock_codes
        ])

        # 执行批量任务
        result = job.apply_async()

        logger.info(f"批量历史行情采集任务已启动，任务ID: {result.id}")
        return {"status": "started", "job_id": result.id, "stock_count": len(stock_codes)}

    except Exception as e:
        logger.error(f"批量历史行情采集任务失败: {e}")
        return {"status": "failed", "error": str(e)}


@celery_app.task
def batch_collect_financial_data(stock_codes: List[str]):
    """批量采集财务数据"""
    try:
        logger.info(f"开始批量采集 {len(stock_codes)} 只股票的财务数据...")

        # 创建子任务
        job = celery_app.group([
            collect_financial_data_task.s(stock_code)
            for stock_code in stock_codes
        ])

        # 执行批量任务
        result = job.apply_async()

        logger.info(f"批量财务数据采集任务已启动，任务ID: {result.id}")
        return {"status": "started", "job_id": result.id, "stock_count": len(stock_codes)}

    except Exception as e:
        logger.error(f"批量财务数据采集任务失败: {e}")
        return {"status": "failed", "error": str(e)}


# 任务状态查询
@celery_app.task
def get_task_status(task_id: str):
    """获取任务状态"""
    try:
        result = celery_app.AsyncResult(task_id)

        return {
            "task_id": task_id,
            "status": result.status,
            "result": result.result,
            "traceback": result.traceback,
        }

    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        return {"status": "error", "error": str(e)}


if __name__ == "__main__":
    # 启动 Celery Worker
    celery_app.start()
