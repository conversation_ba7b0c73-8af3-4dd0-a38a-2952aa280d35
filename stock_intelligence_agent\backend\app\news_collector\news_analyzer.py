"""
新闻分析器
提供新闻内容分析、情感分析、关键词提取等功能
"""

import re
import jieba
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple, Set
from collections import Counter
import numpy as np

# 情感分析相关
try:
    from snownlp import SnowNLP
    SNOWNLP_AVAILABLE = True
except ImportError:
    SNOWNLP_AVAILABLE = False
    logging.warning("SnowNLP 未安装，情感分析功能将受限")

logger = logging.getLogger(__name__)


class NewsAnalyzer:
    """新闻分析器"""
    
    def __init__(self):
        # 加载停用词
        self.stop_words = self._load_stop_words()
        
        # 股票相关关键词
        self.stock_keywords = self._load_stock_keywords()
        
        # 情感词典
        self.sentiment_dict = self._load_sentiment_dict()
        
        # 重要性关键词权重
        self.importance_keywords = self._load_importance_keywords()
        
        logger.info("新闻分析器初始化完成")
    
    def analyze_article(self, article: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析单篇文章
        
        Args:
            article: 文章数据
            
        Returns:
            分析结果
        """
        try:
            title = article.get('title', '')
            content = article.get('content', '')
            summary = article.get('summary', '')
            
            # 合并文本用于分析
            full_text = f"{title} {summary} {content}".strip()
            
            if not full_text:
                return self._empty_analysis()
            
            # 关键词提取
            keywords = self.extract_keywords(full_text)
            
            # 情感分析
            sentiment = self.analyze_sentiment(full_text)
            
            # 相关股票识别
            related_stocks = self.extract_related_stocks(full_text)
            
            # 重要性评分
            importance_score = self.calculate_importance(article, keywords)
            
            # 分类预测
            category = self.predict_category(full_text, keywords)
            
            # 主题提取
            topics = self.extract_topics(keywords)
            
            analysis_result = {
                'keywords': keywords,
                'sentiment_score': sentiment['score'],
                'sentiment_label': sentiment['label'],
                'sentiment_confidence': sentiment['confidence'],
                'related_stocks': related_stocks,
                'importance_score': importance_score,
                'predicted_category': category,
                'topics': topics,
                'word_count': len(full_text),
                'analyzed_at': datetime.utcnow().isoformat()
            }
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"文章分析失败: {e}")
            return self._empty_analysis()
    
    def extract_keywords(self, text: str, top_k: int = 20) -> List[Dict[str, Any]]:
        """
        提取关键词
        
        Args:
            text: 文本内容
            top_k: 返回前k个关键词
            
        Returns:
            关键词列表
        """
        try:
            # 分词
            words = jieba.lcut(text)
            
            # 过滤停用词和短词
            filtered_words = [
                word for word in words
                if len(word) >= 2 and word not in self.stop_words
                and not re.match(r'^[\d\s\W]+$', word)
            ]
            
            # 词频统计
            word_freq = Counter(filtered_words)
            
            # 计算TF-IDF权重（简化版）
            keywords = []
            total_words = len(filtered_words)
            
            for word, freq in word_freq.most_common(top_k):
                tf = freq / total_words
                # 简化的IDF计算
                idf = 1.0
                if word in self.stock_keywords:
                    idf = 2.0  # 股票相关词汇权重更高
                
                score = tf * idf
                
                keywords.append({
                    'word': word,
                    'frequency': freq,
                    'score': score,
                    'category': self._classify_keyword(word)
                })
            
            return keywords
            
        except Exception as e:
            logger.error(f"关键词提取失败: {e}")
            return []
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """
        情感分析
        
        Args:
            text: 文本内容
            
        Returns:
            情感分析结果
        """
        try:
            if SNOWNLP_AVAILABLE and text:
                # 使用SnowNLP进行情感分析
                s = SnowNLP(text)
                score = s.sentiments
                
                # 情感标签
                if score > 0.6:
                    label = "positive"
                elif score < 0.4:
                    label = "negative"
                else:
                    label = "neutral"
                
                # 置信度
                confidence = abs(score - 0.5) * 2
                
            else:
                # 基于词典的简单情感分析
                sentiment_result = self._simple_sentiment_analysis(text)
                score = sentiment_result['score']
                label = sentiment_result['label']
                confidence = sentiment_result['confidence']
            
            return {
                'score': float(score),
                'label': label,
                'confidence': float(confidence)
            }
            
        except Exception as e:
            logger.error(f"情感分析失败: {e}")
            return {
                'score': 0.5,
                'label': 'neutral',
                'confidence': 0.0
            }
    
    def extract_related_stocks(self, text: str) -> List[str]:
        """
        提取相关股票代码
        
        Args:
            text: 文本内容
            
        Returns:
            股票代码列表
        """
        try:
            stock_codes = []
            
            # 匹配6位数字的股票代码
            code_pattern = r'\b[0-9]{6}\b'
            matches = re.findall(code_pattern, text)
            
            for match in matches:
                # 验证是否为有效股票代码
                if self._is_valid_stock_code(match):
                    stock_codes.append(match)
            
            # 去重并排序
            return sorted(list(set(stock_codes)))
            
        except Exception as e:
            logger.error(f"股票代码提取失败: {e}")
            return []
    
    def calculate_importance(self, article: Dict[str, Any], keywords: List[Dict[str, Any]]) -> float:
        """
        计算文章重要性评分
        
        Args:
            article: 文章数据
            keywords: 关键词列表
            
        Returns:
            重要性评分 (0-1)
        """
        try:
            score = 0.0
            
            # 基础分数
            base_score = 0.3
            
            # 来源权重
            source_priority = article.get('source_priority', 1)
            source_weight = min(source_priority / 5.0, 0.3)
            
            # 关键词权重
            keyword_weight = 0.0
            for keyword in keywords:
                word = keyword['word']
                if word in self.importance_keywords:
                    keyword_weight += self.importance_keywords[word] * keyword['score']
            
            keyword_weight = min(keyword_weight, 0.3)
            
            # 标题权重
            title = article.get('title', '')
            title_weight = 0.0
            if any(word in title for word in self.importance_keywords):
                title_weight = 0.1
            
            # 时效性权重
            published_at = article.get('published_at')
            time_weight = 0.0
            if published_at:
                if isinstance(published_at, str):
                    published_at = datetime.fromisoformat(published_at.replace('Z', '+00:00'))
                
                time_diff = (datetime.utcnow() - published_at).total_seconds() / 3600  # 小时
                if time_diff <= 1:
                    time_weight = 0.1
                elif time_diff <= 6:
                    time_weight = 0.05
            
            # 综合评分
            score = base_score + source_weight + keyword_weight + title_weight + time_weight
            
            return min(score, 1.0)
            
        except Exception as e:
            logger.error(f"重要性评分计算失败: {e}")
            return 0.5
    
    def predict_category(self, text: str, keywords: List[Dict[str, Any]]) -> str:
        """
        预测文章分类
        
        Args:
            text: 文本内容
            keywords: 关键词列表
            
        Returns:
            预测的分类
        """
        try:
            # 基于关键词的简单分类
            category_keywords = {
                'policy': ['政策', '监管', '央行', '证监会', '银保监会', '发改委', '财政部'],
                'market': ['市场', '行情', '涨跌', '成交', '资金', '板块'],
                'company': ['公司', '企业', '业绩', '财报', '公告', '股东'],
                'macro': ['经济', 'GDP', 'CPI', '通胀', '就业', '贸易'],
                'international': ['美股', '外汇', '国际', '全球', '美联储'],
                'technology': ['科技', '创新', '研发', '专利', '数字化'],
                'analysis': ['分析', '预测', '观点', '评论', '建议']
            }
            
            category_scores = {}
            
            # 计算每个分类的得分
            for category, category_words in category_keywords.items():
                score = 0
                for keyword in keywords:
                    word = keyword['word']
                    if any(cw in word for cw in category_words):
                        score += keyword['score']
                
                # 检查文本中的分类关键词
                for cw in category_words:
                    if cw in text:
                        score += 0.1
                
                category_scores[category] = score
            
            # 返回得分最高的分类
            if category_scores:
                best_category = max(category_scores, key=category_scores.get)
                if category_scores[best_category] > 0:
                    return best_category
            
            return 'general'
            
        except Exception as e:
            logger.error(f"分类预测失败: {e}")
            return 'general'
    
    def extract_topics(self, keywords: List[Dict[str, Any]], max_topics: int = 5) -> List[str]:
        """
        提取主题
        
        Args:
            keywords: 关键词列表
            max_topics: 最大主题数
            
        Returns:
            主题列表
        """
        try:
            # 基于关键词聚类的简单主题提取
            topics = []
            
            # 按分类分组关键词
            category_groups = {}
            for keyword in keywords:
                category = keyword.get('category', 'other')
                if category not in category_groups:
                    category_groups[category] = []
                category_groups[category].append(keyword['word'])
            
            # 生成主题
            for category, words in category_groups.items():
                if len(words) >= 2:  # 至少2个相关词汇才形成主题
                    topic = f"{category}:{','.join(words[:3])}"
                    topics.append(topic)
            
            return topics[:max_topics]
            
        except Exception as e:
            logger.error(f"主题提取失败: {e}")
            return []
    
    def _load_stop_words(self) -> Set[str]:
        """加载停用词"""
        stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '它', '他', '她', '们', '这个', '那个', '什么', '怎么',
            '为了', '因为', '所以', '但是', '然后', '如果', '虽然', '已经', '还是',
            '可以', '应该', '能够', '需要', '希望', '觉得', '认为', '表示', '指出'
        }
        return stop_words
    
    def _load_stock_keywords(self) -> Set[str]:
        """加载股票相关关键词"""
        keywords = {
            '股票', '股价', '涨停', '跌停', '涨幅', '跌幅', '成交量', '市值', '市盈率',
            '股东', '分红', '配股', '增发', '回购', '重组', '并购', 'IPO', '上市',
            '退市', '停牌', '复牌', '限售', '解禁', '减持', '增持', '举牌',
            '主力', '机构', '散户', '游资', '北向资金', '南向资金', '外资',
            '基金', '保险', '社保', '养老金', 'QFII', 'RQFII'
        }
        return keywords
    
    def _load_sentiment_dict(self) -> Dict[str, float]:
        """加载情感词典"""
        positive_words = {
            '上涨', '涨', '增长', '增加', '提升', '改善', '利好', '积极', '乐观',
            '强势', '突破', '创新高', '反弹', '回升', '企稳', '向好'
        }
        
        negative_words = {
            '下跌', '跌', '下降', '减少', '恶化', '利空', '消极', '悲观',
            '弱势', '跌破', '创新低', '回调', '下滑', '承压', '走弱'
        }
        
        sentiment_dict = {}
        for word in positive_words:
            sentiment_dict[word] = 1.0
        for word in negative_words:
            sentiment_dict[word] = -1.0
        
        return sentiment_dict
    
    def _load_importance_keywords(self) -> Dict[str, float]:
        """加载重要性关键词权重"""
        return {
            '重大', '重要', '紧急', '突发', '首次', '首个', '创历史',
            '监管', '政策', '央行', '证监会', '发改委', '财政部',
            '涨停', '跌停', '停牌', '退市', '重组', '并购',
            '业绩', '财报', '分红', '增持', '减持', '回购'
        }
    
    def _classify_keyword(self, word: str) -> str:
        """关键词分类"""
        if word in self.stock_keywords:
            return 'stock'
        elif any(char.isdigit() for char in word):
            return 'number'
        elif len(word) == 6 and word.isdigit():
            return 'stock_code'
        else:
            return 'general'
    
    def _simple_sentiment_analysis(self, text: str) -> Dict[str, Any]:
        """基于词典的简单情感分析"""
        words = jieba.lcut(text)
        sentiment_score = 0.0
        sentiment_count = 0
        
        for word in words:
            if word in self.sentiment_dict:
                sentiment_score += self.sentiment_dict[word]
                sentiment_count += 1
        
        if sentiment_count > 0:
            avg_score = sentiment_score / sentiment_count
            # 归一化到0-1
            normalized_score = (avg_score + 1) / 2
            
            if normalized_score > 0.6:
                label = "positive"
            elif normalized_score < 0.4:
                label = "negative"
            else:
                label = "neutral"
            
            confidence = abs(normalized_score - 0.5) * 2
        else:
            normalized_score = 0.5
            label = "neutral"
            confidence = 0.0
        
        return {
            'score': normalized_score,
            'label': label,
            'confidence': confidence
        }
    
    def _is_valid_stock_code(self, code: str) -> bool:
        """验证股票代码有效性"""
        if len(code) != 6 or not code.isdigit():
            return False
        
        # 简单的股票代码规则验证
        first_digit = code[0]
        if first_digit in ['0', '1', '2', '3', '6', '7', '8', '9']:
            return True
        
        return False
    
    def _empty_analysis(self) -> Dict[str, Any]:
        """返回空的分析结果"""
        return {
            'keywords': [],
            'sentiment_score': 0.5,
            'sentiment_label': 'neutral',
            'sentiment_confidence': 0.0,
            'related_stocks': [],
            'importance_score': 0.0,
            'predicted_category': 'general',
            'topics': [],
            'word_count': 0,
            'analyzed_at': datetime.utcnow().isoformat()
        }


# 全局分析器实例
news_analyzer = NewsAnalyzer()
