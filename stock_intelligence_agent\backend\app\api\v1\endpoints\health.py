"""
健康检查 API 端点
"""

from datetime import datetime
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db, check_db_health

router = APIRouter()


@router.get("/")
async def health_check():
    """
    系统健康检查
    """
    try:
        db_health = await check_db_health()
        
        # 计算整体健康状态
        healthy_services = sum(db_health.values())
        total_services = len(db_health)
        health_percentage = (healthy_services / total_services) * 100 if total_services > 0 else 0
        
        status = "healthy" if health_percentage == 100 else "degraded" if health_percentage >= 50 else "unhealthy"
        
        return {
            "status": status,
            "timestamp": datetime.utcnow().isoformat(),
            "services": db_health,
            "health_percentage": health_percentage,
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e),
        }


@router.get("/database")
async def database_health(db: AsyncSession = Depends(get_db)):
    """
    数据库健康检查
    """
    try:
        # 执行简单查询测试数据库连接
        result = await db.execute("SELECT 1")
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "database": "connected",
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "database": "disconnected",
            "error": str(e),
        }


@router.get("/services")
async def services_health():
    """
    各服务健康检查
    """
    services_status = {
        "api": "healthy",
        "database": "unknown",
        "redis": "unknown",
        "websocket": "healthy",
    }
    
    try:
        # 检查数据库
        db_health = await check_db_health()
        services_status["database"] = "healthy" if db_health.get("postgresql", False) else "unhealthy"
        services_status["redis"] = "healthy" if db_health.get("redis", False) else "unhealthy"
        
    except Exception as e:
        services_status["database"] = "unhealthy"
        services_status["redis"] = "unhealthy"
    
    # 计算整体状态
    healthy_count = sum(1 for status in services_status.values() if status == "healthy")
    total_count = len(services_status)
    overall_status = "healthy" if healthy_count == total_count else "degraded" if healthy_count >= total_count / 2 else "unhealthy"
    
    return {
        "status": overall_status,
        "timestamp": datetime.utcnow().isoformat(),
        "services": services_status,
        "healthy_services": healthy_count,
        "total_services": total_count,
    }
