"""
新闻服务
提供新闻采集、存储、分析的统一服务
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, func
from sqlalchemy.orm import selectinload

from app.models.news import News, NewsCategory, NewsKeyword
from app.news_collector.news_crawler import NewsCrawler
from app.news_collector.news_analyzer import NewsAnalyzer
from app.news_collector.news_sources import news_source_manager
from app.core.database import AsyncSessionLocal

logger = logging.getLogger(__name__)


class NewsService:
    """新闻服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.crawler = None
        self.analyzer = NewsAnalyzer()
    
    async def collect_and_process_news(self, max_articles_per_source: int = 50) -> Dict[str, Any]:
        """
        采集和处理新闻
        
        Args:
            max_articles_per_source: 每个源最大文章数
            
        Returns:
            处理结果统计
        """
        try:
            logger.info("开始新闻采集和处理")
            start_time = datetime.utcnow()
            
            # 采集新闻
            async with NewsCrawler() as crawler:
                articles = await crawler.crawl_all_sources(max_articles_per_source)
            
            if not articles:
                logger.warning("未采集到任何新闻")
                return {
                    "collected_count": 0,
                    "processed_count": 0,
                    "saved_count": 0,
                    "error_count": 0,
                    "duration": 0
                }
            
            logger.info(f"采集到 {len(articles)} 篇文章，开始处理")
            
            # 处理新闻
            processed_count = 0
            saved_count = 0
            error_count = 0
            
            for article in articles:
                try:
                    # 检查是否已存在
                    if await self._is_duplicate_article(article):
                        continue
                    
                    # 分析文章
                    analysis = self.analyzer.analyze_article(article)
                    
                    # 保存到数据库
                    news_id = await self._save_article(article, analysis)
                    if news_id:
                        saved_count += 1
                    
                    processed_count += 1
                    
                except Exception as e:
                    logger.error(f"处理文章失败: {e}")
                    error_count += 1
            
            # 提交事务
            await self.db.commit()
            
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            result = {
                "collected_count": len(articles),
                "processed_count": processed_count,
                "saved_count": saved_count,
                "error_count": error_count,
                "duration": duration,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat()
            }
            
            logger.info(f"新闻处理完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"新闻采集和处理失败: {e}")
            await self.db.rollback()
            raise
    
    async def get_news_list(
        self,
        skip: int = 0,
        limit: int = 20,
        category_id: Optional[str] = None,
        source: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        importance_min: Optional[int] = None,
        sentiment: Optional[str] = None,
        search_query: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取新闻列表
        
        Args:
            skip: 跳过数量
            limit: 限制数量
            category_id: 分类ID
            source: 新闻源
            start_date: 开始时间
            end_date: 结束时间
            importance_min: 最小重要性等级
            sentiment: 情感倾向
            search_query: 搜索关键词
            
        Returns:
            新闻列表和统计信息
        """
        try:
            # 构建查询条件
            conditions = [News.is_active == True]
            
            if category_id:
                conditions.append(News.category_id == category_id)
            
            if source:
                conditions.append(News.source == source)
            
            if start_date:
                conditions.append(News.published_at >= start_date)
            
            if end_date:
                conditions.append(News.published_at <= end_date)
            
            if importance_min:
                conditions.append(News.importance >= importance_min)
            
            if sentiment:
                conditions.append(News.sentiment_label == sentiment)
            
            if search_query:
                search_condition = or_(
                    News.title.ilike(f"%{search_query}%"),
                    News.summary.ilike(f"%{search_query}%"),
                    News.content.ilike(f"%{search_query}%")
                )
                conditions.append(search_condition)
            
            # 查询总数
            count_stmt = select(func.count(News.id)).where(and_(*conditions))
            count_result = await self.db.execute(count_stmt)
            total = count_result.scalar()
            
            # 查询数据
            stmt = (
                select(News)
                .where(and_(*conditions))
                .order_by(desc(News.published_at))
                .offset(skip)
                .limit(limit)
            )
            
            result = await self.db.execute(stmt)
            news_list = result.scalars().all()
            
            return {
                "news": [self._format_news_item(news) for news in news_list],
                "total": total,
                "skip": skip,
                "limit": limit,
                "has_more": skip + len(news_list) < total
            }
            
        except Exception as e:
            logger.error(f"获取新闻列表失败: {e}")
            raise
    
    async def get_news_by_id(self, news_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取新闻详情
        
        Args:
            news_id: 新闻ID
            
        Returns:
            新闻详情
        """
        try:
            stmt = select(News).where(News.id == news_id)
            result = await self.db.execute(stmt)
            news = result.scalar_one_or_none()
            
            if news:
                # 增加浏览次数
                news.view_count += 1
                await self.db.commit()
                
                return self._format_news_item(news, include_content=True)
            
            return None
            
        except Exception as e:
            logger.error(f"获取新闻详情失败: {e}")
            raise
    
    async def get_hot_news(self, limit: int = 10, hours: int = 24) -> List[Dict[str, Any]]:
        """
        获取热门新闻
        
        Args:
            limit: 返回数量
            hours: 时间范围（小时）
            
        Returns:
            热门新闻列表
        """
        try:
            since_time = datetime.utcnow() - timedelta(hours=hours)
            
            stmt = (
                select(News)
                .where(
                    and_(
                        News.is_active == True,
                        News.published_at >= since_time
                    )
                )
                .order_by(
                    desc(News.importance),
                    desc(News.view_count),
                    desc(News.published_at)
                )
                .limit(limit)
            )
            
            result = await self.db.execute(stmt)
            hot_news = result.scalars().all()
            
            return [self._format_news_item(news) for news in hot_news]
            
        except Exception as e:
            logger.error(f"获取热门新闻失败: {e}")
            raise
    
    async def get_news_by_stock(self, stock_code: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        获取股票相关新闻
        
        Args:
            stock_code: 股票代码
            limit: 返回数量
            
        Returns:
            相关新闻列表
        """
        try:
            # 查询包含股票代码的新闻
            conditions = [
                News.is_active == True,
                or_(
                    News.related_stocks.contains([stock_code]),
                    News.title.ilike(f"%{stock_code}%"),
                    News.content.ilike(f"%{stock_code}%")
                )
            ]
            
            stmt = (
                select(News)
                .where(and_(*conditions))
                .order_by(desc(News.published_at))
                .limit(limit)
            )
            
            result = await self.db.execute(stmt)
            related_news = result.scalars().all()
            
            return [self._format_news_item(news) for news in related_news]
            
        except Exception as e:
            logger.error(f"获取股票相关新闻失败: {e}")
            raise
    
    async def get_news_analytics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        stock_code: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取新闻分析统计
        
        Args:
            start_date: 开始时间
            end_date: 结束时间
            stock_code: 股票代码
            
        Returns:
            分析统计结果
        """
        try:
            # 默认时间范围为最近7天
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=7)
            if not end_date:
                end_date = datetime.utcnow()
            
            conditions = [
                News.is_active == True,
                News.published_at >= start_date,
                News.published_at <= end_date
            ]
            
            if stock_code:
                conditions.append(
                    or_(
                        News.related_stocks.contains([stock_code]),
                        News.title.ilike(f"%{stock_code}%")
                    )
                )
            
            # 基础统计
            base_stmt = select(News).where(and_(*conditions))
            result = await self.db.execute(base_stmt)
            news_list = result.scalars().all()
            
            total_news = len(news_list)
            
            # 情感分析统计
            sentiment_stats = {
                'positive': 0,
                'negative': 0,
                'neutral': 0
            }
            
            # 重要性统计
            importance_stats = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}
            
            # 来源统计
            source_stats = {}
            
            # 时间趋势
            daily_stats = {}
            
            for news in news_list:
                # 情感统计
                sentiment = news.sentiment_label or 'neutral'
                sentiment_stats[sentiment] = sentiment_stats.get(sentiment, 0) + 1
                
                # 重要性统计
                importance = news.importance or 1
                importance_stats[importance] = importance_stats.get(importance, 0) + 1
                
                # 来源统计
                source = news.source or 'unknown'
                source_stats[source] = source_stats.get(source, 0) + 1
                
                # 日期统计
                date_key = news.published_at.date().isoformat()
                if date_key not in daily_stats:
                    daily_stats[date_key] = {
                        'total': 0,
                        'positive': 0,
                        'negative': 0,
                        'neutral': 0
                    }
                daily_stats[date_key]['total'] += 1
                daily_stats[date_key][sentiment] += 1
            
            # 平均情感分数
            sentiment_scores = [news.sentiment_score for news in news_list if news.sentiment_score is not None]
            avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0.5
            
            return {
                'time_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'total_news': total_news,
                'sentiment_analysis': {
                    'distribution': sentiment_stats,
                    'average_score': avg_sentiment
                },
                'importance_distribution': importance_stats,
                'source_distribution': source_stats,
                'daily_trends': daily_stats,
                'analysis_date': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取新闻分析统计失败: {e}")
            raise
    
    async def search_news(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        搜索新闻
        
        Args:
            query: 搜索关键词
            limit: 返回数量
            
        Returns:
            搜索结果
        """
        try:
            search_condition = or_(
                News.title.ilike(f"%{query}%"),
                News.summary.ilike(f"%{query}%"),
                News.content.ilike(f"%{query}%")
            )
            
            stmt = (
                select(News)
                .where(
                    and_(
                        News.is_active == True,
                        search_condition
                    )
                )
                .order_by(
                    desc(News.importance),
                    desc(News.published_at)
                )
                .limit(limit)
            )
            
            result = await self.db.execute(stmt)
            search_results = result.scalars().all()
            
            return [self._format_news_item(news) for news in search_results]
            
        except Exception as e:
            logger.error(f"搜索新闻失败: {e}")
            raise
    
    async def _is_duplicate_article(self, article: Dict[str, Any]) -> bool:
        """检查文章是否重复"""
        try:
            title = article.get('title', '').strip()
            url = article.get('url', '').strip()
            
            if not title:
                return True
            
            # 检查标题相似度
            stmt = select(News).where(News.title == title)
            result = await self.db.execute(stmt)
            existing_news = result.scalar_one_or_none()
            
            if existing_news:
                return True
            
            # 检查URL
            if url:
                stmt = select(News).where(News.url == url)
                result = await self.db.execute(stmt)
                existing_news = result.scalar_one_or_none()
                
                if existing_news:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查重复文章失败: {e}")
            return False
    
    async def _save_article(self, article: Dict[str, Any], analysis: Dict[str, Any]) -> Optional[str]:
        """保存文章到数据库"""
        try:
            # 创建新闻记录
            news = News(
                title=article.get('title', ''),
                content=article.get('content', ''),
                summary=article.get('summary', ''),
                source=article.get('source_name', ''),
                author=article.get('author', ''),
                url=article.get('url', ''),
                published_at=article.get('published_at', datetime.utcnow()),
                tags=article.get('tags', []),
                related_stocks=analysis.get('related_stocks', []),
                importance=min(max(int(analysis.get('importance_score', 0) * 5), 1), 5),
                sentiment_score=analysis.get('sentiment_score'),
                sentiment_label=analysis.get('sentiment_label'),
                view_count=0,
                like_count=0,
                comment_count=0,
                is_active=True
            )
            
            self.db.add(news)
            await self.db.flush()  # 获取ID
            
            return str(news.id)
            
        except Exception as e:
            logger.error(f"保存文章失败: {e}")
            return None
    
    def _format_news_item(self, news: News, include_content: bool = False) -> Dict[str, Any]:
        """格式化新闻项目"""
        item = {
            'id': str(news.id),
            'title': news.title,
            'summary': news.summary,
            'source': news.source,
            'author': news.author,
            'url': news.url,
            'published_at': news.published_at.isoformat() if news.published_at else None,
            'tags': news.tags or [],
            'related_stocks': news.related_stocks or [],
            'importance': news.importance,
            'sentiment_score': float(news.sentiment_score) if news.sentiment_score else None,
            'sentiment_label': news.sentiment_label,
            'view_count': news.view_count,
            'like_count': news.like_count,
            'comment_count': news.comment_count,
            'created_at': news.created_at.isoformat() if news.created_at else None,
            'updated_at': news.updated_at.isoformat() if news.updated_at else None
        }
        
        if include_content:
            item['content'] = news.content
        
        return item


# 便捷函数
async def collect_news(max_articles_per_source: int = 50) -> Dict[str, Any]:
    """采集新闻的便捷函数"""
    async with AsyncSessionLocal() as db:
        service = NewsService(db)
        return await service.collect_and_process_news(max_articles_per_source)
