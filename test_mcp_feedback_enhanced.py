#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP Feedback Enhanced 集成测试脚本
"""

import json
import subprocess
import sys
import time
import os
from pathlib import Path

def test_installation():
    """测试安装和基本功能"""
    print("=== MCP Feedback Enhanced 集成测试 ===")
    
    # 1. 测试uvx命令
    print("1. 测试uvx命令")
    try:
        result = subprocess.run(["uvx", "--version"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"   [OK] uvx可用")
        else:
            print("   [FAIL] uvx命令不可用")
            return False
    except Exception as e:
        print(f"   [FAIL] uvx测试失败: {e}")
        return False
    
    # 2. 测试MCP包安装
    print("2. 测试MCP包安装")
    try:
        result = subprocess.run([
            "uvx", "mcp-feedback-enhanced@latest", "test"
        ], capture_output=True, text=True, timeout=60)
        
        if "測試功能已簡化" in result.stdout or "test" in result.stdout.lower():
            print("   [OK] MCP包安装成功")
        else:
            print(f"   [FAIL] MCP包测试失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"   [FAIL] MCP包测试异常: {e}")
        return False
    
    # 3. 检查配置文件
    print("3. 检查配置文件")
    config_files = [
        "augment-mcp-feedback-enhanced-config.json",
        "augment-mcp-feedback-enhanced-desktop-config.json"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"   [OK] {config_file} 格式正确")
            except Exception as e:
                print(f"   [FAIL] {config_file} 格式错误: {e}")
                return False
        else:
            print(f"   [FAIL] {config_file} 不存在")
            return False
    
    # 4. 测试Web UI启动（快速测试）
    print("4. 测试Web UI启动")
    try:
        process = subprocess.Popen([
            "uvx", "mcp-feedback-enhanced@latest", "test", "--web"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待启动
        time.sleep(8)
        
        if process.poll() is None:
            print("   [OK] Web UI启动成功")
            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"   [FAIL] Web UI启动失败")
            print(f"   输出: {stdout}")
            print(f"   错误: {stderr}")
            return False
            
    except Exception as e:
        print(f"   [FAIL] Web UI测试异常: {e}")
        if 'process' in locals():
            try:
                process.terminate()
            except:
                pass
        return False

def show_configuration_guide():
    """显示配置指南"""
    print("\n" + "="*60)
    print("MCP Feedback Enhanced 配置指南")
    print("="*60)

    print("\n选择配置模式:")
    print("1. Web UI模式 (推荐) - 使用 augment-mcp-feedback-enhanced-config.json")
    print("2. 桌面应用模式 - 使用 augment-mcp-feedback-enhanced-desktop-config.json")

    print("\n配置步骤:")
    print("1. 将选择的配置文件内容复制到Augment的MCP设置中")
    print("2. 重启Augment")
    print("3. 测试 interactive_feedback 工具")

    print("\n增强功能:")
    print("- 图片上传支持 (拖拽、Ctrl+V)")
    print("- 提示词管理和自动提交")
    print("- 会话历史和统计")
    print("- 现代化UI和多语言支持")
    print("- 桌面应用和Web UI双模式")

    print("\n测试命令:")
    print("请使用interactive_feedback工具，展示增强版的图片上传和提示词管理功能。")

def main():
    """主函数"""
    print("开始MCP Feedback Enhanced集成测试...")

    success = test_installation()

    if success:
        print("\n所有测试通过！")
        print("MCP Feedback Enhanced已准备好集成到Augment中。")
        show_configuration_guide()
    else:
        print("\n测试失败，请检查相关问题。")
        print("\n故障排除建议:")
        print("1. 确认uvx已正确安装")
        print("2. 检查网络连接")
        print("3. 尝试手动运行: uvx mcp-feedback-enhanced@latest test")

if __name__ == "__main__":
    main()
