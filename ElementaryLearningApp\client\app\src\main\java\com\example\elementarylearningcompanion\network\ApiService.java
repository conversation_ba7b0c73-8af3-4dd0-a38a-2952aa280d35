package com.example.elementarylearningcompanion.network;

import com.example.elementarylearningcompanion.dto.ApiResponse;
import com.example.elementarylearningcompanion.dto.AuthRequest;
import com.example.elementarylearningcompanion.dto.Character;
import com.example.elementarylearningcompanion.dto.EnglishWord;
import com.example.elementarylearningcompanion.dto.Lesson;
import com.example.elementarylearningcompanion.dto.MathExercise;
import com.example.elementarylearningcompanion.dto.TextbookVersion;
import com.example.elementarylearningcompanion.dto.WrongQuestion;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;
import retrofit2.http.Query;

import java.util.List;
import java.util.Map;

public interface ApiService {

    @POST("api/auth/register")
    Call<ApiResponse> registerParent(@Body AuthRequest authRequest);

    @POST("api/auth/login")
    Call<ApiResponse> login(@Body AuthRequest authRequest);

    @GET("api/content/textbooks")
    Call<List<TextbookVersion>> getTextbookVersions(@Query("subject") String subject);

    @GET("api/content/lessons")
    Call<List<Lesson>> getLessons(@Query("textbookVersionId") int textbookVersionId, @Query("grade") int grade);

    @GET("api/content/lessons/{id}")
    Call<Lesson> getLessonDetails(@Path("id") long lessonId);

    @GET("api/content/lessons/{lessonId}/characters")
    Call<List<Character>> getCharactersByLesson(@Path("lessonId") long lessonId);

    // Math API endpoints
    @GET("api/math/exercises/generate")
    Call<List<MathExercise>> generateMathExercises(
        @Query("exerciseType") String exerciseType,
        @Query("difficultyLevel") int difficultyLevel,
        @Query("count") int count
    );

    @GET("api/math/practice-types")
    Call<List<Map<String, Object>>> getMathPracticeTypes();

    @POST("api/math/submit-answer")
    Call<ApiResponse> submitMathAnswer(@Body Map<String, Object> request);

    @GET("api/math/wrong-questions/{studentId}")
    Call<List<WrongQuestion>> getWrongQuestions(@Path("studentId") long studentId);

    // English API endpoints
    @GET("api/english/words")
    Call<List<EnglishWord>> getEnglishWords(
        @Query("textbookVersionId") int textbookVersionId,
        @Query("grade") int grade,
        @Query("unit") String unit
    );

    @GET("api/english/words/random")
    Call<List<EnglishWord>> getRandomEnglishWords(
        @Query("textbookVersionId") int textbookVersionId,
        @Query("grade") int grade,
        @Query("count") int count,
        @Query("difficultyLevel") Integer difficultyLevel
    );

    @GET("api/english/learning-modes")
    Call<List<Map<String, Object>>> getEnglishLearningModes();

    @POST("api/english/record-learning")
    Call<ApiResponse> recordEnglishLearning(@Body Map<String, Object> request);

    @GET("api/english/spelling-test")
    Call<List<EnglishWord>> getSpellingTest(
        @Query("textbookVersionId") int textbookVersionId,
        @Query("grade") int grade,
        @Query("count") int count
    );

    // Wrong Questions API endpoints
    @GET("api/wrong-questions/{studentId}")
    Call<List<WrongQuestion>> getAllWrongQuestions(@Path("studentId") long studentId);

    @GET("api/wrong-questions/{studentId}/by-type")
    Call<List<WrongQuestion>> getWrongQuestionsByType(
        @Path("studentId") long studentId,
        @Query("contentType") String contentType
    );

    @GET("api/wrong-questions/{studentId}/statistics")
    Call<Map<String, Object>> getWrongQuestionStatistics(@Path("studentId") long studentId);

    @GET("api/wrong-questions/{studentId}/detailed")
    Call<List<Map<String, Object>>> getWrongQuestionsWithDetails(@Path("studentId") long studentId);

    @POST("api/wrong-questions/{studentId}/mark-mastered/{wrongQuestionId}")
    Call<ApiResponse> markWrongQuestionAsMastered(
        @Path("studentId") long studentId,
        @Path("wrongQuestionId") long wrongQuestionId
    );

    @DELETE("api/wrong-questions/{studentId}/{wrongQuestionId}")
    Call<ApiResponse> deleteWrongQuestion(
        @Path("studentId") long studentId,
        @Path("wrongQuestionId") long wrongQuestionId
    );

    // User Profile API endpoints
    @GET("api/profile/student/{studentId}")
    Call<Student> getStudentProfile(@Path("studentId") long studentId);

    @PUT("api/profile/student/{studentId}")
    Call<ApiResponse> updateStudentProfile(
        @Path("studentId") long studentId,
        @Body Map<String, Object> request
    );

    @GET("api/profile/student/{studentId}/statistics")
    Call<Map<String, Object>> getStudentStatistics(@Path("studentId") long studentId);

    @GET("api/profile/settings/{userId}")
    Call<Map<String, Object>> getAppSettings(@Path("userId") long userId);

    @PUT("api/profile/settings/{userId}")
    Call<ApiResponse> updateAppSettings(
        @Path("userId") long userId,
        @Body Map<String, Object> settings
    );

    @GET("api/profile/grades")
    Call<Map<String, Object>> getAvailableGrades();
}
