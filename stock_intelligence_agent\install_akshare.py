#!/usr/bin/env python3
"""
安装AKShare库的脚本
"""

import subprocess
import sys
import os

def install_akshare():
    """安装AKShare库"""
    try:
        print("🚀 开始安装AKShare...")
        
        # 尝试安装AKShare
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "akshare", "-i", "https://pypi.tuna.tsinghua.edu.cn/simple/"
        ], capture_output=True, text=True, check=True)
        
        print("✅ AKShare安装成功!")
        print(result.stdout)
        
        # 测试导入
        try:
            import akshare as ak
            print("✅ AKShare导入测试成功!")
            
            # 测试获取股票数据
            print("🧪 测试获取股票数据...")
            df = ak.stock_zh_a_spot_em()
            print(f"✅ 成功获取{len(df)}只股票数据")
            
            # 测试研报数据
            print("🧪 测试获取研报数据...")
            try:
                research_df = ak.stock_research_report_em(symbol="000001")
                print(f"✅ 成功获取{len(research_df)}条研报数据")
            except Exception as e:
                print(f"⚠️ 研报数据测试失败: {e}")
                
        except ImportError as e:
            print(f"❌ AKShare导入失败: {e}")
            return False
            
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ AKShare安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def check_akshare():
    """检查AKShare是否已安装"""
    try:
        import akshare as ak
        print("✅ AKShare已安装")
        return True
    except ImportError:
        print("❌ AKShare未安装")
        return False

if __name__ == "__main__":
    print("📊 AKShare安装工具")
    print("=" * 50)
    
    if check_akshare():
        print("AKShare已经安装，无需重复安装")
    else:
        if install_akshare():
            print("\n🎉 AKShare安装完成！")
            print("现在可以使用真实的A股研报数据了！")
        else:
            print("\n❌ AKShare安装失败")
            print("请手动执行: pip install akshare")
