"""
WebSocket 消息处理器
处理客户端发送的各种消息类型
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import WebSocket

from app.websocket.connection_manager import ConnectionManager
from app.services.stock_service import StockService
from app.core.database import AsyncSessionLocal, get_redis

logger = logging.getLogger(__name__)


class MessageHandler:
    """WebSocket 消息处理器"""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager
    
    async def handle_message(
        self,
        connection_id: str,
        message: Dict[str, Any],
        websocket: WebSocket
    ):
        """
        处理客户端消息
        
        Args:
            connection_id: 连接ID
            message: 消息内容
            websocket: WebSocket 连接
        """
        try:
            message_type = message.get("type")
            
            if not message_type:
                await self._send_error(connection_id, "缺少消息类型")
                return
            
            # 更新心跳时间
            await self.connection_manager.update_heartbeat(connection_id)
            
            # 根据消息类型分发处理
            handler_map = {
                "ping": self._handle_ping,
                "subscribe_stock": self._handle_subscribe_stock,
                "unsubscribe_stock": self._handle_unsubscribe_stock,
                "subscribe_news": self._handle_subscribe_news,
                "unsubscribe_news": self._handle_unsubscribe_news,
                "get_stock_data": self._handle_get_stock_data,
                "get_market_overview": self._handle_get_market_overview,
                "search_stocks": self._handle_search_stocks,
                "get_hot_stocks": self._handle_get_hot_stocks,
                "get_user_alerts": self._handle_get_user_alerts,
                "update_preferences": self._handle_update_preferences,
            }
            
            handler = handler_map.get(message_type)
            if handler:
                await handler(connection_id, message)
            else:
                await self._send_error(connection_id, f"未知的消息类型: {message_type}")
                
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
            await self._send_error(connection_id, "消息处理失败")
    
    async def _handle_ping(self, connection_id: str, message: Dict[str, Any]):
        """处理心跳消息"""
        await self.connection_manager.send_personal_message(connection_id, {
            "type": "pong",
            "timestamp": datetime.utcnow().isoformat()
        })
    
    async def _handle_subscribe_stock(self, connection_id: str, message: Dict[str, Any]):
        """处理股票订阅"""
        try:
            stock_code = message.get("stock_code")
            if not stock_code:
                await self._send_error(connection_id, "缺少股票代码")
                return
            
            await self.connection_manager.subscribe_stock(connection_id, stock_code)
            
        except Exception as e:
            logger.error(f"处理股票订阅失败: {e}")
            await self._send_error(connection_id, "股票订阅失败")
    
    async def _handle_unsubscribe_stock(self, connection_id: str, message: Dict[str, Any]):
        """处理取消股票订阅"""
        try:
            stock_code = message.get("stock_code")
            if not stock_code:
                await self._send_error(connection_id, "缺少股票代码")
                return
            
            await self.connection_manager.unsubscribe_stock(connection_id, stock_code)
            
        except Exception as e:
            logger.error(f"处理取消股票订阅失败: {e}")
            await self._send_error(connection_id, "取消股票订阅失败")
    
    async def _handle_subscribe_news(self, connection_id: str, message: Dict[str, Any]):
        """处理新闻订阅"""
        try:
            category = message.get("category", "all")
            await self.connection_manager.subscribe_news(connection_id, category)
            
        except Exception as e:
            logger.error(f"处理新闻订阅失败: {e}")
            await self._send_error(connection_id, "新闻订阅失败")
    
    async def _handle_unsubscribe_news(self, connection_id: str, message: Dict[str, Any]):
        """处理取消新闻订阅"""
        try:
            category = message.get("category", "all")
            await self.connection_manager.unsubscribe_news(connection_id, category)
            
        except Exception as e:
            logger.error(f"处理取消新闻订阅失败: {e}")
            await self._send_error(connection_id, "取消新闻订阅失败")
    
    async def _handle_get_stock_data(self, connection_id: str, message: Dict[str, Any]):
        """处理获取股票数据请求"""
        try:
            stock_code = message.get("stock_code")
            if not stock_code:
                await self._send_error(connection_id, "缺少股票代码")
                return
            
            # 获取股票数据
            async with AsyncSessionLocal() as db:
                redis_client = await get_redis()
                stock_service = StockService(db, redis_client)
                
                # 获取股票基本信息
                stock = await stock_service.get_stock_by_code(stock_code)
                if not stock:
                    await self._send_error(connection_id, "股票不存在")
                    return
                
                # 获取实时行情
                realtime_quote = await stock_service.get_realtime_quote_from_cache(stock_code)
                if not realtime_quote:
                    realtime_quote = await stock_service.get_latest_realtime_quote(stock.id)
                
                # 获取技术分析
                technical_analysis = await stock_service.get_technical_analysis(stock.id)
                
                response_data = {
                    "type": "stock_data_response",
                    "stock_code": stock_code,
                    "data": {
                        "basic_info": {
                            "code": stock.code,
                            "name": stock.name,
                            "market": stock.market,
                            "industry": stock.industry
                        },
                        "realtime_quote": realtime_quote,
                        "technical_analysis": technical_analysis
                    },
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                await self.connection_manager.send_personal_message(connection_id, response_data)
                
        except Exception as e:
            logger.error(f"处理获取股票数据失败: {e}")
            await self._send_error(connection_id, "获取股票数据失败")
    
    async def _handle_get_market_overview(self, connection_id: str, message: Dict[str, Any]):
        """处理获取市场概览请求"""
        try:
            async with AsyncSessionLocal() as db:
                redis_client = await get_redis()
                stock_service = StockService(db, redis_client)
                
                # 获取热门股票
                hot_stocks = await stock_service.get_hot_stocks(limit=10)
                
                # 获取股票统计
                statistics = await stock_service.get_stock_statistics()
                
                response_data = {
                    "type": "market_overview_response",
                    "data": {
                        "hot_stocks": hot_stocks,
                        "statistics": statistics,
                        "market_status": self._get_market_status()
                    },
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                await self.connection_manager.send_personal_message(connection_id, response_data)
                
        except Exception as e:
            logger.error(f"处理获取市场概览失败: {e}")
            await self._send_error(connection_id, "获取市场概览失败")
    
    async def _handle_search_stocks(self, connection_id: str, message: Dict[str, Any]):
        """处理股票搜索请求"""
        try:
            query = message.get("query")
            if not query:
                await self._send_error(connection_id, "缺少搜索关键词")
                return
            
            limit = message.get("limit", 10)
            
            async with AsyncSessionLocal() as db:
                stock_service = StockService(db)
                
                # 搜索股票
                stocks = await stock_service.search_stocks(query, limit)
                
                response_data = {
                    "type": "search_stocks_response",
                    "query": query,
                    "data": [
                        {
                            "code": stock.code,
                            "name": stock.name,
                            "market": stock.market,
                            "industry": stock.industry
                        }
                        for stock in stocks
                    ],
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                await self.connection_manager.send_personal_message(connection_id, response_data)
                
        except Exception as e:
            logger.error(f"处理股票搜索失败: {e}")
            await self._send_error(connection_id, "股票搜索失败")
    
    async def _handle_get_hot_stocks(self, connection_id: str, message: Dict[str, Any]):
        """处理获取热门股票请求"""
        try:
            limit = message.get("limit", 20)
            sort_by = message.get("sort_by", "pct_change")
            order = message.get("order", "desc")
            
            async with AsyncSessionLocal() as db:
                stock_service = StockService(db)
                
                # 获取热门股票
                hot_stocks = await stock_service.get_hot_stocks(
                    limit=limit,
                    sort_by=sort_by,
                    order=order
                )
                
                response_data = {
                    "type": "hot_stocks_response",
                    "data": hot_stocks,
                    "sort_by": sort_by,
                    "order": order,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                await self.connection_manager.send_personal_message(connection_id, response_data)
                
        except Exception as e:
            logger.error(f"处理获取热门股票失败: {e}")
            await self._send_error(connection_id, "获取热门股票失败")
    
    async def _handle_get_user_alerts(self, connection_id: str, message: Dict[str, Any]):
        """处理获取用户预警请求"""
        try:
            # 获取用户ID
            metadata = self.connection_manager.connection_metadata.get(connection_id, {})
            user_id = metadata.get("user_id")
            
            if not user_id:
                await self._send_error(connection_id, "用户未登录")
                return
            
            # TODO: 实现获取用户预警逻辑
            response_data = {
                "type": "user_alerts_response",
                "data": {
                    "alerts": [],
                    "unread_count": 0
                },
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await self.connection_manager.send_personal_message(connection_id, response_data)
            
        except Exception as e:
            logger.error(f"处理获取用户预警失败: {e}")
            await self._send_error(connection_id, "获取用户预警失败")
    
    async def _handle_update_preferences(self, connection_id: str, message: Dict[str, Any]):
        """处理更新用户偏好请求"""
        try:
            preferences = message.get("preferences", {})
            
            # 更新连接元数据中的偏好设置
            if connection_id in self.connection_manager.connection_metadata:
                metadata = self.connection_manager.connection_metadata[connection_id]
                if "preferences" not in metadata:
                    metadata["preferences"] = {}
                metadata["preferences"].update(preferences)
            
            response_data = {
                "type": "preferences_updated",
                "data": preferences,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await self.connection_manager.send_personal_message(connection_id, response_data)
            
        except Exception as e:
            logger.error(f"处理更新用户偏好失败: {e}")
            await self._send_error(connection_id, "更新用户偏好失败")
    
    async def _send_error(self, connection_id: str, error_message: str):
        """发送错误消息"""
        error_data = {
            "type": "error",
            "message": error_message,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.connection_manager.send_personal_message(connection_id, error_data)
    
    def _get_market_status(self) -> Dict[str, Any]:
        """获取市场状态"""
        now = datetime.now()
        hour = now.hour
        minute = now.minute
        weekday = now.weekday()  # 0=Monday, 6=Sunday
        
        # 判断是否为交易日（周一到周五）
        is_trading_day = weekday < 5
        
        # 判断是否为交易时间
        is_trading_time = False
        session = "closed"
        
        if is_trading_day:
            # 上午交易时间 9:30-11:30
            if (hour == 9 and minute >= 30) or (hour == 10) or (hour == 11 and minute <= 30):
                is_trading_time = True
                session = "morning"
            # 下午交易时间 13:00-15:00
            elif (hour == 13) or (hour == 14) or (hour == 15 and minute == 0):
                is_trading_time = True
                session = "afternoon"
            # 集合竞价时间
            elif (hour == 9 and minute < 30):
                session = "call_auction"
        
        return {
            "is_trading_day": is_trading_day,
            "is_trading_time": is_trading_time,
            "session": session,
            "current_time": now.isoformat(),
            "next_trading_session": self._get_next_trading_session(now)
        }
    
    def _get_next_trading_session(self, current_time: datetime) -> Optional[str]:
        """获取下一个交易时段"""
        hour = current_time.hour
        minute = current_time.minute
        weekday = current_time.weekday()
        
        if weekday < 5:  # 工作日
            if hour < 9 or (hour == 9 and minute < 30):
                return "09:30 开盘"
            elif hour < 13:
                return "13:00 开盘"
            elif hour < 15:
                return None  # 当前在交易时间
            else:
                return "明日 09:30 开盘"
        else:  # 周末
            days_until_monday = (7 - weekday) % 7
            if days_until_monday == 0:
                days_until_monday = 1
            return f"{days_until_monday}天后 09:30 开盘"


# 全局消息处理器实例
message_handler = None

def get_message_handler(connection_manager: ConnectionManager) -> MessageHandler:
    """获取消息处理器实例"""
    global message_handler
    if message_handler is None:
        message_handler = MessageHandler(connection_manager)
    return message_handler
