package com.example.elementarylearningcompanion.service;

import com.example.elementarylearningcompanion.model.*;
import com.example.elementarylearningcompanion.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

@Service
public class WrongQuestionService {

    @Autowired
    private WrongQuestionRepository wrongQuestionRepository;

    @Autowired
    private QuestionRepository questionRepository;

    @Autowired
    private MathExerciseRepository mathExerciseRepository;

    @Autowired
    private EnglishWordRepository englishWordRepository;

    public List<WrongQuestion> getAllWrongQuestions(Long studentId) {
        return wrongQuestionRepository.findByStudentIdAndIsMasteredFalseOrderByCreatedAtDesc(studentId);
    }

    public List<WrongQuestion> getWrongQuestionsByType(Long studentId, String contentType) {
        return wrongQuestionRepository.findByStudentIdAndContentTypeAndIsMasteredFalse(studentId, contentType);
    }

    public Map<String, Object> getWrongQuestionStatistics(Long studentId) {
        Map<String, Object> stats = new HashMap<>();
        
        Long mathCount = wrongQuestionRepository.countUnmasteredQuestions(studentId, "math_exercise");
        Long englishCount = wrongQuestionRepository.countUnmasteredQuestions(studentId, "english_word");
        Long questionCount = wrongQuestionRepository.countUnmasteredQuestions(studentId, "question");
        
        stats.put("mathExercises", mathCount);
        stats.put("englishWords", englishCount);
        stats.put("generalQuestions", questionCount);
        stats.put("total", mathCount + englishCount + questionCount);
        
        return stats;
    }

    public List<Map<String, Object>> getWrongQuestionsWithDetails(Long studentId) {
        List<WrongQuestion> wrongQuestions = getAllWrongQuestions(studentId);
        List<Map<String, Object>> detailedQuestions = new ArrayList<>();

        for (WrongQuestion wrongQuestion : wrongQuestions) {
            Map<String, Object> detail = new HashMap<>();
            detail.put("wrongQuestion", wrongQuestion);
            
            switch (wrongQuestion.getContentType()) {
                case "math_exercise":
                    Optional<MathExercise> mathExercise = mathExerciseRepository.findById(wrongQuestion.getContentId());
                    if (mathExercise.isPresent()) {
                        detail.put("content", mathExercise.get());
                        detail.put("title", mathExercise.get().getQuestion());
                        detail.put("type", "数学练习");
                    }
                    break;
                case "english_word":
                    Optional<EnglishWord> englishWord = englishWordRepository.findById(wrongQuestion.getContentId());
                    if (englishWord.isPresent()) {
                        detail.put("content", englishWord.get());
                        detail.put("title", englishWord.get().getWord() + " - " + englishWord.get().getMeaningChinese());
                        detail.put("type", "英语单词");
                    }
                    break;
                case "question":
                    Optional<Question> question = questionRepository.findById(wrongQuestion.getContentId());
                    if (question.isPresent()) {
                        detail.put("content", question.get());
                        detail.put("title", question.get().getQuestionText());
                        detail.put("type", getQuestionTypeDisplayName(question.get().getQuestionType()));
                    }
                    break;
            }
            
            detailedQuestions.add(detail);
        }

        return detailedQuestions;
    }

    private String getQuestionTypeDisplayName(String questionType) {
        switch (questionType) {
            case "single_choice": return "单选题";
            case "multiple_choice": return "多选题";
            case "true_false": return "判断题";
            case "fill_blank": return "填空题";
            default: return "题目";
        }
    }

    @Transactional
    public boolean markAsMastered(Long studentId, Long wrongQuestionId) {
        Optional<WrongQuestion> wrongQuestionOpt = wrongQuestionRepository.findById(wrongQuestionId);
        if (wrongQuestionOpt.isPresent()) {
            WrongQuestion wrongQuestion = wrongQuestionOpt.get();
            if (wrongQuestion.getStudentId().equals(studentId)) {
                wrongQuestion.setIsMastered(true);
                wrongQuestionRepository.save(wrongQuestion);
                return true;
            }
        }
        return false;
    }

    @Transactional
    public boolean incrementRetryCount(Long studentId, Long wrongQuestionId) {
        Optional<WrongQuestion> wrongQuestionOpt = wrongQuestionRepository.findById(wrongQuestionId);
        if (wrongQuestionOpt.isPresent()) {
            WrongQuestion wrongQuestion = wrongQuestionOpt.get();
            if (wrongQuestion.getStudentId().equals(studentId)) {
                wrongQuestion.setRetryCount(wrongQuestion.getRetryCount() + 1);
                wrongQuestionRepository.save(wrongQuestion);
                return true;
            }
        }
        return false;
    }

    public List<WrongQuestion> getQuestionsForReview(Long studentId, Integer limit) {
        List<WrongQuestion> allQuestions = wrongQuestionRepository.findQuestionsForReview(studentId);
        if (limit != null && limit > 0 && allQuestions.size() > limit) {
            return allQuestions.subList(0, limit);
        }
        return allQuestions;
    }

    @Transactional
    public boolean deleteWrongQuestion(Long studentId, Long wrongQuestionId) {
        Optional<WrongQuestion> wrongQuestionOpt = wrongQuestionRepository.findById(wrongQuestionId);
        if (wrongQuestionOpt.isPresent()) {
            WrongQuestion wrongQuestion = wrongQuestionOpt.get();
            if (wrongQuestion.getStudentId().equals(studentId)) {
                wrongQuestionRepository.delete(wrongQuestion);
                return true;
            }
        }
        return false;
    }

    public Map<String, Object> getReviewSuggestions(Long studentId) {
        Map<String, Object> suggestions = new HashMap<>();
        
        // Get questions that need most review (highest retry count)
        List<WrongQuestion> needReview = getQuestionsForReview(studentId, 5);
        suggestions.put("priorityQuestions", needReview);
        
        // Get statistics by content type
        Map<String, Long> typeStats = new HashMap<>();
        typeStats.put("math", wrongQuestionRepository.countUnmasteredQuestions(studentId, "math_exercise"));
        typeStats.put("english", wrongQuestionRepository.countUnmasteredQuestions(studentId, "english_word"));
        typeStats.put("general", wrongQuestionRepository.countUnmasteredQuestions(studentId, "question"));
        suggestions.put("typeStatistics", typeStats);
        
        // Suggest focus area
        String focusArea = "math";
        Long maxCount = typeStats.get("math");
        if (typeStats.get("english") > maxCount) {
            focusArea = "english";
            maxCount = typeStats.get("english");
        }
        if (typeStats.get("general") > maxCount) {
            focusArea = "general";
        }
        suggestions.put("suggestedFocus", focusArea);
        
        return suggestions;
    }
}
