package com.example.elementarylearningcompanion.repository;

import com.example.elementarylearningcompanion.model.Question;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface QuestionRepository extends JpaRepository<Question, Long> {
    
    List<Question> findByTextbookVersionIdAndGradeAndSubject(Integer textbookVersionId, Integer grade, String subject);
    
    List<Question> findByTextbookVersionIdAndGradeAndSubjectAndQuestionType(
        Integer textbookVersionId, Integer grade, String subject, String questionType);
    
    List<Question> findByTextbookVersionIdAndGradeAndSubjectAndDifficultyLevel(
        Integer textbookVersionId, Integer grade, String subject, Integer difficultyLevel);
    
    @Query("SELECT q FROM Question q WHERE q.textbookVersionId = :textbookVersionId " +
           "AND q.grade = :grade AND q.subject = :subject " +
           "ORDER BY RAND() LIMIT :limit")
    List<Question> findRandomQuestions(@Param("textbookVersionId") Integer textbookVersionId,
                                      @Param("grade") Integer grade,
                                      @Param("subject") String subject,
                                      @Param("limit") Integer limit);
    
    @Query("SELECT q FROM Question q WHERE q.textbookVersionId = :textbookVersionId " +
           "AND q.grade = :grade AND q.subject = :subject " +
           "AND q.questionType = :questionType AND q.difficultyLevel = :difficultyLevel " +
           "ORDER BY RAND() LIMIT :limit")
    List<Question> findRandomQuestionsByTypeAndDifficulty(@Param("textbookVersionId") Integer textbookVersionId,
                                                         @Param("grade") Integer grade,
                                                         @Param("subject") String subject,
                                                         @Param("questionType") String questionType,
                                                         @Param("difficultyLevel") Integer difficultyLevel,
                                                         @Param("limit") Integer limit);
    
    @Query("SELECT DISTINCT q.questionType FROM Question q WHERE q.textbookVersionId = :textbookVersionId " +
           "AND q.grade = :grade AND q.subject = :subject")
    List<String> findDistinctQuestionTypes(@Param("textbookVersionId") Integer textbookVersionId,
                                          @Param("grade") Integer grade,
                                          @Param("subject") String subject);
    
    @Query("SELECT DISTINCT q.chapter FROM Question q WHERE q.textbookVersionId = :textbookVersionId " +
           "AND q.grade = :grade AND q.subject = :subject AND q.chapter IS NOT NULL")
    List<String> findDistinctChapters(@Param("textbookVersionId") Integer textbookVersionId,
                                     @Param("grade") Integer grade,
                                     @Param("subject") String subject);
}
