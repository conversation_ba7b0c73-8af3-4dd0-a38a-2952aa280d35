package com.example.elementarylearningcompanion.repository;

import com.example.elementarylearningcompanion.model.TestPaper;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TestPaperRepository extends JpaRepository<TestPaper, Long> {
    
    List<TestPaper> findByTextbookVersionIdAndGradeAndSubjectAndIsActiveTrue(
        Integer textbookVersionId, Integer grade, String subject);
    
    List<TestPaper> findByTextbookVersionIdAndGradeAndSubjectAndTestTypeAndIsActiveTrue(
        Integer textbookVersionId, Integer grade, String subject, String testType);
    
    @Query("SELECT tp FROM TestPaper tp WHERE tp.textbookVersionId = :textbookVersionId " +
           "AND tp.grade = :grade AND tp.isActive = true ORDER BY tp.createdAt DESC")
    List<TestPaper> findActiveTestPapers(@Param("textbookVersionId") Integer textbookVersionId,
                                        @Param("grade") Integer grade);
    
    @Query("SELECT DISTINCT tp.testType FROM TestPaper tp WHERE tp.textbookVersionId = :textbookVersionId " +
           "AND tp.grade = :grade AND tp.subject = :subject AND tp.isActive = true")
    List<String> findDistinctTestTypes(@Param("textbookVersionId") Integer textbookVersionId,
                                      @Param("grade") Integer grade,
                                      @Param("subject") String subject);
}
