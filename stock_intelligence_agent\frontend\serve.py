#!/usr/bin/env python3
"""
简单的HTTP服务器，用于托管前端文件
解决file://协议访问API的跨域问题
"""

import http.server
import socketserver
import os
import webbrowser
from pathlib import Path

# 设置端口
PORT = 3000

# 获取当前目录
current_dir = Path(__file__).parent

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=str(current_dir), **kwargs)
    
    def end_headers(self):
        # 添加CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()

def main():
    """启动HTTP服务器"""
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"🌐 前端服务器启动成功!")
            print(f"📍 服务地址: http://localhost:{PORT}")
            print(f"🧪 测试工具: http://localhost:{PORT}/professional-test-ui.html")
            print(f"📊 简单测试: http://localhost:{PORT}/detailed-test.html")
            print(f"🔍 按 Ctrl+C 停止服务")
            print()
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{PORT}/professional-test-ui.html')
                print("✅ 已自动打开专业测试工具页面")
            except:
                print("⚠️ 无法自动打开浏览器，请手动访问上述地址")
            
            print()
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except OSError as e:
        if e.errno == 10048:  # Windows: Address already in use
            print(f"❌ 端口 {PORT} 已被占用，请检查是否有其他服务在运行")
            print("💡 您可以修改 serve.py 中的 PORT 变量来使用其他端口")
        else:
            print(f"❌ 启动服务器失败: {e}")

if __name__ == "__main__":
    main()
