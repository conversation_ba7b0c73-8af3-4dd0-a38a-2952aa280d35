@echo off
chcp 65001 >nul
title 股票智能体 - 一键启动

echo.
echo ========================================
echo 🚀 股票智能体 - 一键启动脚本
echo ========================================
echo.

echo 📋 启动步骤:
echo 1. 启动后端API服务 (端口8000)
echo 2. 启动前端测试工具 (端口3000)
echo 3. 自动打开专业测试工具页面
echo.

echo 🔧 正在启动后端服务...
cd /d "%~dp0backend"
start "后端API服务" cmd /k "python simple_test.py"

echo ⏳ 等待后端服务启动...
timeout /t 3 /nobreak >nul

echo 🌐 正在启动前端服务...
cd /d "%~dp0frontend"
start "前端测试工具" cmd /k "python serve.py"

echo ⏳ 等待前端服务启动...
timeout /t 2 /nobreak >nul

echo.
echo ✅ 启动完成！
echo.
echo 📍 服务地址:
echo    后端API: http://localhost:8000
echo    前端工具: http://localhost:3000
echo    专业测试: http://localhost:3000/professional-test-ui.html
echo.
echo 💡 使用说明:
echo    1. 等待两个服务完全启动（约5-10秒）
echo    2. 浏览器会自动打开专业测试工具
echo    3. 检查页面顶部状态指示灯是否为绿色
echo    4. 开始进行功能测试和反馈收集
echo.
echo 🛑 停止服务:
echo    关闭对应的命令行窗口即可停止服务
echo.

pause
