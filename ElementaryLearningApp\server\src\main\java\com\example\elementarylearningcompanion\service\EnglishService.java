package com.example.elementarylearningcompanion.service;

import com.example.elementarylearningcompanion.model.EnglishWord;
import com.example.elementarylearningcompanion.model.WordLearningRecord;
import com.example.elementarylearningcompanion.model.WrongQuestion;
import com.example.elementarylearningcompanion.repository.EnglishWordRepository;
import com.example.elementarylearningcompanion.repository.WordLearningRecordRepository;
import com.example.elementarylearningcompanion.repository.WrongQuestionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.ArrayList;
import java.util.Collections;

@Service
public class EnglishService {

    @Autowired
    private EnglishWordRepository englishWordRepository;

    @Autowired
    private WordLearningRecordRepository wordLearningRecordRepository;

    @Autowired
    private WrongQuestionRepository wrongQuestionRepository;

    private final Random random = new Random();

    public List<EnglishWord> getWordsByGrade(Integer textbookVersionId, Integer grade) {
        return englishWordRepository.findByTextbookVersionIdAndGrade(textbookVersionId, grade);
    }

    public List<EnglishWord> getWordsByUnit(Integer textbookVersionId, Integer grade, String unit) {
        return englishWordRepository.findByTextbookVersionIdAndGradeAndUnit(textbookVersionId, grade, unit);
    }

    public List<String> getAvailableUnits(Integer textbookVersionId, Integer grade) {
        return englishWordRepository.findDistinctUnits(textbookVersionId, grade);
    }

    public List<EnglishWord> getRandomWords(Integer textbookVersionId, Integer grade, Integer count) {
        return englishWordRepository.findRandomWords(textbookVersionId, grade, count);
    }

    public List<EnglishWord> getRandomWordsByDifficulty(Integer textbookVersionId, Integer grade, 
                                                       Integer difficultyLevel, Integer count) {
        return englishWordRepository.findRandomWordsByDifficulty(textbookVersionId, grade, difficultyLevel, count);
    }

    @Transactional
    public void recordWordLearning(Long studentId, Long wordId, String learningType, Boolean isCorrect) {
        Optional<WordLearningRecord> existingRecord = wordLearningRecordRepository
            .findByStudentIdAndWordIdAndLearningType(studentId, wordId, learningType);

        WordLearningRecord record;
        if (existingRecord.isPresent()) {
            record = existingRecord.get();
            record.setAttempts(record.getAttempts() + 1);
        } else {
            record = new WordLearningRecord(studentId, wordId, learningType);
        }

        if (isCorrect != null) {
            record.setIsCorrect(isCorrect);
            updateMasteryLevel(record, isCorrect);
        }

        wordLearningRecordRepository.save(record);

        // Handle wrong answers for spelling/test activities
        if (isCorrect != null && !isCorrect && ("spell".equals(learningType) || "test".equals(learningType))) {
            handleWrongWord(studentId, wordId);
        } else if (isCorrect != null && isCorrect) {
            markWordAsMastered(studentId, wordId);
        }
    }

    private void updateMasteryLevel(WordLearningRecord record, Boolean isCorrect) {
        int currentLevel = record.getMasteryLevel();
        
        if (isCorrect) {
            // Increase mastery level
            int increase = Math.max(10, 100 - currentLevel) / 5;
            record.setMasteryLevel(Math.min(100, currentLevel + increase));
        } else {
            // Decrease mastery level
            int decrease = Math.max(5, currentLevel / 10);
            record.setMasteryLevel(Math.max(0, currentLevel - decrease));
        }
    }

    private void handleWrongWord(Long studentId, Long wordId) {
        Optional<WrongQuestion> existingWrong = wrongQuestionRepository
            .findByStudentIdAndContentTypeAndContentId(studentId, "english_word", wordId);

        if (existingWrong.isPresent()) {
            WrongQuestion wrongQuestion = existingWrong.get();
            wrongQuestion.setRetryCount(wrongQuestion.getRetryCount() + 1);
            wrongQuestion.setIsMastered(false);
            wrongQuestionRepository.save(wrongQuestion);
        } else {
            WrongQuestion wrongQuestion = new WrongQuestion(studentId, "english_word", wordId, "");
            wrongQuestionRepository.save(wrongQuestion);
        }
    }

    private void markWordAsMastered(Long studentId, Long wordId) {
        Optional<WrongQuestion> wrongQuestionOpt = wrongQuestionRepository
            .findByStudentIdAndContentTypeAndContentId(studentId, "english_word", wordId);

        if (wrongQuestionOpt.isPresent()) {
            WrongQuestion wrongQuestion = wrongQuestionOpt.get();
            wrongQuestion.setIsMastered(true);
            wrongQuestionRepository.save(wrongQuestion);
        }
    }

    public List<WrongQuestion> getWrongWords(Long studentId) {
        return wrongQuestionRepository.findByStudentIdAndContentTypeAndIsMasteredFalse(studentId, "english_word");
    }

    public Double getStudentMasteryLevel(Long studentId) {
        return wordLearningRecordRepository.getAverageMasteryLevel(studentId);
    }

    public List<WordLearningRecord> getWordsNeedingReview(Long studentId, Integer threshold) {
        return wordLearningRecordRepository.findWordsNeedingReview(studentId, threshold);
    }

    // Generate spelling test questions
    public List<EnglishWord> generateSpellingTest(Integer textbookVersionId, Integer grade, Integer count) {
        List<EnglishWord> allWords = englishWordRepository.findByTextbookVersionIdAndGrade(textbookVersionId, grade);
        Collections.shuffle(allWords);
        return allWords.subList(0, Math.min(count, allWords.size()));
    }

    // Generate vocabulary quiz with multiple choice options
    public List<VocabularyQuiz> generateVocabularyQuiz(Integer textbookVersionId, Integer grade, Integer count) {
        List<EnglishWord> words = getRandomWords(textbookVersionId, grade, count);
        List<VocabularyQuiz> quizzes = new ArrayList<>();

        for (EnglishWord word : words) {
            VocabularyQuiz quiz = new VocabularyQuiz();
            quiz.setWord(word);
            quiz.setCorrectAnswer(word.getMeaningChinese());
            
            // Generate wrong options
            List<String> wrongOptions = generateWrongOptions(textbookVersionId, grade, word.getMeaningChinese());
            quiz.setOptions(createOptionsWithCorrectAnswer(word.getMeaningChinese(), wrongOptions));
            
            quizzes.add(quiz);
        }

        return quizzes;
    }

    private List<String> generateWrongOptions(Integer textbookVersionId, Integer grade, String correctAnswer) {
        List<EnglishWord> randomWords = getRandomWords(textbookVersionId, grade, 10);
        List<String> wrongOptions = new ArrayList<>();
        
        for (EnglishWord word : randomWords) {
            if (!word.getMeaningChinese().equals(correctAnswer) && wrongOptions.size() < 3) {
                wrongOptions.add(word.getMeaningChinese());
            }
        }
        
        // Fill with generic wrong answers if needed
        while (wrongOptions.size() < 3) {
            wrongOptions.add("选项" + (wrongOptions.size() + 1));
        }
        
        return wrongOptions;
    }

    private List<String> createOptionsWithCorrectAnswer(String correctAnswer, List<String> wrongOptions) {
        List<String> options = new ArrayList<>(wrongOptions);
        options.add(correctAnswer);
        Collections.shuffle(options);
        return options;
    }

    // Inner class for vocabulary quiz
    public static class VocabularyQuiz {
        private EnglishWord word;
        private String correctAnswer;
        private List<String> options;

        // Getters and setters
        public EnglishWord getWord() { return word; }
        public void setWord(EnglishWord word) { this.word = word; }
        public String getCorrectAnswer() { return correctAnswer; }
        public void setCorrectAnswer(String correctAnswer) { this.correctAnswer = correctAnswer; }
        public List<String> getOptions() { return options; }
        public void setOptions(List<String> options) { this.options = options; }
    }
}
