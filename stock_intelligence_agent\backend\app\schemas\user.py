"""
用户相关的 Pydantic 模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, EmailStr, validator
from uuid import UUID


class UserBase(BaseModel):
    """用户基础模型"""
    username: str = Field(..., description="用户名", min_length=3, max_length=50)
    email: EmailStr = Field(..., description="邮箱地址")
    full_name: Optional[str] = Field(None, description="全名", max_length=100)
    is_active: bool = Field(True, description="是否活跃")


class UserCreate(UserBase):
    """创建用户模型"""
    password: str = Field(..., description="密码", min_length=8, max_length=100)
    confirm_password: str = Field(..., description="确认密码")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'password' in values and v != values['password']:
            raise ValueError('密码不匹配')
        return v


class UserUpdate(BaseModel):
    """更新用户模型"""
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    full_name: Optional[str] = Field(None, description="全名", max_length=100)
    is_active: Optional[bool] = Field(None, description="是否活跃")


class UserResponse(UserBase):
    """用户响应模型"""
    id: UUID = Field(..., description="用户ID")
    is_superuser: bool = Field(False, description="是否超级用户")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    last_login: Optional[datetime] = Field(None, description="最后登录时间")

    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    """用户登录模型"""
    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")
    remember_me: bool = Field(False, description="记住我")


class UserLoginResponse(BaseModel):
    """用户登录响应模型"""
    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field("bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间(秒)")
    user: UserResponse = Field(..., description="用户信息")


class TokenRefresh(BaseModel):
    """令牌刷新模型"""
    refresh_token: str = Field(..., description="刷新令牌")


class PasswordChange(BaseModel):
    """密码修改模型"""
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., description="新密码", min_length=8, max_length=100)
    confirm_password: str = Field(..., description="确认新密码")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('新密码不匹配')
        return v


class PasswordReset(BaseModel):
    """密码重置模型"""
    email: EmailStr = Field(..., description="邮箱地址")


class PasswordResetConfirm(BaseModel):
    """密码重置确认模型"""
    token: str = Field(..., description="重置令牌")
    new_password: str = Field(..., description="新密码", min_length=8, max_length=100)
    confirm_password: str = Field(..., description="确认新密码")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('新密码不匹配')
        return v


class UserProfileBase(BaseModel):
    """用户档案基础模型"""
    avatar_url: Optional[str] = Field(None, description="头像URL")
    bio: Optional[str] = Field(None, description="个人简介", max_length=500)
    location: Optional[str] = Field(None, description="所在地", max_length=100)
    website: Optional[str] = Field(None, description="个人网站")
    phone: Optional[str] = Field(None, description="电话号码")
    timezone: str = Field("Asia/Shanghai", description="时区")
    language: str = Field("zh-CN", description="语言偏好")
    theme: str = Field("light", description="主题偏好")


class UserProfileUpdate(UserProfileBase):
    """更新用户档案模型"""
    pass


class UserProfileResponse(UserProfileBase):
    """用户档案响应模型"""
    id: UUID = Field(..., description="档案ID")
    user_id: UUID = Field(..., description="用户ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class AlertBase(BaseModel):
    """预警基础模型"""
    name: str = Field(..., description="预警名称", max_length=100)
    stock_code: str = Field(..., description="股票代码")
    alert_type: str = Field(..., description="预警类型")
    condition: Dict[str, Any] = Field(..., description="预警条件")
    is_active: bool = Field(True, description="是否启用")


class AlertCreate(AlertBase):
    """创建预警模型"""
    notification_methods: List[str] = Field(default_factory=list, description="通知方式")


class AlertUpdate(BaseModel):
    """更新预警模型"""
    name: Optional[str] = Field(None, description="预警名称", max_length=100)
    condition: Optional[Dict[str, Any]] = Field(None, description="预警条件")
    is_active: Optional[bool] = Field(None, description="是否启用")
    notification_methods: Optional[List[str]] = Field(None, description="通知方式")


class AlertResponse(AlertBase):
    """预警响应模型"""
    id: UUID = Field(..., description="预警ID")
    user_id: UUID = Field(..., description="用户ID")
    trigger_count: int = Field(0, description="触发次数")
    last_triggered: Optional[datetime] = Field(None, description="最后触发时间")
    notification_methods: List[str] = Field(default_factory=list, description="通知方式")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class AlertTrigger(BaseModel):
    """预警触发模型"""
    alert_id: UUID = Field(..., description="预警ID")
    trigger_value: float = Field(..., description="触发值")
    trigger_time: datetime = Field(..., description="触发时间")
    message: str = Field(..., description="触发消息")
    is_read: bool = Field(False, description="是否已读")


class AlertTriggerResponse(AlertTrigger):
    """预警触发响应模型"""
    id: UUID = Field(..., description="触发记录ID")
    alert: AlertResponse = Field(..., description="预警信息")

    class Config:
        from_attributes = True


class WatchlistBase(BaseModel):
    """自选股基础模型"""
    name: str = Field(..., description="自选股名称", max_length=100)
    description: Optional[str] = Field(None, description="描述", max_length=500)
    is_public: bool = Field(False, description="是否公开")


class WatchlistCreate(WatchlistBase):
    """创建自选股模型"""
    stock_codes: List[str] = Field(default_factory=list, description="股票代码列表")


class WatchlistUpdate(BaseModel):
    """更新自选股模型"""
    name: Optional[str] = Field(None, description="自选股名称", max_length=100)
    description: Optional[str] = Field(None, description="描述", max_length=500)
    is_public: Optional[bool] = Field(None, description="是否公开")


class WatchlistItemBase(BaseModel):
    """自选股项目基础模型"""
    stock_code: str = Field(..., description="股票代码")
    notes: Optional[str] = Field(None, description="备注", max_length=500)
    sort_order: int = Field(0, description="排序顺序")


class WatchlistItemCreate(WatchlistItemBase):
    """创建自选股项目模型"""
    watchlist_id: UUID = Field(..., description="自选股ID")


class WatchlistItemResponse(WatchlistItemBase):
    """自选股项目响应模型"""
    id: UUID = Field(..., description="项目ID")
    watchlist_id: UUID = Field(..., description="自选股ID")
    stock_name: Optional[str] = Field(None, description="股票名称")
    current_price: Optional[float] = Field(None, description="当前价格")
    change: Optional[float] = Field(None, description="涨跌额")
    pct_change: Optional[float] = Field(None, description="涨跌幅")
    added_at: datetime = Field(..., description="添加时间")

    class Config:
        from_attributes = True


class WatchlistResponse(WatchlistBase):
    """自选股响应模型"""
    id: UUID = Field(..., description="自选股ID")
    user_id: UUID = Field(..., description="用户ID")
    stock_count: int = Field(0, description="股票数量")
    items: List[WatchlistItemResponse] = Field(default_factory=list, description="股票列表")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class UserPreferences(BaseModel):
    """用户偏好设置模型"""
    default_watchlist: Optional[UUID] = Field(None, description="默认自选股")
    notification_settings: Dict[str, bool] = Field(default_factory=dict, description="通知设置")
    display_settings: Dict[str, Any] = Field(default_factory=dict, description="显示设置")
    trading_settings: Dict[str, Any] = Field(default_factory=dict, description="交易设置")
    privacy_settings: Dict[str, bool] = Field(default_factory=dict, description="隐私设置")


class UserPreferencesResponse(UserPreferences):
    """用户偏好设置响应模型"""
    user_id: UUID = Field(..., description="用户ID")
    updated_at: datetime = Field(..., description="更新时间")


class UserActivity(BaseModel):
    """用户活动模型"""
    activity_type: str = Field(..., description="活动类型")
    description: str = Field(..., description="活动描述")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")
    ip_address: Optional[str] = Field(None, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")
    created_at: datetime = Field(..., description="创建时间")


class UserActivityResponse(UserActivity):
    """用户活动响应模型"""
    id: UUID = Field(..., description="活动ID")
    user_id: UUID = Field(..., description="用户ID")

    class Config:
        from_attributes = True


class UserStatistics(BaseModel):
    """用户统计模型"""
    total_users: int = Field(..., description="总用户数")
    active_users: int = Field(..., description="活跃用户数")
    new_users_today: int = Field(..., description="今日新用户")
    new_users_week: int = Field(..., description="本周新用户")
    new_users_month: int = Field(..., description="本月新用户")
    user_growth_rate: float = Field(..., description="用户增长率")
    retention_rate: float = Field(..., description="用户留存率")
    avg_session_duration: float = Field(..., description="平均会话时长")
    most_active_hours: List[int] = Field(..., description="最活跃时段")
    platform_distribution: Dict[str, int] = Field(..., description="平台分布")
    last_updated: datetime = Field(..., description="最后更新时间")


class UserListResponse(BaseModel):
    """用户列表响应模型"""
    users: List[UserResponse] = Field(..., description="用户列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class UserSearchRequest(BaseModel):
    """用户搜索请求模型"""
    query: Optional[str] = Field(None, description="搜索关键词")
    is_active: Optional[bool] = Field(None, description="是否活跃")
    is_superuser: Optional[bool] = Field(None, description="是否超级用户")
    created_after: Optional[datetime] = Field(None, description="创建时间之后")
    created_before: Optional[datetime] = Field(None, description="创建时间之前")
    sort_by: str = Field("created_at", description="排序字段")
    sort_order: str = Field("desc", description="排序方向")
    skip: int = Field(0, description="跳过数量", ge=0)
    limit: int = Field(20, description="限制数量", ge=1, le=100)
