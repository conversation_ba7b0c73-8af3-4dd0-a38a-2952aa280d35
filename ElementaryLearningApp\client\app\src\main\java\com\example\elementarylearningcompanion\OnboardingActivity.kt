package com.example.elementarylearningcompanion

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.elementarylearningcompanion.dto.Lesson
import com.example.elementarylearningcompanion.dto.TextbookVersion
import com.example.elementarylearningcompanion.ui.theme.ElementaryLearningCompanionTheme
import com.example.elementarylearningcompanion.viewmodel.ContentViewModel

class OnboardingActivity : ComponentActivity() {
    private val viewModel: ContentViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ElementaryLearningCompanionTheme {
                Surface(modifier = Modifier.fillMaxSize(), color = MaterialTheme.colorScheme.background) {
                    OnboardingScreen(viewModel)
                }
            }
        }
    }
}

@Composable
fun OnboardingScreen(viewModel: ContentViewModel) {
    val context = LocalContext.current
    var selectedTextbookId by remember { mutableStateOf<Int?>(null) }
    var selectedGrade by remember { mutableStateOf(1) } // Default to 1st grade

    val textbooks by viewModel.textbooks.observeAsState(emptyList())
    val lessons by viewModel.lessons.observeAsState(emptyList())
    val isLoading by viewModel.isLoading.observeAsState(false)
    val error by viewModel.error.observeAsState()

    // Fetch textbooks when the screen is first composed
    LaunchedEffect(Unit) {
        viewModel.fetchTextbooks("语文") // Fetch Chinese textbooks by default
    }
    
    // Fetch lessons when selection changes
    LaunchedEffect(selectedTextbookId, selectedGrade) {
        selectedTextbookId?.let {
            viewModel.fetchLessons(it, selectedGrade)
        }
    }

    // Show error message
    LaunchedEffect(error) {
        error?.let { Toast.makeText(context, it, Toast.LENGTH_LONG).show() }
    }

    Column(
        modifier = Modifier.fillMaxSize().padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 顶部返回按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            TextButton(onClick = { (context as? ComponentActivity)?.finish() }) {
                Text("← 返回")
            }
            TextButton(
                onClick = {
                    val intent = Intent(context, HomeActivity::class.java)
                    context.startActivity(intent)
                    (context as? ComponentActivity)?.finish()
                }
            ) {
                Text("跳过 →")
            }
        }

        Text("请选择你的教材和年级", style = MaterialTheme.typography.headlineMedium)
        Text("(可以跳过，直接进入主页)", style = MaterialTheme.typography.bodyMedium,
             color = MaterialTheme.colorScheme.onSurfaceVariant)

        Spacer(modifier = Modifier.height(16.dp))

        // Grade Selection
        Text("选择年级", style = MaterialTheme.typography.titleMedium)
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            for (grade in 1..6) {
                FilterChip(
                    onClick = { selectedGrade = grade },
                    label = { Text("${grade}年级") },
                    selected = selectedGrade == grade,
                    modifier = Modifier.weight(1f)
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Textbooks List
        Text("教材列表", style = MaterialTheme.typography.titleMedium)
        LazyColumn(modifier = Modifier.fillMaxWidth().height(150.dp)) {
            items(textbooks) { textbook ->
                TextbookItem(
                    textbook = textbook,
                    isSelected = selectedTextbookId == textbook.getId(),
                    onClick = { selectedTextbookId = it.getId() }
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Navigation Button - only enabled when textbook is selected
        Button(
            onClick = {
                val intent = Intent(context, HomeActivity::class.java)
                context.startActivity(intent)
                (context as? ComponentActivity)?.finish()
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedTextbookId != null
        ) {
            Text(if (selectedTextbookId != null) "进入学习主页" else "请先选择教材")
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Lessons List - only show when textbook is selected
        if (selectedTextbookId != null) {
            Text("课文列表", style = MaterialTheme.typography.titleMedium)
            if (isLoading) {
                CircularProgressIndicator()
            } else if (lessons.isEmpty()) {
                Card(
                    modifier = Modifier.fillMaxWidth().padding(vertical = 8.dp)
                ) {
                    Text(
                        text = "暂无课文数据，点击上方按钮进入主页",
                        modifier = Modifier.padding(16.dp),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                LazyColumn(modifier = Modifier.fillMaxWidth().height(200.dp)) {
                    items(lessons) { lesson ->
                        LessonItem(lesson = lesson, onClick = {
                            val intent = Intent(context, LessonActivity::class.java)
                            intent.putExtra("LESSON_ID", it.getId())
                            context.startActivity(intent)
                        })
                    }
                }
            }
        } else {
            Card(
                modifier = Modifier.fillMaxWidth().padding(vertical = 8.dp)
            ) {
                Text(
                    text = "请先选择教材以查看课文列表",
                    modifier = Modifier.padding(16.dp),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
fun TextbookItem(
    textbook: TextbookVersion,
    isSelected: Boolean = false,
    onClick: (TextbookVersion) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clickable { onClick(textbook) },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected)
                MaterialTheme.colorScheme.primaryContainer
            else
                MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "${textbook.getName()} - ${textbook.getSubject()}",
                color = if (isSelected)
                    MaterialTheme.colorScheme.onPrimaryContainer
                else
                    MaterialTheme.colorScheme.onSurface
            )
            if (isSelected) {
                Text(
                    text = "✓",
                    color = MaterialTheme.colorScheme.primary,
                    style = MaterialTheme.typography.titleMedium
                )
            }
        }
    }
}

@Composable
fun LessonItem(lesson: Lesson, onClick: (Lesson) -> Unit) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clickable { onClick(lesson) },
    ) {
        Text(
            text = "第${lesson.getLessonNumber()}课: ${lesson.getTitle()}",
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun OnboardingScreenPreview() {
    ElementaryLearningCompanionTheme {
        Surface(modifier = Modifier.fillMaxSize()) {
            Text("Onboarding Screen Preview")
        }
    }
}
