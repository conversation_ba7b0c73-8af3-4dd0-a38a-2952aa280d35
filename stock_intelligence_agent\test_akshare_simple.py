#!/usr/bin/env python3
"""
简单测试AKShare研报接口
"""

def test_akshare_research():
    """测试AKShare研报接口"""
    try:
        import akshare as ak
        print("✅ AKShare导入成功")
        
        # 测试研报接口
        print("🧪 测试研报接口...")
        
        # 检查接口是否存在
        if hasattr(ak, 'stock_research_report_em'):
            print("✅ stock_research_report_em 接口存在")
            
            # 尝试获取数据
            try:
                df = ak.stock_research_report_em(symbol="300750")
                print(f"✅ 成功获取数据，共 {len(df)} 条记录")
                
                if len(df) > 0:
                    print("📋 数据列名:", list(df.columns))
                    print("📄 前3条数据:")
                    for i, (_, row) in enumerate(df.head(3).iterrows()):
                        print(f"  {i+1}. {dict(row)}")
                else:
                    print("⚠️ 返回数据为空")
                    
            except Exception as e:
                print(f"❌ 获取数据失败: {e}")
                
        else:
            print("❌ stock_research_report_em 接口不存在")
            
            # 列出所有包含research的接口
            research_funcs = [name for name in dir(ak) if 'research' in name.lower()]
            print("🔍 包含research的接口:", research_funcs)
            
    except ImportError as e:
        print(f"❌ AKShare导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    print("📊 AKShare研报接口简单测试")
    print("=" * 40)
    test_akshare_research()
    print("\n🎉 测试完成！")
