# study - 项目规则

> 自动生成于: 2025-06-17 11:35:25

## 📋 项目信息

- **项目名称**: study
- **项目描述**: 项目规则和约定
- **项目类型**: general

## 💻 编码标准

### 语言偏好

#### Python
- **Style Guide**: PEP 8
- **Formatter**: black
- **Linter**: ruff
- **Type Checker**: mypy
- **Docstring Style**: Google

#### Javascript
- **Style Guide**: Airbnb
- **Formatter**: prettier
- **Linter**: eslint

### 通用原则

- 遵循单一职责原则
- 使用有意义的变量和函数名
- 编写清晰的注释和文档
- 优先使用组合而不是继承
- 实现适当的错误处理


## 🔄 开发工作流

### Git 约定
- **提交格式**: type(scope): description
- **分支命名**: feature/description, fix/description, hotfix/description
- **合并策略**: squash

### 测试要求
- **单元测试**: required
- **覆盖率阈值**: 80%
- **测试框架**: pytest

## 🤖 AI 助手规则

### 交互风格
- **风格**: professional_friendly
- **反馈频率**: after_major_changes
- **解释级别**: detailed

### 代码生成要求
- **包含注释**: True
- **包含类型提示**: True
- **包含文档字符串**: True
- **遵循项目结构**: True

### MCP Interactive Feedback Rules (严格执行)
1. During any process, task, or conversation, whether asking, replying, or completing phased tasks, you must call MCP mcp-feedback-enhanced.
2. Whenever user feedback is received, if the feedback content is not empty, you must call MCP mcp-feedback-enhanced again and adjust behavior based on the feedback content.
3. Only when the user explicitly indicates 'end' or 'no more interaction needed' can you stop calling MCP mcp-feedback-enhanced, and the process is considered complete.
4. Unless receiving termination instructions, all steps must repeatedly call MCP mcp-feedback-enhanced.
5. Before completing tasks, you must use the MCP mcp-feedback-enhanced tool to ask users for feedback.

- **执行级别**: strict
- **例外情况**: Simple informational queries, Version checks, Basic status reports

### MCP Feedback Enhanced 配置
- **自动批准**: ['interactive_feedback']
- **使用 Web UI**: True
- **启用图片支持**: True

## 📁 项目结构

- **源码目录**: src, lib
- **测试目录**: tests, test
- **文档目录**: docs, documentation

## 📦 依赖管理

- **包管理器**: uv
- **更新策略**: conservative
- **安全扫描**: True

---

*此文档由 Augment Rules System 自动生成*
