package com.example.elementarylearningcompanion.service;

import com.example.elementarylearningcompanion.model.MathExercise;
import com.example.elementarylearningcompanion.model.LearningRecord;
import com.example.elementarylearningcompanion.model.WrongQuestion;
import com.example.elementarylearningcompanion.repository.MathExerciseRepository;
import com.example.elementarylearningcompanion.repository.LearningRecordRepository;
import com.example.elementarylearningcompanion.repository.WrongQuestionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.ArrayList;

@Service
public class MathService {

    @Autowired
    private MathExerciseRepository mathExerciseRepository;

    @Autowired
    private LearningRecordRepository learningRecordRepository;

    @Autowired
    private WrongQuestionRepository wrongQuestionRepository;

    private final Random random = new Random();

    public List<MathExercise> getExercisesByGradeAndType(Integer textbookVersionId, Integer grade, String exerciseType) {
        return mathExerciseRepository.findByTextbookVersionIdAndGradeAndExerciseType(textbookVersionId, grade, exerciseType);
    }

    public List<MathExercise> getRandomExercises(Integer textbookVersionId, Integer grade, String exerciseType, Integer difficultyLevel, Integer count) {
        return mathExerciseRepository.findRandomExercises(textbookVersionId, grade, exerciseType, difficultyLevel, count);
    }

    public List<String> getAvailableExerciseTypes(Integer textbookVersionId, Integer grade) {
        return mathExerciseRepository.findDistinctExerciseTypes(textbookVersionId, grade);
    }

    @Transactional
    public boolean submitAnswer(Long studentId, Long exerciseId, String answer, Integer timeSpent) {
        Optional<MathExercise> exerciseOpt = mathExerciseRepository.findById(exerciseId);
        if (exerciseOpt.isEmpty()) {
            return false;
        }

        MathExercise exercise = exerciseOpt.get();
        boolean isCorrect = exercise.getCorrectAnswer().equals(answer.trim());

        // Record learning activity
        LearningRecord record = new LearningRecord(studentId, "math_exercise", exerciseId, "practice");
        record.setIsCorrect(isCorrect);
        record.setTimeSpent(timeSpent);
        learningRecordRepository.save(record);

        // Handle wrong answers
        if (!isCorrect) {
            handleWrongAnswer(studentId, exerciseId, answer);
        } else {
            // Mark as mastered if answered correctly
            markQuestionAsMastered(studentId, exerciseId);
        }

        return isCorrect;
    }

    private void handleWrongAnswer(Long studentId, Long exerciseId, String wrongAnswer) {
        Optional<WrongQuestion> existingWrong = wrongQuestionRepository
            .findByStudentIdAndContentTypeAndContentId(studentId, "math_exercise", exerciseId);

        if (existingWrong.isPresent()) {
            WrongQuestion wrongQuestion = existingWrong.get();
            wrongQuestion.setRetryCount(wrongQuestion.getRetryCount() + 1);
            wrongQuestion.setWrongAnswer(wrongAnswer);
            wrongQuestionRepository.save(wrongQuestion);
        } else {
            WrongQuestion wrongQuestion = new WrongQuestion(studentId, "math_exercise", exerciseId, wrongAnswer);
            wrongQuestionRepository.save(wrongQuestion);
        }
    }

    private void markQuestionAsMastered(Long studentId, Long exerciseId) {
        Optional<WrongQuestion> wrongQuestionOpt = wrongQuestionRepository
            .findByStudentIdAndContentTypeAndContentId(studentId, "math_exercise", exerciseId);

        if (wrongQuestionOpt.isPresent()) {
            WrongQuestion wrongQuestion = wrongQuestionOpt.get();
            wrongQuestion.setIsMastered(true);
            wrongQuestionRepository.save(wrongQuestion);
        }
    }

    public List<WrongQuestion> getWrongQuestions(Long studentId) {
        return wrongQuestionRepository.findByStudentIdAndContentTypeAndIsMasteredFalse(studentId, "math_exercise");
    }

    // Generate arithmetic exercises dynamically
    public List<MathExercise> generateArithmeticExercises(String exerciseType, Integer difficultyLevel, Integer count) {
        List<MathExercise> exercises = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            MathExercise exercise = generateSingleExercise(exerciseType, difficultyLevel);
            exercises.add(exercise);
        }
        
        return exercises;
    }

    private MathExercise generateSingleExercise(String exerciseType, Integer difficultyLevel) {
        MathExercise exercise = new MathExercise();
        exercise.setExerciseType(exerciseType);
        exercise.setDifficultyLevel(difficultyLevel);

        int maxNumber = getMaxNumberForDifficulty(difficultyLevel);
        
        switch (exerciseType.toLowerCase()) {
            case "addition":
                generateAdditionExercise(exercise, maxNumber);
                break;
            case "subtraction":
                generateSubtractionExercise(exercise, maxNumber);
                break;
            case "multiplication":
                generateMultiplicationExercise(exercise, maxNumber);
                break;
            case "division":
                generateDivisionExercise(exercise, maxNumber);
                break;
            default:
                generateAdditionExercise(exercise, maxNumber);
        }

        return exercise;
    }

    private int getMaxNumberForDifficulty(Integer difficultyLevel) {
        switch (difficultyLevel) {
            case 1: return 10;
            case 2: return 20;
            case 3: return 50;
            case 4: return 100;
            case 5: return 1000;
            default: return 10;
        }
    }

    private void generateAdditionExercise(MathExercise exercise, int maxNumber) {
        int a = random.nextInt(maxNumber) + 1;
        int b = random.nextInt(maxNumber) + 1;
        int result = a + b;
        
        exercise.setQuestion(a + " + " + b + " = ?");
        exercise.setCorrectAnswer(String.valueOf(result));
        exercise.setExplanation(a + " + " + b + " = " + result);
    }

    private void generateSubtractionExercise(MathExercise exercise, int maxNumber) {
        int a = random.nextInt(maxNumber) + 1;
        int b = random.nextInt(a) + 1; // Ensure positive result
        int result = a - b;
        
        exercise.setQuestion(a + " - " + b + " = ?");
        exercise.setCorrectAnswer(String.valueOf(result));
        exercise.setExplanation(a + " - " + b + " = " + result);
    }

    private void generateMultiplicationExercise(MathExercise exercise, int maxNumber) {
        int a = random.nextInt(Math.min(maxNumber, 12)) + 1;
        int b = random.nextInt(Math.min(maxNumber, 12)) + 1;
        int result = a * b;
        
        exercise.setQuestion(a + " × " + b + " = ?");
        exercise.setCorrectAnswer(String.valueOf(result));
        exercise.setExplanation(a + " × " + b + " = " + result);
    }

    private void generateDivisionExercise(MathExercise exercise, int maxNumber) {
        int b = random.nextInt(Math.min(maxNumber, 12)) + 1;
        int result = random.nextInt(Math.min(maxNumber, 12)) + 1;
        int a = b * result; // Ensure exact division
        
        exercise.setQuestion(a + " ÷ " + b + " = ?");
        exercise.setCorrectAnswer(String.valueOf(result));
        exercise.setExplanation(a + " ÷ " + b + " = " + result);
    }
}
