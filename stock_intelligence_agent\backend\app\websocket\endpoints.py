"""
WebSocket API 端点
"""

import json
import logging
from typing import Optional, Dict, Any
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query, HTTPException
from fastapi.responses import J<PERSON>NResponse

from app.websocket.connection_manager import connection_manager
from app.websocket.message_handler import get_message_handler
from app.websocket.data_pusher import get_data_pusher
from app.core.database import get_redis

logger = logging.getLogger(__name__)

router = APIRouter()

# 获取全局实例
message_handler = get_message_handler(connection_manager)
data_pusher = get_data_pusher(connection_manager)


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    user_id: Optional[str] = Query(None, description="用户ID"),
    client_type: Optional[str] = Query("web", description="客户端类型"),
    version: Optional[str] = Query("1.0", description="客户端版本")
):
    """
    WebSocket 主端点
    
    支持的消息类型:
    - ping: 心跳检测
    - subscribe_stock: 订阅股票实时数据
    - unsubscribe_stock: 取消订阅股票
    - subscribe_news: 订阅新闻推送
    - unsubscribe_news: 取消订阅新闻
    - get_stock_data: 获取股票数据
    - get_market_overview: 获取市场概览
    - search_stocks: 搜索股票
    - get_hot_stocks: 获取热门股票
    """
    connection_id = None
    
    try:
        # 设置 Redis 客户端
        redis_client = await get_redis()
        await connection_manager.set_redis(redis_client)
        
        # 启动数据推送器
        if not data_pusher.is_running:
            await data_pusher.start()
        
        # 建立连接
        client_info = {
            "client_type": client_type,
            "version": version,
            "user_agent": websocket.headers.get("user-agent", ""),
            "origin": websocket.headers.get("origin", "")
        }
        
        connection_id = await connection_manager.connect(
            websocket=websocket,
            user_id=user_id,
            client_info=client_info
        )
        
        logger.info(f"WebSocket 连接建立: {connection_id}")
        
        # 消息循环
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理消息
                await message_handler.handle_message(connection_id, message, websocket)
                
            except json.JSONDecodeError:
                await connection_manager.send_personal_message(connection_id, {
                    "type": "error",
                    "message": "无效的JSON格式"
                })
            except WebSocketDisconnect:
                logger.info(f"WebSocket 连接断开: {connection_id}")
                break
            except Exception as e:
                logger.error(f"处理WebSocket消息失败: {e}")
                await connection_manager.send_personal_message(connection_id, {
                    "type": "error",
                    "message": "消息处理失败"
                })
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket 连接断开: {connection_id}")
    except Exception as e:
        logger.error(f"WebSocket 连接失败: {e}")
    finally:
        # 清理连接
        if connection_id:
            await connection_manager.disconnect(connection_id)


@router.get("/ws/stats")
async def get_websocket_stats():
    """获取 WebSocket 连接统计信息"""
    try:
        connection_stats = connection_manager.get_connection_stats()
        pusher_stats = data_pusher.get_pusher_stats()
        
        return {
            "connection_stats": connection_stats,
            "pusher_stats": pusher_stats,
            "timestamp": "2024-01-01T00:00:00"  # 使用当前时间
        }
        
    except Exception as e:
        logger.error(f"获取WebSocket统计信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取统计信息失败")


@router.post("/ws/broadcast")
async def broadcast_message(message: Dict[str, Any]):
    """
    广播消息给所有连接
    
    仅供管理员使用
    """
    try:
        await connection_manager.broadcast_message(message)
        
        return {
            "message": "消息已广播",
            "recipients": len(connection_manager.active_connections)
        }
        
    except Exception as e:
        logger.error(f"广播消息失败: {e}")
        raise HTTPException(status_code=500, detail="广播消息失败")


@router.post("/ws/push/stock/{stock_code}")
async def push_stock_data(stock_code: str, data: Dict[str, Any]):
    """
    推送股票数据给订阅用户
    
    Args:
        stock_code: 股票代码
        data: 股票数据
    """
    try:
        await connection_manager.push_stock_data(stock_code, data)
        
        subscribers = len(connection_manager.stock_subscriptions.get(stock_code, set()))
        
        return {
            "message": f"股票数据已推送: {stock_code}",
            "subscribers": subscribers
        }
        
    except Exception as e:
        logger.error(f"推送股票数据失败: {e}")
        raise HTTPException(status_code=500, detail="推送股票数据失败")


@router.post("/ws/push/news/{category}")
async def push_news_data(category: str, news_data: Dict[str, Any]):
    """
    推送新闻数据给订阅用户
    
    Args:
        category: 新闻分类
        news_data: 新闻数据
    """
    try:
        await connection_manager.push_news_data(category, news_data)
        
        subscribers = len(connection_manager.news_subscriptions.get(category, set()))
        
        return {
            "message": f"新闻数据已推送: {category}",
            "subscribers": subscribers
        }
        
    except Exception as e:
        logger.error(f"推送新闻数据失败: {e}")
        raise HTTPException(status_code=500, detail="推送新闻数据失败")


@router.post("/ws/push/alert/{user_id}")
async def push_user_alert(user_id: str, alert_data: Dict[str, Any]):
    """
    推送预警给特定用户
    
    Args:
        user_id: 用户ID
        alert_data: 预警数据
    """
    try:
        await connection_manager.push_alert(user_id, alert_data)
        
        user_connections = len(connection_manager.user_connections.get(user_id, set()))
        
        return {
            "message": f"预警已推送给用户: {user_id}",
            "connections": user_connections
        }
        
    except Exception as e:
        logger.error(f"推送用户预警失败: {e}")
        raise HTTPException(status_code=500, detail="推送用户预警失败")


@router.post("/ws/push/system")
async def push_system_notification(notification: Dict[str, Any]):
    """
    推送系统通知
    
    Args:
        notification: 通知数据
    """
    try:
        await data_pusher.push_system_notification(notification)
        
        return {
            "message": "系统通知已推送",
            "recipients": len(connection_manager.active_connections)
        }
        
    except Exception as e:
        logger.error(f"推送系统通知失败: {e}")
        raise HTTPException(status_code=500, detail="推送系统通知失败")


@router.get("/ws/connections")
async def get_active_connections():
    """获取活跃连接列表"""
    try:
        connections = []
        
        for connection_id, metadata in connection_manager.connection_metadata.items():
            connection_info = {
                "connection_id": connection_id,
                "user_id": metadata.get("user_id"),
                "connected_at": metadata.get("connected_at"),
                "last_heartbeat": metadata.get("last_heartbeat"),
                "client_info": metadata.get("client_info", {}),
                "subscriptions": {
                    "stocks": list(metadata.get("subscriptions", {}).get("stocks", set())),
                    "news": list(metadata.get("subscriptions", {}).get("news", set()))
                }
            }
            connections.append(connection_info)
        
        return {
            "connections": connections,
            "total": len(connections)
        }
        
    except Exception as e:
        logger.error(f"获取连接列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取连接列表失败")


@router.delete("/ws/connections/{connection_id}")
async def disconnect_connection(connection_id: str):
    """强制断开指定连接"""
    try:
        if connection_id in connection_manager.active_connections:
            await connection_manager.disconnect(connection_id)
            return {"message": f"连接已断开: {connection_id}"}
        else:
            raise HTTPException(status_code=404, detail="连接不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"断开连接失败: {e}")
        raise HTTPException(status_code=500, detail="断开连接失败")


@router.get("/ws/subscriptions/stocks")
async def get_stock_subscriptions():
    """获取股票订阅统计"""
    try:
        subscriptions = {}
        
        for stock_code, connection_ids in connection_manager.stock_subscriptions.items():
            subscriptions[stock_code] = {
                "subscribers": len(connection_ids),
                "connection_ids": list(connection_ids)
            }
        
        return {
            "subscriptions": subscriptions,
            "total_stocks": len(subscriptions),
            "total_subscribers": sum(len(ids) for ids in connection_manager.stock_subscriptions.values())
        }
        
    except Exception as e:
        logger.error(f"获取股票订阅统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票订阅统计失败")


@router.get("/ws/subscriptions/news")
async def get_news_subscriptions():
    """获取新闻订阅统计"""
    try:
        subscriptions = {}
        
        for category, connection_ids in connection_manager.news_subscriptions.items():
            subscriptions[category] = {
                "subscribers": len(connection_ids),
                "connection_ids": list(connection_ids)
            }
        
        return {
            "subscriptions": subscriptions,
            "total_categories": len(subscriptions),
            "total_subscribers": sum(len(ids) for ids in connection_manager.news_subscriptions.values())
        }
        
    except Exception as e:
        logger.error(f"获取新闻订阅统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取新闻订阅统计失败")


@router.post("/ws/test/message")
async def send_test_message(
    connection_id: Optional[str] = None,
    user_id: Optional[str] = None,
    message: Dict[str, Any] = None
):
    """
    发送测试消息
    
    用于测试 WebSocket 功能
    """
    try:
        if not message:
            message = {
                "type": "test_message",
                "content": "这是一条测试消息",
                "timestamp": "2024-01-01T00:00:00"
            }
        
        if connection_id:
            await connection_manager.send_personal_message(connection_id, message)
            return {"message": f"测试消息已发送给连接: {connection_id}"}
        elif user_id:
            await connection_manager.send_user_message(user_id, message)
            return {"message": f"测试消息已发送给用户: {user_id}"}
        else:
            await connection_manager.broadcast_message(message)
            return {"message": "测试消息已广播给所有连接"}
            
    except Exception as e:
        logger.error(f"发送测试消息失败: {e}")
        raise HTTPException(status_code=500, detail="发送测试消息失败")
