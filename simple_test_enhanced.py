#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的MCP Feedback Enhanced测试脚本
"""

import json
import subprocess
import sys
import time
import os
from pathlib import Path

def test_basic():
    """基本功能测试"""
    print("=== MCP Feedback Enhanced 集成测试 ===")
    
    # 1. 测试uvx命令
    print("1. 测试uvx命令")
    try:
        result = subprocess.run(["uvx", "--version"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("   [OK] uvx可用")
        else:
            print("   [FAIL] uvx命令不可用")
            return False
    except Exception as e:
        print(f"   [FAIL] uvx测试失败: {e}")
        return False
    
    # 2. 测试MCP包
    print("2. 测试MCP包")
    try:
        result = subprocess.run([
            "uvx", "mcp-feedback-enhanced@latest", "test"
        ], capture_output=True, text=True, timeout=30, encoding='utf-8', errors='ignore')
        
        if result.returncode == 0 or "test" in result.stdout.lower():
            print("   [OK] MCP包可用")
        else:
            print("   [FAIL] MCP包测试失败")
            return False
    except Exception as e:
        print(f"   [FAIL] MCP包测试异常: {e}")
        return False
    
    # 3. 检查配置文件
    print("3. 检查配置文件")
    config_files = [
        "augment-mcp-feedback-enhanced-config.json",
        "augment-mcp-feedback-enhanced-desktop-config.json"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"   [OK] {config_file} 格式正确")
            except Exception as e:
                print(f"   [FAIL] {config_file} 格式错误: {e}")
                return False
        else:
            print(f"   [FAIL] {config_file} 不存在")
            return False
    
    print("\n=== 测试完成 ===")
    print("基本测试通过！")
    
    print("\n=== 配置指南 ===")
    print("选择配置模式:")
    print("1. Web UI模式 (推荐)")
    print("   - 使用: augment-mcp-feedback-enhanced-config.json")
    print("   - 特点: 浏览器界面，适合远程环境")
    
    print("2. 桌面应用模式")
    print("   - 使用: augment-mcp-feedback-enhanced-desktop-config.json")
    print("   - 特点: 原生桌面应用，跨平台支持")
    
    print("\n配置步骤:")
    print("1. 将配置文件内容添加到Augment的MCP设置")
    print("2. 重启Augment")
    print("3. 测试 interactive_feedback 工具")
    
    print("\n增强功能:")
    print("- 图片上传支持 (拖拽、Ctrl+V)")
    print("- 提示词管理和自动提交")
    print("- 会话历史和统计")
    print("- 多语言支持")
    print("- 双界面模式")
    
    return True

if __name__ == "__main__":
    test_basic()
