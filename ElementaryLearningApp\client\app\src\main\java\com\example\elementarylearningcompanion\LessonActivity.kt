package com.example.elementarylearningcompanion

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.example.elementarylearningcompanion.dto.Character
import com.example.elementarylearningcompanion.dto.Lesson
import com.example.elementarylearningcompanion.dto.Sentence
import com.example.elementarylearningcompanion.ui.theme.ElementaryLearningCompanionTheme
import com.example.elementarylearningcompanion.viewmodel.ContentViewModel

class LessonActivity : ComponentActivity() {
    private val viewModel: ContentViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val lessonId = intent.getLongExtra("LESSON_ID", -1L)

        setContent {
            ElementaryLearningCompanionTheme {
                Surface(modifier = Modifier.fillMaxSize(), color = MaterialTheme.colorScheme.background) {
                    LessonScreen(viewModel, lessonId)
                }
            }
        }
    }
}

@Composable
fun LessonScreen(viewModel: ContentViewModel, lessonId: Long) {
    val context = LocalContext.current
    val lessonDetails by viewModel.lessonDetails.observeAsState()
    val characters by viewModel.characters.observeAsState(emptyList())
    var showCharacters by remember { mutableStateOf(false) }

    val isLoading by viewModel.isLoading.observeAsState(false)
    val error by viewModel.error.observeAsState()

    LaunchedEffect(lessonId) {
        if (lessonId != -1L) {
            viewModel.fetchLessonDetails(lessonId)
        }
    }
    
    LaunchedEffect(error) {
        error?.let { Toast.makeText(context, it, Toast.LENGTH_LONG).show() }
    }

    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        if (isLoading && lessonDetails == null) { // Show loading only on initial load
            CircularProgressIndicator()
        } else {
            lessonDetails?.let { lesson ->
                Column(
                    modifier = Modifier.fillMaxSize().padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 返回按钮
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.Start
                    ) {
                        TextButton(
                            onClick = { (context as? ComponentActivity)?.finish() }
                        ) {
                            Text("← 返回")
                        }
                    }

                    Text(lesson.getTitle(), style = MaterialTheme.typography.headlineLarge)
                    Spacer(modifier = Modifier.height(16.dp))

                    Button(onClick = { 
                        viewModel.fetchCharactersByLesson(lessonId)
                        showCharacters = !showCharacters
                    }) {
                        Text(if (showCharacters) "返回课文" else "学习本课生字")
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))

                    if (showCharacters) {
                        CharactersList(characters)
                    } else {
                        SentencesList(lesson.getSentences() ?: emptyList())
                    }
                }
            }
        }
    }
}

@Composable
fun SentencesList(sentences: List<Sentence>) {
    LazyColumn {
        items(sentences) { sentence ->
            SentenceItem(sentence = sentence)
        }
    }
}

@Composable
fun CharactersList(characters: List<Character>) {
    LazyColumn {
        items(characters) { character ->
            CharacterItem(character = character)
        }
    }
}

@Composable
fun SentenceItem(sentence: Sentence) {
    val context = LocalContext.current
    Card(
        modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp),
    ) {
        Row(
            modifier = Modifier.padding(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = sentence.getTextContent(),
                modifier = Modifier.weight(1f)
            )
            IconButton(onClick = { 
                // TODO: Implement audio playback
                Toast.makeText(context, "Playing audio for sentence ${sentence.getId()}", Toast.LENGTH_SHORT).show()
            }) {
                Icon(Icons.Default.PlayArrow, contentDescription = "Play audio")
            }
        }
    }
}

@Composable
fun CharacterItem(character: Character) {
    Card(
        modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp),
    ) {
        Row(
            modifier = Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text("${character.getCharacterText()} (${character.getPinyin()})", style = MaterialTheme.typography.bodyLarge)
            Icon(Icons.Default.ArrowForward, contentDescription = "View details")
        }
    }
}
