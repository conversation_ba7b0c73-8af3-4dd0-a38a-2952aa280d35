"""
新闻相关 API 端点
"""

from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.news_collector.news_service import NewsService
from app.auth.security import get_current_user_from_token
from app.schemas.news import (
    NewsResponse,
    NewsListResponse,
    NewsAnalyticsResponse,
    HotNewsResponse
)
from app.schemas.base import PaginationResponse

router = APIRouter()


@router.get("/", response_model=PaginationResponse[NewsResponse])
async def get_news_list(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    category_id: Optional[str] = Query(None, description="分类ID"),
    source: Optional[str] = Query(None, description="新闻源"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    importance_min: Optional[int] = Query(None, ge=1, le=5, description="最小重要性等级"),
    sentiment: Optional[str] = Query(None, description="情感倾向"),
    search_query: Optional[str] = Query(None, description="搜索关键词"),
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取新闻列表"""
    news_service = NewsService(db)
    
    result = await news_service.get_news_list(
        skip=skip,
        limit=limit,
        category_id=category_id,
        source=source,
        start_date=start_date,
        end_date=end_date,
        importance_min=importance_min,
        sentiment=sentiment,
        search_query=search_query
    )
    
    return PaginationResponse(
        data=[NewsResponse(**news) for news in result["news"]],
        total=result["total"],
        skip=result["skip"],
        limit=result["limit"],
        has_more=result["has_more"]
    )


@router.get("/hot", response_model=List[HotNewsResponse])
async def get_hot_news(
    limit: int = Query(10, ge=1, le=50, description="返回数量"),
    hours: int = Query(24, ge=1, le=168, description="时间范围（小时）"),
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取热门新闻"""
    news_service = NewsService(db)
    hot_news = await news_service.get_hot_news(limit=limit, hours=hours)
    
    return [HotNewsResponse(**news) for news in hot_news]


@router.get("/search", response_model=PaginationResponse[NewsResponse])
async def search_news(
    q: str = Query(..., description="搜索关键词"),
    limit: int = Query(20, ge=1, le=100, description="返回数量"),
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """搜索新闻"""
    news_service = NewsService(db)
    results = await news_service.search_news(query=q, limit=limit)
    
    return PaginationResponse(
        data=[NewsResponse(**news) for news in results],
        total=len(results),
        skip=0,
        limit=limit,
        has_more=False
    )


@router.get("/analytics", response_model=NewsAnalyticsResponse)
async def get_news_analytics(
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    stock_code: Optional[str] = Query(None, description="股票代码"),
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取新闻分析统计"""
    news_service = NewsService(db)
    
    analytics = await news_service.get_news_analytics(
        start_date=start_date,
        end_date=end_date,
        stock_code=stock_code
    )
    
    return NewsAnalyticsResponse(**analytics)


@router.get("/stock/{stock_code}", response_model=List[NewsResponse])
async def get_news_by_stock(
    stock_code: str,
    limit: int = Query(20, ge=1, le=100, description="返回数量"),
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取股票相关新闻"""
    news_service = NewsService(db)
    related_news = await news_service.get_news_by_stock(
        stock_code=stock_code,
        limit=limit
    )
    
    return [NewsResponse(**news) for news in related_news]


@router.get("/{news_id}", response_model=NewsResponse)
async def get_news_by_id(
    news_id: str,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取新闻详情"""
    news_service = NewsService(db)
    news = await news_service.get_news_by_id(news_id)
    
    if not news:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="新闻不存在"
        )
    
    return NewsResponse(**news)


@router.post("/{news_id}/like", response_model=dict)
async def like_news(
    news_id: str,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """点赞新闻"""
    # TODO: 实现新闻点赞功能
    return {"success": True, "message": "点赞成功"}


@router.delete("/{news_id}/like", response_model=dict)
async def unlike_news(
    news_id: str,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """取消点赞新闻"""
    # TODO: 实现取消点赞功能
    return {"success": True, "message": "取消点赞成功"}


@router.post("/{news_id}/bookmark", response_model=dict)
async def bookmark_news(
    news_id: str,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """收藏新闻"""
    # TODO: 实现新闻收藏功能
    return {"success": True, "message": "收藏成功"}


@router.delete("/{news_id}/bookmark", response_model=dict)
async def unbookmark_news(
    news_id: str,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """取消收藏新闻"""
    # TODO: 实现取消收藏功能
    return {"success": True, "message": "取消收藏成功"}


@router.get("/categories/", response_model=List[dict])
async def get_news_categories(
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取新闻分类列表"""
    # TODO: 实现新闻分类管理
    categories = [
        {"id": "market", "name": "市场资讯", "description": "股市行情和市场动态"},
        {"id": "policy", "name": "政策法规", "description": "监管政策和法规变化"},
        {"id": "company", "name": "公司公告", "description": "上市公司公告和业绩"},
        {"id": "industry", "name": "行业动态", "description": "各行业发展趋势"},
        {"id": "macro", "name": "宏观经济", "description": "宏观经济数据和分析"},
        {"id": "international", "name": "国际财经", "description": "国际市场和外汇"},
        {"id": "technology", "name": "科技创新", "description": "科技行业创新动态"},
        {"id": "analysis", "name": "分析评论", "description": "专家分析和市场评论"},
    ]
    
    return categories


@router.get("/sources/", response_model=List[dict])
async def get_news_sources(
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取新闻源列表"""
    from app.news_collector.news_sources import news_source_manager
    
    sources = news_source_manager.get_active_sources()
    return [
        {
            "name": source.name,
            "display_name": source.display_name,
            "category": source.category.value,
            "priority": source.priority,
            "description": source.description
        }
        for source in sources
    ]


@router.post("/collect", response_model=dict)
async def trigger_news_collection(
    max_articles_per_source: int = Query(50, ge=1, le=200, description="每个源最大文章数"),
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """手动触发新闻采集"""
    # 检查用户权限
    if not current_user.get("is_superuser", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    # 触发新闻采集任务
    from app.news_collector.tasks import collect_news_task
    
    # 异步执行采集任务
    task = collect_news_task.delay(max_articles_per_source=max_articles_per_source)
    
    return {
        "success": True,
        "message": "新闻采集任务已启动",
        "task_id": task.id
    }
