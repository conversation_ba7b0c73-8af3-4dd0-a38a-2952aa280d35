package com.example.elementarylearningcompanion.controller;

import com.example.elementarylearningcompanion.dto.ApiResponse;
import com.example.elementarylearningcompanion.model.Question;
import com.example.elementarylearningcompanion.model.TestPaper;
import com.example.elementarylearningcompanion.model.StudentAnswer;
import com.example.elementarylearningcompanion.model.WrongQuestion;
import com.example.elementarylearningcompanion.service.TestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
public class TestController {

    @Autowired
    private TestService testService;

    @GetMapping("/questions")
    public ResponseEntity<List<Question>> getQuestions(
            @RequestParam Integer textbookVersionId,
            @RequestParam Integer grade,
            @RequestParam String subject,
            @RequestParam(required = false) String questionType,
            @RequestParam(required = false) Integer difficultyLevel,
            @RequestParam(defaultValue = "10") Integer count) {
        
        List<Question> questions;
        if (questionType != null && difficultyLevel != null) {
            questions = testService.getRandomQuestionsByTypeAndDifficulty(
                textbookVersionId, grade, subject, questionType, difficultyLevel, count);
        } else {
            questions = testService.getRandomQuestions(textbookVersionId, grade, subject, count);
        }
        
        return ResponseEntity.ok(questions);
    }

    @GetMapping("/question-types")
    public ResponseEntity<List<String>> getQuestionTypes(
            @RequestParam Integer textbookVersionId,
            @RequestParam Integer grade,
            @RequestParam String subject) {
        List<String> types = testService.getAvailableQuestionTypes(textbookVersionId, grade, subject);
        return ResponseEntity.ok(types);
    }

    @GetMapping("/papers")
    public ResponseEntity<List<TestPaper>> getTestPapers(
            @RequestParam Integer textbookVersionId,
            @RequestParam Integer grade,
            @RequestParam String subject) {
        List<TestPaper> testPapers = testService.getActiveTestPapers(textbookVersionId, grade, subject);
        return ResponseEntity.ok(testPapers);
    }

    @GetMapping("/papers/{testPaperId}")
    public ResponseEntity<TestPaper> getTestPaper(@PathVariable Long testPaperId) {
        return testService.getTestPaper(testPaperId)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/submit-answer")
    public ResponseEntity<ApiResponse> submitAnswer(@RequestBody Map<String, Object> request) {
        try {
            Long studentId = Long.valueOf(request.get("studentId").toString());
            Long questionId = Long.valueOf(request.get("questionId").toString());
            String answer = request.get("answer").toString();
            Integer timeSpent = request.get("timeSpent") != null ? 
                Integer.valueOf(request.get("timeSpent").toString()) : 0;
            Long testPaperId = request.get("testPaperId") != null ? 
                Long.valueOf(request.get("testPaperId").toString()) : null;

            boolean isCorrect = testService.submitAnswer(studentId, questionId, answer, timeSpent, testPaperId);
            
            String message = isCorrect ? "答案正确！" : "答案错误，请再试一次。";
            return ResponseEntity.ok(new ApiResponse(isCorrect, message));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "提交答案失败：" + e.getMessage()));
        }
    }

    @GetMapping("/result/{studentId}/{testPaperId}")
    public ResponseEntity<Map<String, Object>> getTestResult(
            @PathVariable Long studentId,
            @PathVariable Long testPaperId) {
        Map<String, Object> result = testService.getTestResult(studentId, testPaperId);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/answers/{studentId}/{testPaperId}")
    public ResponseEntity<List<StudentAnswer>> getStudentAnswers(
            @PathVariable Long studentId,
            @PathVariable Long testPaperId) {
        List<StudentAnswer> answers = testService.getStudentAnswers(studentId, testPaperId);
        return ResponseEntity.ok(answers);
    }

    @GetMapping("/wrong-questions/{studentId}")
    public ResponseEntity<List<WrongQuestion>> getWrongQuestions(@PathVariable Long studentId) {
        List<WrongQuestion> wrongQuestions = testService.getWrongQuestions(studentId);
        return ResponseEntity.ok(wrongQuestions);
    }

    @PostMapping("/generate-practice")
    public ResponseEntity<TestPaper> generatePracticeTest(@RequestBody Map<String, Object> request) {
        try {
            Integer textbookVersionId = Integer.valueOf(request.get("textbookVersionId").toString());
            Integer grade = Integer.valueOf(request.get("grade").toString());
            String subject = request.get("subject").toString();
            Integer questionCount = Integer.valueOf(request.get("questionCount").toString());
            String title = request.get("title").toString();

            TestPaper testPaper = testService.generatePracticeTest(
                textbookVersionId, grade, subject, questionCount, title);
            
            return ResponseEntity.ok(testPaper);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/subjects")
    public ResponseEntity<List<Map<String, Object>>> getSubjects() {
        List<Map<String, Object>> subjects = List.of(
            Map.of("code", "chinese", "name", "语文", "description", "语文学科测试"),
            Map.of("code", "math", "name", "数学", "description", "数学学科测试"),
            Map.of("code", "english", "name", "英语", "description", "英语学科测试")
        );
        return ResponseEntity.ok(subjects);
    }

    @GetMapping("/question-types-info")
    public ResponseEntity<List<Map<String, Object>>> getQuestionTypesInfo() {
        List<Map<String, Object>> types = List.of(
            Map.of("type", "single_choice", "name", "单选题", "description", "从多个选项中选择一个正确答案"),
            Map.of("type", "multiple_choice", "name", "多选题", "description", "从多个选项中选择多个正确答案"),
            Map.of("type", "true_false", "name", "判断题", "description", "判断题目描述是否正确"),
            Map.of("type", "fill_blank", "name", "填空题", "description", "在空白处填入正确答案")
        );
        return ResponseEntity.ok(types);
    }
}
