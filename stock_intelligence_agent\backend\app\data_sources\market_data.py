"""
市场数据源提供者
获取板块、概念、指数等市场数据
"""

import asyncio
import aiohttp
import json
from typing import List, Dict, Any, Optional
from datetime import datetime, date
import logging

logger = logging.getLogger(__name__)

class MarketDataProvider:
    """市场数据提供者"""
    
    def __init__(self):
        self.session = None
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'http://quote.eastmoney.com/',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            headers=self.headers,
            timeout=aiohttp.ClientTimeout(total=10)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_hot_sectors(self, sector_type: str = "concept", limit: int = 20) -> List[Dict[str, Any]]:
        """获取热门板块

        Args:
            sector_type: 板块类型 ('concept' 概念板块, 'industry' 行业板块)
            limit: 返回数量
        """
        try:
            # 使用东方财富API获取板块数据
            url = "http://push2.eastmoney.com/api/qt/clist/get"

            # 根据板块类型设置不同参数
            if sector_type == "concept":
                fs_param = 'm:90+t:3'  # 概念板块
            else:
                fs_param = 'm:90+t:2'  # 行业板块

            params = {
                'pn': 1,
                'pz': limit,
                'po': 1,
                'np': 1,
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': 2,
                'invt': 2,
                'fid': 'f3',  # 按涨跌幅排序
                'fs': fs_param,
                'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f26,f22,f33,f11,f62,f128,f136,f115,f152,f124,f107,f104,f105,f140,f141,f207,f208,f209,f222'
            }

            async with self.session.get(url, params=params) as response:
                data = await response.json()

                hot_sectors = []
                for item in data.get('data', {}).get('diff', []):
                    hot_sectors.append({
                        'sector_name': item.get('f14', ''),
                        'sector_code': item.get('f12', ''),
                        'sector_type': sector_type,
                        'stock_count': item.get('f104', 0),
                        'avg_pct_change': item.get('f3', 0) / 100 if item.get('f3') else 0,
                        'total_amount': item.get('f6', 0),
                        'avg_turnover_rate': item.get('f8', 0) / 100 if item.get('f8') else 0,
                        'max_pct_change': item.get('f15', 0) / 100 if item.get('f15') else 0,
                        'min_pct_change': item.get('f16', 0) / 100 if item.get('f16') else 0,
                        'leading_stock': item.get('f140', ''),
                        'leading_stock_code': item.get('f141', ''),
                        'rank': len(hot_sectors) + 1
                    })

                return hot_sectors

        except Exception as e:
            logger.error(f"获取热门板块失败: {e}")
            return self._get_mock_hot_sectors(sector_type)
    
    async def get_concept_sectors(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取概念板块"""
        try:
            # 获取概念板块数据
            url = "http://push2.eastmoney.com/api/qt/clist/get"
            params = {
                'pn': 1,
                'pz': limit,
                'po': 1,
                'np': 1,
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': 2,
                'invt': 2,
                'fid': 'f3',
                'fs': 'm:90+t:3',  # 概念板块
                'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f26,f22,f33,f11,f62,f128,f136,f115,f152,f124,f107,f104,f105,f140,f141'
            }
            
            async with self.session.get(url, params=params) as response:
                data = await response.json()
                
                concept_sectors = []
                for item in data.get('data', {}).get('diff', []):
                    concept_sectors.append({
                        'sector_name': item.get('f14', ''),
                        'sector_code': item.get('f12', ''),
                        'stock_count': item.get('f104', 0),
                        'avg_pct_change': item.get('f3', 0) / 100 if item.get('f3') else 0,
                        'total_amount': item.get('f6', 0),
                        'avg_turnover_rate': item.get('f8', 0) / 100 if item.get('f8') else 0,
                        'leading_stock': item.get('f140', ''),
                        'leading_stock_code': item.get('f141', ''),
                    })
                
                return concept_sectors
                
        except Exception as e:
            logger.error(f"获取概念板块失败: {e}")
            return self._get_mock_concept_sectors()
    
    async def get_sector_stocks(self, sector_code: str, limit: int = 50) -> List[Dict[str, Any]]:
        """获取板块内股票"""
        try:
            url = "http://push2.eastmoney.com/api/qt/clist/get"
            params = {
                'pn': 1,
                'pz': limit,
                'po': 1,
                'np': 1,
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': 2,
                'invt': 2,
                'fid': 'f3',
                'fs': f'b:{sector_code}',
                'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152'
            }
            
            async with self.session.get(url, params=params) as response:
                data = await response.json()
                
                stocks = []
                for item in data.get('data', {}).get('diff', []):
                    stocks.append({
                        'code': item.get('f12', ''),
                        'name': item.get('f14', ''),
                        'market': 'SH' if item.get('f13', 0) == 1 else 'SZ',
                        'close_price': item.get('f2', 0) / 100 if item.get('f2') else 0,
                        'change': item.get('f4', 0) / 100 if item.get('f4') else 0,
                        'pct_change': item.get('f3', 0) / 100 if item.get('f3') else 0,
                        'volume': item.get('f5', 0),
                        'amount': item.get('f6', 0),
                        'turnover_rate': item.get('f8', 0) / 100 if item.get('f8') else 0,
                        'pe_ratio': item.get('f9', 0) / 100 if item.get('f9') else 0,
                        'pb_ratio': item.get('f23', 0) / 100 if item.get('f23') else 0,
                    })
                
                return stocks
                
        except Exception as e:
            logger.error(f"获取板块股票失败: {e}")
            return []
    
    async def get_market_indices(self) -> List[Dict[str, Any]]:
        """获取市场指数"""
        try:
            # 主要指数代码
            indices = {
                '000001': '上证指数',
                '399001': '深证成指',
                '399006': '创业板指',
                '000300': '沪深300',
                '000016': '上证50',
                '399905': '中证500',
            }
            
            index_data = []
            for code, name in indices.items():
                try:
                    # 获取指数实时数据
                    url = f"http://push2.eastmoney.com/api/qt/stock/get"
                    params = {
                        'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                        'invt': 2,
                        'fltt': 1,
                        'fields': 'f43,f57,f58,f169,f170,f46,f44,f51,f168,f47,f164,f163,f116,f60,f45,f52,f50,f48,f167,f117,f71,f161,f49,f530,f135,f136,f137,f138,f139,f141,f142,f144,f145,f147,f148,f140,f143,f146,f149,f55,f62,f162,f92,f173,f104,f105,f84,f85,f183,f184,f185,f186,f187,f188,f189,f190,f191,f192,f107,f111,f86,f177,f78,f110,f262,f263,f264,f267,f268,f250,f251,f252,f253,f254,f255,f256,f257,f258,f266,f269,f270,f271,f273,f274,f275,f127,f199,f128,f193,f196,f194,f195,f197,f80,f280,f281,f282,f284,f285,f286,f287,f292',
                        'secid': f"1.{code}" if code.startswith('000') else f"0.{code}"
                    }
                    
                    async with self.session.get(url, params=params) as response:
                        data = await response.json()
                        
                        if data.get('data'):
                            item = data['data']
                            index_data.append({
                                'code': code,
                                'name': name,
                                'current': item.get('f43', 0) / 100 if item.get('f43') else 0,
                                'change': item.get('f169', 0) / 100 if item.get('f169') else 0,
                                'pct_change': item.get('f170', 0) / 100 if item.get('f170') else 0,
                                'volume': item.get('f47', 0),
                                'amount': item.get('f48', 0),
                                'high': item.get('f44', 0) / 100 if item.get('f44') else 0,
                                'low': item.get('f45', 0) / 100 if item.get('f45') else 0,
                                'open': item.get('f46', 0) / 100 if item.get('f46') else 0,
                            })
                except Exception as e:
                    logger.error(f"获取指数{code}数据失败: {e}")
                    continue
            
            return index_data
            
        except Exception as e:
            logger.error(f"获取市场指数失败: {e}")
            return self._get_mock_market_indices()
    
    async def get_market_overview(self) -> Dict[str, Any]:
        """获取市场概览"""
        try:
            # 获取市场统计数据
            indices = await self.get_market_indices()
            hot_sectors = await self.get_hot_sectors(limit=10)
            
            # 计算市场统计
            overview = {
                'market_indices': indices,
                'hot_sectors': hot_sectors,
                'market_status': {
                    'is_trading_day': True,
                    'is_trading_time': self._is_trading_time(),
                    'session': self._get_trading_session(),
                    'current_time': datetime.now().isoformat(),
                },
                'statistics': {
                    'total_stocks': 5000,  # 估算值
                    'market_distribution': {
                        'SH': 2000,
                        'SZ': 2500,
                        'BJ': 500,
                    },
                    'top_industries': {},
                    'last_updated': datetime.now().isoformat(),
                }
            }
            
            return overview
            
        except Exception as e:
            logger.error(f"获取市场概览失败: {e}")
            return self._get_mock_market_overview()
    
    def _is_trading_time(self) -> bool:
        """判断是否交易时间"""
        now = datetime.now()
        weekday = now.weekday()
        
        # 周末不交易
        if weekday >= 5:
            return False
        
        # 交易时间：9:30-11:30, 13:00-15:00
        time_str = now.strftime('%H%M')
        time_int = int(time_str)
        
        return (930 <= time_int <= 1130) or (1300 <= time_int <= 1500)
    
    def _get_trading_session(self) -> str:
        """获取交易时段"""
        now = datetime.now()
        time_str = now.strftime('%H%M')
        time_int = int(time_str)
        
        if 930 <= time_int <= 1130:
            return 'morning'
        elif 1300 <= time_int <= 1500:
            return 'afternoon'
        else:
            return 'closed'
    
    def _get_mock_hot_sectors(self) -> List[Dict[str, Any]]:
        """模拟热门板块数据"""
        return [
            {
                'sector_name': '银行',
                'sector_code': 'BK0475',
                'stock_count': 42,
                'avg_pct_change': 1.25,
                'total_amount': 15000000000,
                'avg_turnover_rate': 0.85,
                'max_pct_change': 3.50,
                'min_pct_change': -0.50,
                'leading_stock': '平安银行',
                'leading_stock_code': '000001',
            },
            {
                'sector_name': '新能源汽车',
                'sector_code': 'BK0493',
                'stock_count': 156,
                'avg_pct_change': 2.15,
                'total_amount': 25000000000,
                'avg_turnover_rate': 1.25,
                'max_pct_change': 8.50,
                'min_pct_change': -2.30,
                'leading_stock': '比亚迪',
                'leading_stock_code': '002594',
            }
        ]
    
    def _get_mock_concept_sectors(self) -> List[Dict[str, Any]]:
        """模拟概念板块数据"""
        return [
            {
                'sector_name': '人工智能',
                'sector_code': 'BK0464',
                'stock_count': 89,
                'avg_pct_change': 1.85,
                'total_amount': 18000000000,
                'avg_turnover_rate': 1.15,
                'leading_stock': '科大讯飞',
                'leading_stock_code': '002230',
            }
        ]
    
    def _get_mock_market_indices(self) -> List[Dict[str, Any]]:
        """模拟市场指数数据"""
        return [
            {
                'code': '000001',
                'name': '上证指数',
                'current': 3200.50,
                'change': 15.20,
                'pct_change': 0.48,
                'volume': 250000000,
                'amount': 320000000000,
                'high': 3215.80,
                'low': 3185.30,
                'open': 3190.00,
            }
        ]
    
    def _get_mock_market_overview(self) -> Dict[str, Any]:
        """模拟市场概览数据"""
        return {
            'market_indices': self._get_mock_market_indices(),
            'hot_sectors': self._get_mock_hot_sectors(),
            'market_status': {
                'is_trading_day': True,
                'is_trading_time': False,
                'session': 'closed',
                'current_time': datetime.now().isoformat(),
            },
            'statistics': {
                'total_stocks': 5000,
                'market_distribution': {
                    'SH': 2000,
                    'SZ': 2500,
                    'BJ': 500,
                },
                'top_industries': {
                    '银行': 50,
                    '房地产': 45,
                    '医药': 40,
                },
                'last_updated': datetime.now().isoformat(),
            }
        }
