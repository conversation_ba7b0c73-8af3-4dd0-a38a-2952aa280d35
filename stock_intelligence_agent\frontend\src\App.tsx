/**
 * 主应用组件
 */

import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ConfigProvider, App as AntApp, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import enUS from 'antd/locale/en_US';

import { store } from '@/store';
import { useAppDispatch, useAppSelector } from '@/store';
import { restoreFromStorage, getCurrentUserAsync } from '@/store/slices/authSlice';
import { selectTheme, selectLanguage } from '@/store/slices/uiSlice';

// 导入页面组件
import Layout from '@/components/Layout';
import LoginPage from '@/pages/Login';
import RegisterPage from '@/pages/Register';
import HomePage from '@/pages/Home';
import StockListPage from '@/pages/StockList';
import StockDetailPage from '@/pages/StockDetail';
import NewsPage from '@/pages/News';
import NewsDetailPage from '@/pages/NewsDetail';
import WatchlistPage from '@/pages/Watchlist';
import AlertPage from '@/pages/Alert';
import ProfilePage from '@/pages/Profile';
import SettingsPage from '@/pages/Settings';
import NotFoundPage from '@/pages/NotFound';

// 导入样式
import '@/styles/global.css';

// 受保护的路由组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isAuthenticated = useAppSelector(state => state.auth.isAuthenticated);
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return <>{children}</>;
};

// 应用内容组件
const AppContent: React.FC = () => {
  const dispatch = useAppDispatch();
  const currentTheme = useAppSelector(selectTheme);
  const language = useAppSelector(selectLanguage);
  const isAuthenticated = useAppSelector(state => state.auth.isAuthenticated);

  // 应用初始化
  useEffect(() => {
    // 恢复认证状态
    dispatch(restoreFromStorage());
    
    // 如果有令牌，尝试获取用户信息
    const token = localStorage.getItem('access_token');
    if (token) {
      dispatch(getCurrentUserAsync());
    }
  }, [dispatch]);

  // Ant Design 主题配置
  const antdTheme = {
    algorithm: currentTheme === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
    token: {
      colorPrimary: '#1890ff',
      borderRadius: 6,
      wireframe: false,
    },
  };

  // 语言配置
  const locale = language === 'zh-CN' ? zhCN : enUS;

  return (
    <ConfigProvider theme={antdTheme} locale={locale}>
      <AntApp>
        <Router>
          <Routes>
            {/* 公开路由 */}
            <Route 
              path="/login" 
              element={
                isAuthenticated ? <Navigate to="/" replace /> : <LoginPage />
              } 
            />
            <Route 
              path="/register" 
              element={
                isAuthenticated ? <Navigate to="/" replace /> : <RegisterPage />
              } 
            />
            
            {/* 受保护的路由 */}
            <Route 
              path="/" 
              element={
                <ProtectedRoute>
                  <Layout />
                </ProtectedRoute>
              }
            >
              {/* 主页 */}
              <Route index element={<HomePage />} />
              
              {/* 股票相关页面 */}
              <Route path="stocks" element={<StockListPage />} />
              <Route path="stocks/:stockCode" element={<StockDetailPage />} />
              
              {/* 新闻相关页面 */}
              <Route path="news" element={<NewsPage />} />
              <Route path="news/:newsId" element={<NewsDetailPage />} />
              
              {/* 自选股页面 */}
              <Route path="watchlist" element={<WatchlistPage />} />
              
              {/* 预警页面 */}
              <Route path="alerts" element={<AlertPage />} />
              
              {/* 用户相关页面 */}
              <Route path="profile" element={<ProfilePage />} />
              <Route path="settings" element={<SettingsPage />} />
            </Route>
            
            {/* 404 页面 */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </Router>
      </AntApp>
    </ConfigProvider>
  );
};

// 主应用组件
const App: React.FC = () => {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
};

export default App;
