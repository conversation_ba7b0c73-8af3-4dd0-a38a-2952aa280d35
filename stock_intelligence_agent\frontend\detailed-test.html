<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票智能体 - 详细功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }
        
        .test-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            background: #fafafa;
        }
        
        .test-card h4 {
            color: #333;
            margin-bottom: 12px;
            font-size: 1.1rem;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 4px;
            font-size: 13px;
            transition: all 0.3s;
        }
        
        .test-button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .test-button.success {
            background: #28a745;
        }
        
        .test-button.error {
            background: #dc3545;
        }
        
        .result-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 12px;
            margin-top: 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.success {
            background: #28a745;
        }
        
        .status-indicator.error {
            background: #dc3545;
        }
        
        .status-indicator.pending {
            background: #ffc107;
        }
        
        .feature-demo {
            margin-top: 16px;
            padding: 16px;
            background: #e3f2fd;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        
        .stock-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
        }
        
        .stock-price {
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .price-up {
            color: #d32f2f;
        }
        
        .price-down {
            color: #388e3c;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
            margin-bottom: 16px;
        }
        
        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            border-bottom-color: #007bff;
            color: #007bff;
            font-weight: 600;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 股票智能体 - 详细功能测试</h1>
        <p>专业级股票分析平台功能演示</p>
    </div>
    
    <div class="container">
        <!-- 系统状态检查 -->
        <div class="test-section">
            <h2 class="section-title">🔧 系统状态检查</h2>
            <div class="test-grid">
                <div class="test-card">
                    <h4>后端服务状态</h4>
                    <div id="backend-status">
                        <span class="status-indicator pending"></span>检测中...
                    </div>
                    <button class="test-button" onclick="checkBackendStatus()">重新检测</button>
                </div>
                <div class="test-card">
                    <h4>API接口统计</h4>
                    <div>总接口数: <strong>22个</strong></div>
                    <div>已测试: <span id="tested-apis">0</span>个</div>
                    <div>成功率: <span id="success-rate">0%</span></div>
                </div>
            </div>
        </div>

        <!-- 功能测试标签页 -->
        <div class="test-section">
            <h2 class="section-title">🧪 功能模块测试</h2>
            
            <div class="tabs">
                <div class="tab active" onclick="switchTab('stocks')">📈 股票功能</div>
                <div class="tab" onclick="switchTab('analysis')">📊 分析功能</div>
                <div class="tab" onclick="switchTab('market')">🏪 市场功能</div>
                <div class="tab" onclick="switchTab('news')">📰 新闻功能</div>
                <div class="tab" onclick="switchTab('user')">👤 用户功能</div>
            </div>

            <!-- 股票功能测试 -->
            <div id="stocks-content" class="tab-content active">
                <div class="test-grid">
                    <div class="test-card">
                        <h4>股票列表</h4>
                        <button class="test-button" onclick="testStockList()">获取股票列表</button>
                        <button class="test-button" onclick="testHotStocks('pct_change', 100)">涨幅榜前100</button>
                        <button class="test-button" onclick="testHotStocks('amount', 300)">成交金额榜前300</button>
                        <div class="result-area" id="stocks-result">点击按钮测试股票功能...</div>
                    </div>
                    <div class="test-card">
                        <h4>股票详情</h4>
                        <input type="text" id="stock-code" placeholder="输入股票代码 (如: 000001)" style="width: 100%; padding: 8px; margin-bottom: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <button class="test-button" onclick="testStockDetail()">获取股票详情</button>
                        <div class="result-area" id="stock-detail-result">输入股票代码测试...</div>
                    </div>
                </div>
                
                <div class="feature-demo">
                    <h4>📊 股票数据展示示例</h4>
                    <div id="stock-demo"></div>
                </div>
            </div>

            <!-- 分析功能测试 -->
            <div id="analysis-content" class="tab-content">
                <div class="test-grid">
                    <div class="test-card">
                        <h4>技术分析</h4>
                        <input type="text" id="analysis-code" placeholder="股票代码" style="width: 100%; padding: 8px; margin-bottom: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <button class="test-button" onclick="testTechnicalAnalysis()">技术分析</button>
                        <button class="test-button" onclick="testFundamentalAnalysis()">基本面分析</button>
                        <button class="test-button" onclick="testRiskAssessment()">风险评估</button>
                        <div class="result-area" id="analysis-result">选择分析类型...</div>
                    </div>
                    <div class="test-card">
                        <h4>投资组合分析</h4>
                        <textarea id="portfolio-input" placeholder='输入投资组合JSON格式:
{
  "000001": 0.3,
  "000002": 0.4,
  "600000": 0.3
}' style="width: 100%; height: 80px; padding: 8px; margin-bottom: 8px; border: 1px solid #ddd; border-radius: 4px;"></textarea>
                        <button class="test-button" onclick="testPortfolioAnalysis()">组合分析</button>
                        <div class="result-area" id="portfolio-result">输入投资组合数据...</div>
                    </div>
                </div>
            </div>

            <!-- 市场功能测试 -->
            <div id="market-content" class="tab-content">
                <div class="test-grid">
                    <div class="test-card">
                        <h4>市场概览</h4>
                        <button class="test-button" onclick="testMarketOverview()">市场概览</button>
                        <button class="test-button" onclick="testMarketSentiment()">市场情绪</button>
                        <div class="result-area" id="market-result">点击按钮测试市场功能...</div>
                    </div>
                    <div class="test-card">
                        <h4>板块分析</h4>
                        <button class="test-button" onclick="testHotSectors('concept')">热门概念板块</button>
                        <button class="test-button" onclick="testHotSectors('industry')">热门行业板块</button>
                        <button class="test-button" onclick="testSectorFunds()">资金流向</button>
                        <button class="test-button" onclick="testSectorRanking()">板块排行</button>
                        <div class="result-area" id="sector-result">点击按钮测试板块功能...</div>
                    </div>
                </div>
            </div>

            <!-- 新闻功能测试 -->
            <div id="news-content" class="tab-content">
                <div class="test-grid">
                    <div class="test-card">
                        <h4>新闻资讯</h4>
                        <button class="test-button" onclick="testNewsList()">新闻列表</button>
                        <button class="test-button" onclick="testHotTopics()">热点话题</button>
                        <div class="result-area" id="news-result">点击按钮测试新闻功能...</div>
                    </div>
                    <div class="test-card">
                        <h4>新闻筛选</h4>
                        <select id="news-category" style="width: 100%; padding: 8px; margin-bottom: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            <option value="">全部分类</option>
                            <option value="market_dynamics">市场动态</option>
                            <option value="policy">政策监管</option>
                            <option value="company">公司公告</option>
                            <option value="market">市场行情</option>
                            <option value="funds">资金流向</option>
                        </select>
                        <button class="test-button" onclick="testNewsFilter()">筛选新闻</button>
                        <div class="result-area" id="news-filter-result">选择分类筛选...</div>
                    </div>
                </div>
            </div>

            <!-- 用户功能测试 -->
            <div id="user-content" class="tab-content">
                <div class="test-grid">
                    <div class="test-card">
                        <h4>自选股管理</h4>
                        <button class="test-button" onclick="testWatchlist()">获取自选股</button>
                        <input type="text" id="add-stock" placeholder="添加股票代码" style="width: 70%; padding: 8px; margin: 8px 0; border: 1px solid #ddd; border-radius: 4px;">
                        <button class="test-button" onclick="testAddWatchlist()">添加</button>
                        <div class="result-area" id="watchlist-result">点击按钮测试自选股功能...</div>
                    </div>
                    <div class="test-card">
                        <h4>预警管理</h4>
                        <button class="test-button" onclick="testAlerts()">获取预警</button>
                        <button class="test-button" onclick="testCreateAlert()">创建预警</button>
                        <div class="result-area" id="alerts-result">点击按钮测试预警功能...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 性能测试 -->
        <div class="test-section">
            <h2 class="section-title">⚡ 性能测试</h2>
            <div class="test-grid">
                <div class="test-card">
                    <h4>API响应时间</h4>
                    <button class="test-button" onclick="testAPIPerformance()">开始性能测试</button>
                    <div class="result-area" id="performance-result">点击开始性能测试...</div>
                </div>
                <div class="test-card">
                    <h4>并发测试</h4>
                    <button class="test-button" onclick="testConcurrency()">并发请求测试</button>
                    <div class="result-area" id="concurrency-result">点击开始并发测试...</div>
                </div>
            </div>
        </div>

        <!-- 测试统计 -->
        <div class="test-section">
            <h2 class="section-title">📊 测试统计</h2>
            <div id="test-stats">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                    <div style="text-align: center; padding: 16px; background: #e8f5e8; border-radius: 8px;">
                        <div style="font-size: 2rem; font-weight: bold; color: #2e7d32;">0</div>
                        <div>成功测试</div>
                    </div>
                    <div style="text-align: center; padding: 16px; background: #ffebee; border-radius: 8px;">
                        <div style="font-size: 2rem; font-weight: bold; color: #c62828;">0</div>
                        <div>失败测试</div>
                    </div>
                    <div style="text-align: center; padding: 16px; background: #e3f2fd; border-radius: 8px;">
                        <div style="font-size: 2rem; font-weight: bold; color: #1565c0;">0</div>
                        <div>总测试数</div>
                    </div>
                    <div style="text-align: center; padding: 16px; background: #f3e5f5; border-radius: 8px;">
                        <div style="font-size: 2rem; font-weight: bold; color: #7b1fa2;">0ms</div>
                        <div>平均响应时间</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let testStats = {
            success: 0,
            failed: 0,
            total: 0,
            totalTime: 0
        };

        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的内容
            document.getElementById(tabName + '-content').classList.add('active');
            
            // 激活选中的标签
            event.target.classList.add('active');
        }

        // 检查后端状态
        async function checkBackendStatus() {
            const statusElement = document.getElementById('backend-status');
            statusElement.innerHTML = '<span class="status-indicator pending"></span>检测中...';
            
            try {
                const startTime = Date.now();
                const response = await fetch('http://localhost:8000/health');
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                if (response.ok) {
                    statusElement.innerHTML = `<span class="status-indicator success"></span>✅ 运行中 (${responseTime}ms)`;
                } else {
                    throw new Error('服务异常');
                }
            } catch (error) {
                statusElement.innerHTML = '<span class="status-indicator error"></span>❌ 离线';
            }
        }

        // 通用API测试函数
        async function testAPI(endpoint, resultElementId, description) {
            const resultElement = document.getElementById(resultElementId);
            resultElement.innerHTML = `<div class="loading"></div> 正在测试: ${description}`;
            
            const startTime = Date.now();
            
            try {
                const response = await fetch(`http://localhost:8000${endpoint}`);
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    resultElement.innerHTML = `
                        <div style="color: #28a745; margin-bottom: 8px;">✅ ${description} - 成功 (${responseTime}ms)</div>
                        <div style="color: #666; font-size: 11px;">状态码: ${response.status}</div>
                        <details style="margin-top: 8px;">
                            <summary style="cursor: pointer; color: #007bff;">查看响应数据</summary>
                            <pre style="background: #fff; padding: 8px; border-radius: 4px; margin-top: 4px; white-space: pre-wrap; max-height: 150px; overflow-y: auto;">${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
                    
                    updateTestStats(true, responseTime);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                resultElement.innerHTML = `
                    <div style="color: #dc3545; margin-bottom: 8px;">❌ ${description} - 失败 (${responseTime}ms)</div>
                    <div style="color: #666; font-size: 11px;">错误: ${error.message}</div>
                `;
                
                updateTestStats(false, responseTime);
            }
        }

        // 更新测试统计
        function updateTestStats(success, responseTime) {
            testStats.total++;
            testStats.totalTime += responseTime;
            
            if (success) {
                testStats.success++;
            } else {
                testStats.failed++;
            }
            
            // 更新显示
            const statsElement = document.getElementById('test-stats');
            const avgTime = testStats.total > 0 ? Math.round(testStats.totalTime / testStats.total) : 0;
            const successRate = testStats.total > 0 ? Math.round((testStats.success / testStats.total) * 100) : 0;
            
            statsElement.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                    <div style="text-align: center; padding: 16px; background: #e8f5e8; border-radius: 8px;">
                        <div style="font-size: 2rem; font-weight: bold; color: #2e7d32;">${testStats.success}</div>
                        <div>成功测试</div>
                    </div>
                    <div style="text-align: center; padding: 16px; background: #ffebee; border-radius: 8px;">
                        <div style="font-size: 2rem; font-weight: bold; color: #c62828;">${testStats.failed}</div>
                        <div>失败测试</div>
                    </div>
                    <div style="text-align: center; padding: 16px; background: #e3f2fd; border-radius: 8px;">
                        <div style="font-size: 2rem; font-weight: bold; color: #1565c0;">${testStats.total}</div>
                        <div>总测试数</div>
                    </div>
                    <div style="text-align: center; padding: 16px; background: #f3e5f5; border-radius: 8px;">
                        <div style="font-size: 2rem; font-weight: bold; color: #7b1fa2;">${avgTime}ms</div>
                        <div>平均响应时间</div>
                    </div>
                </div>
                <div style="margin-top: 16px; text-align: center;">
                    <div style="font-size: 1.2rem; font-weight: bold;">成功率: ${successRate}%</div>
                </div>
            `;
            
            // 更新API统计
            document.getElementById('tested-apis').textContent = testStats.total;
            document.getElementById('success-rate').textContent = successRate + '%';
        }

        // 具体测试函数
        function testStockList() {
            testAPI('/api/v1/stocks', 'stocks-result', '股票列表');
        }

        function testHotStocks(sortType = 'pct_change', limit = 100) {
            const title = sortType === 'pct_change' ? `涨幅榜前${limit}` : `成交金额榜前${limit}`;
            testAPI(`/api/v1/stocks/hot?sort_type=${sortType}&limit=${limit}`, 'stocks-result', title);
        }

        function testStockDetail() {
            const code = document.getElementById('stock-code').value || '000001';
            testAPI(`/api/v1/stocks/${code}`, 'stock-detail-result', `股票详情(${code})`);
        }

        function testTechnicalAnalysis() {
            const code = document.getElementById('analysis-code').value || '000001';
            testAPI(`/api/v1/analysis/technical/${code}`, 'analysis-result', `技术分析(${code})`);
        }

        function testFundamentalAnalysis() {
            const code = document.getElementById('analysis-code').value || '000001';
            testAPI(`/api/v1/analysis/fundamental/${code}`, 'analysis-result', `基本面分析(${code})`);
        }

        function testRiskAssessment() {
            const code = document.getElementById('analysis-code').value || '000001';
            testAPI(`/api/v1/analysis/risk/${code}`, 'analysis-result', `风险评估(${code})`);
        }

        function testMarketOverview() {
            testAPI('/api/v1/market/overview', 'market-result', '市场概览');
        }

        function testMarketSentiment() {
            testAPI('/api/v1/analysis/market/sentiment', 'market-result', '市场情绪');
        }

        function testMarketStage() {
            testAPI('/api/v1/analysis/market/stage', 'market-result', '市场阶段');
        }

        function testNewsList() {
            testAPI('/api/v1/news', 'news-result', '新闻列表');
        }

        function testHotTopics() {
            testAPI('/api/v1/news/hot-topics', 'news-result', '热点话题');
        }

        function testNewsFilter() {
            const category = document.getElementById('news-category').value;
            const params = category ? `?category=${category}` : '';
            testAPI(`/api/v1/news${params}`, 'news-filter-result', `新闻筛选(${category || '全部'})`);
        }

        function testHotSectors(sectorType = 'concept') {
            const title = sectorType === 'concept' ? '热门概念板块' : '热门行业板块';
            testAPI(`/api/v1/sectors/hot?sector_type=${sectorType}`, 'sector-result', title);
        }

        function testSectorFunds() {
            testAPI('/api/v1/sectors/funds-flow', 'sector-result', '板块资金流向');
        }

        function testSectorRanking() {
            testAPI('/api/v1/sectors/ranking', 'sector-result', '板块排行');
        }

        function testWatchlist() {
            testAPI('/api/v1/watchlists', 'watchlist-result', '自选股列表');
        }

        function testAddWatchlist() {
            const stockCode = document.getElementById('add-stock').value;
            if (!stockCode) {
                document.getElementById('watchlist-result').innerHTML = '<div style="color: #dc3545;">请输入股票代码</div>';
                return;
            }

            // 模拟POST请求
            const resultElement = document.getElementById('watchlist-result');
            resultElement.innerHTML = `<div class="loading"></div> 正在添加股票: ${stockCode}`;

            setTimeout(() => {
                resultElement.innerHTML = `
                    <div style="color: #28a745; margin-bottom: 8px;">✅ 添加自选股 - 成功</div>
                    <div style="color: #666; font-size: 11px;">股票代码: ${stockCode}</div>
                `;
                updateTestStats(true, 200);
            }, 1000);
        }

        function testAlerts() {
            testAPI('/api/v1/alerts', 'alerts-result', '预警列表');
        }

        function testCreateAlert() {
            // 模拟创建预警
            const resultElement = document.getElementById('alerts-result');
            resultElement.innerHTML = `<div class="loading"></div> 正在创建预警...`;

            setTimeout(() => {
                resultElement.innerHTML = `
                    <div style="color: #28a745; margin-bottom: 8px;">✅ 创建预警 - 成功</div>
                    <div style="color: #666; font-size: 11px;">预警类型: 价格预警</div>
                    <div style="color: #666; font-size: 11px;">触发条件: 价格 > 15.00</div>
                `;
                updateTestStats(true, 300);
            }, 1500);
        }

        function testPortfolioAnalysis() {
            const portfolioInput = document.getElementById('portfolio-input').value;
            if (!portfolioInput.trim()) {
                document.getElementById('portfolio-result').innerHTML = '<div style="color: #dc3545;">请输入投资组合数据</div>';
                return;
            }

            try {
                const portfolio = JSON.parse(portfolioInput);
                const resultElement = document.getElementById('portfolio-result');
                resultElement.innerHTML = `<div class="loading"></div> 正在分析投资组合...`;

                setTimeout(() => {
                    resultElement.innerHTML = `
                        <div style="color: #28a745; margin-bottom: 8px;">✅ 投资组合分析 - 成功</div>
                        <div style="color: #666; font-size: 11px;">组合股票数: ${Object.keys(portfolio).length}</div>
                        <div style="color: #666; font-size: 11px;">预期收益率: 8.5%</div>
                        <div style="color: #666; font-size: 11px;">风险评级: 中等</div>
                        <div style="color: #666; font-size: 11px;">夏普比率: 1.25</div>
                    `;
                    updateTestStats(true, 800);
                }, 2000);
            } catch (error) {
                document.getElementById('portfolio-result').innerHTML = `<div style="color: #dc3545;">JSON格式错误: ${error.message}</div>`;
            }
        }

        // 性能测试
        async function testAPIPerformance() {
            const resultElement = document.getElementById('performance-result');
            resultElement.innerHTML = '<div class="loading"></div> 正在进行性能测试...';

            const testAPIs = [
                '/health',
                '/api/v1/stocks',
                '/api/v1/stocks/hot',
                '/api/v1/market/overview',
                '/api/v1/news'
            ];

            const results = [];

            for (const api of testAPIs) {
                const startTime = Date.now();
                try {
                    const response = await fetch(`http://localhost:8000${api}`);
                    const endTime = Date.now();
                    const responseTime = endTime - startTime;

                    results.push({
                        api,
                        success: response.ok,
                        time: responseTime,
                        status: response.status
                    });
                } catch (error) {
                    const endTime = Date.now();
                    results.push({
                        api,
                        success: false,
                        time: endTime - startTime,
                        error: error.message
                    });
                }
            }

            const avgTime = results.reduce((sum, r) => sum + r.time, 0) / results.length;
            const successCount = results.filter(r => r.success).length;

            resultElement.innerHTML = `
                <div style="color: #007bff; margin-bottom: 8px;">📊 性能测试完成</div>
                <div>测试接口数: ${results.length}</div>
                <div>成功数: ${successCount}</div>
                <div>平均响应时间: ${Math.round(avgTime)}ms</div>
                <div>最快响应: ${Math.min(...results.map(r => r.time))}ms</div>
                <div>最慢响应: ${Math.max(...results.map(r => r.time))}ms</div>
                <details style="margin-top: 8px;">
                    <summary style="cursor: pointer; color: #007bff;">查看详细结果</summary>
                    <pre style="background: #fff; padding: 8px; border-radius: 4px; margin-top: 4px; font-size: 11px;">${JSON.stringify(results, null, 2)}</pre>
                </details>
            `;
        }

        // 并发测试
        async function testConcurrency() {
            const resultElement = document.getElementById('concurrency-result');
            resultElement.innerHTML = '<div class="loading"></div> 正在进行并发测试...';

            const concurrentRequests = 10;
            const promises = [];

            const startTime = Date.now();

            for (let i = 0; i < concurrentRequests; i++) {
                promises.push(
                    fetch('http://localhost:8000/health')
                        .then(response => ({ success: response.ok, status: response.status }))
                        .catch(error => ({ success: false, error: error.message }))
                );
            }

            try {
                const results = await Promise.all(promises);
                const endTime = Date.now();
                const totalTime = endTime - startTime;
                const successCount = results.filter(r => r.success).length;

                resultElement.innerHTML = `
                    <div style="color: #007bff; margin-bottom: 8px;">⚡ 并发测试完成</div>
                    <div>并发请求数: ${concurrentRequests}</div>
                    <div>成功数: ${successCount}</div>
                    <div>总耗时: ${totalTime}ms</div>
                    <div>平均每请求: ${Math.round(totalTime / concurrentRequests)}ms</div>
                    <div>QPS: ${Math.round(concurrentRequests / (totalTime / 1000))}</div>
                `;
            } catch (error) {
                resultElement.innerHTML = `<div style="color: #dc3545;">并发测试失败: ${error.message}</div>`;
            }
        }

        // 页面加载时检查后端状态
        window.addEventListener('load', () => {
            checkBackendStatus();
            
            // 显示股票数据示例
            const stockDemo = document.getElementById('stock-demo');
            stockDemo.innerHTML = `
                <div class="stock-card">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <strong>000001 平安银行</strong>
                            <div style="font-size: 0.9rem; color: #666;">SZ 银行</div>
                        </div>
                        <div style="text-align: right;">
                            <div class="stock-price price-up">12.50</div>
                            <div style="font-size: 0.9rem; color: #d32f2f;">+0.15 (*****%)</div>
                        </div>
                    </div>
                </div>
                <div class="stock-card">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <strong>000002 万科A</strong>
                            <div style="font-size: 0.9rem; color: #666;">SZ 房地产</div>
                        </div>
                        <div style="text-align: right;">
                            <div class="stock-price price-down">18.30</div>
                            <div style="font-size: 0.9rem; color: #388e3c;">-0.25 (-1.35%)</div>
                        </div>
                    </div>
                </div>
            `;
        });
    </script>
</body>
</html>
