"""
新闻采集相关的 Celery 任务
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from app.core.celery_app import celery_app
from celery.schedules import crontab
from app.core.database import AsyncSessionLocal
from app.news_collector.news_service import NewsService
from app.news_collector.news_sources import news_source_manager
from app.websocket.connection_manager import connection_manager

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, max_retries=3)
def collect_news_task(self, max_articles_per_source: int = 50) -> Dict[str, Any]:
    """
    新闻采集任务

    Args:
        max_articles_per_source: 每个源最大文章数

    Returns:
        采集结果统计
    """
    try:
        logger.info(f"开始新闻采集任务，每源最大文章数: {max_articles_per_source}")

        # 使用异步函数的同步包装
        import asyncio

        async def _collect():
            async with AsyncSessionLocal() as db:
                service = NewsService(db)
                return await service.collect_and_process_news(max_articles_per_source)

        # 运行异步任务
        result = asyncio.run(_collect())

        logger.info(f"新闻采集任务完成: {result}")

        # 如果有新新闻，推送通知
        if result.get('saved_count', 0) > 0:
            asyncio.run(_push_news_notification(result))

        return result

    except Exception as e:
        logger.error(f"新闻采集任务失败: {e}")

        # 重试机制
        if self.request.retries < self.max_retries:
            logger.info(f"任务重试 {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=60 * (self.request.retries + 1))

        return {
            "error": str(e),
            "collected_count": 0,
            "processed_count": 0,
            "saved_count": 0,
            "error_count": 1
        }


@celery_app.task(bind=True)
def collect_news_by_source_task(self, source_name: str, max_articles: int = 50) -> Dict[str, Any]:
    """
    按源采集新闻任务

    Args:
        source_name: 新闻源名称
        max_articles: 最大文章数

    Returns:
        采集结果
    """
    try:
        logger.info(f"开始采集新闻源: {source_name}")

        import asyncio
        from app.news_collector.news_crawler import NewsCrawler
        from app.news_collector.news_analyzer import news_analyzer

        async def _collect_source():
            source = news_source_manager.get_source(source_name)
            if not source:
                raise ValueError(f"新闻源不存在: {source_name}")

            async with NewsCrawler() as crawler:
                articles = await crawler.crawl_source(source, max_articles)

            # 分析文章
            analyzed_articles = []
            for article in articles:
                analysis = news_analyzer.analyze_article(article)
                article['analysis'] = analysis
                analyzed_articles.append(article)

            return analyzed_articles

        articles = asyncio.run(_collect_source())

        result = {
            "source_name": source_name,
            "collected_count": len(articles),
            "articles": articles[:10]  # 只返回前10篇作为示例
        }

        logger.info(f"新闻源采集完成: {source_name}, 采集到 {len(articles)} 篇文章")
        return result

    except Exception as e:
        logger.error(f"新闻源采集失败 {source_name}: {e}")
        return {
            "source_name": source_name,
            "error": str(e),
            "collected_count": 0,
            "articles": []
        }


@celery_app.task
def analyze_news_sentiment_task(news_ids: List[str]) -> Dict[str, Any]:
    """
    新闻情感分析任务

    Args:
        news_ids: 新闻ID列表

    Returns:
        分析结果
    """
    try:
        logger.info(f"开始分析 {len(news_ids)} 篇新闻的情感")

        import asyncio
        from app.models.news import News
        from app.news_collector.news_analyzer import news_analyzer
        from sqlalchemy import select

        async def _analyze_sentiment():
            async with AsyncSessionLocal() as db:
                results = []

                for news_id in news_ids:
                    try:
                        # 获取新闻
                        stmt = select(News).where(News.id == news_id)
                        result = await db.execute(stmt)
                        news = result.scalar_one_or_none()

                        if not news:
                            continue

                        # 分析情感
                        full_text = f"{news.title} {news.summary} {news.content}".strip()
                        sentiment = news_analyzer.analyze_sentiment(full_text)

                        # 更新数据库
                        news.sentiment_score = sentiment['score']
                        news.sentiment_label = sentiment['label']

                        results.append({
                            "news_id": news_id,
                            "sentiment": sentiment
                        })

                    except Exception as e:
                        logger.error(f"分析新闻情感失败 {news_id}: {e}")

                await db.commit()
                return results

        results = asyncio.run(_analyze_sentiment())

        logger.info(f"情感分析完成，处理了 {len(results)} 篇新闻")
        return {
            "analyzed_count": len(results),
            "results": results
        }

    except Exception as e:
        logger.error(f"情感分析任务失败: {e}")
        return {
            "error": str(e),
            "analyzed_count": 0,
            "results": []
        }


@celery_app.task
def generate_news_summary_task(time_range_hours: int = 24) -> Dict[str, Any]:
    """
    生成新闻摘要任务

    Args:
        time_range_hours: 时间范围（小时）

    Returns:
        新闻摘要
    """
    try:
        logger.info(f"开始生成最近 {time_range_hours} 小时的新闻摘要")

        import asyncio

        async def _generate_summary():
            async with AsyncSessionLocal() as db:
                service = NewsService(db)

                # 获取时间范围内的新闻分析
                end_time = datetime.utcnow()
                start_time = end_time - timedelta(hours=time_range_hours)

                analytics = await service.get_news_analytics(start_time, end_time)

                # 获取热门新闻
                hot_news = await service.get_hot_news(limit=10, hours=time_range_hours)

                # 生成摘要
                summary = {
                    "time_range": {
                        "start": start_time.isoformat(),
                        "end": end_time.isoformat(),
                        "hours": time_range_hours
                    },
                    "statistics": analytics,
                    "hot_news": hot_news,
                    "key_insights": _extract_key_insights(analytics, hot_news),
                    "generated_at": datetime.utcnow().isoformat()
                }

                return summary

        summary = asyncio.run(_generate_summary())

        logger.info("新闻摘要生成完成")
        return summary

    except Exception as e:
        logger.error(f"生成新闻摘要失败: {e}")
        return {
            "error": str(e),
            "time_range": {
                "hours": time_range_hours
            },
            "generated_at": datetime.utcnow().isoformat()
        }


@celery_app.task
def cleanup_old_news_task(days_to_keep: int = 30) -> Dict[str, Any]:
    """
    清理旧新闻任务

    Args:
        days_to_keep: 保留天数

    Returns:
        清理结果
    """
    try:
        logger.info(f"开始清理 {days_to_keep} 天前的旧新闻")

        import asyncio
        from app.models.news import News
        from sqlalchemy import delete

        async def _cleanup():
            async with AsyncSessionLocal() as db:
                cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)

                # 删除旧新闻
                stmt = delete(News).where(
                    News.published_at < cutoff_date,
                    News.importance < 3  # 保留重要新闻
                )

                result = await db.execute(stmt)
                deleted_count = result.rowcount

                await db.commit()

                return deleted_count

        deleted_count = asyncio.run(_cleanup())

        result = {
            "deleted_count": deleted_count,
            "days_to_keep": days_to_keep,
            "cleanup_date": datetime.utcnow().isoformat()
        }

        logger.info(f"旧新闻清理完成: {result}")
        return result

    except Exception as e:
        logger.error(f"清理旧新闻失败: {e}")
        return {
            "error": str(e),
            "deleted_count": 0,
            "days_to_keep": days_to_keep
        }


@celery_app.task
def update_news_keywords_task() -> Dict[str, Any]:
    """
    更新新闻关键词任务

    Returns:
        更新结果
    """
    try:
        logger.info("开始更新新闻关键词")

        import asyncio
        from app.models.news import News, NewsKeyword
        from app.news_collector.news_analyzer import news_analyzer
        from sqlalchemy import select
        from collections import Counter

        async def _update_keywords():
            async with AsyncSessionLocal() as db:
                # 获取最近的新闻
                recent_date = datetime.utcnow() - timedelta(days=7)
                stmt = select(News).where(News.published_at >= recent_date)
                result = await db.execute(stmt)
                recent_news = result.scalars().all()

                # 提取所有关键词
                all_keywords = []
                for news in recent_news:
                    full_text = f"{news.title} {news.summary} {news.content}".strip()
                    keywords = news_analyzer.extract_keywords(full_text, top_k=10)
                    all_keywords.extend([kw['word'] for kw in keywords])

                # 统计关键词频率
                keyword_freq = Counter(all_keywords)

                # 更新关键词表
                updated_count = 0
                for word, freq in keyword_freq.items():
                    # 查找现有关键词
                    stmt = select(NewsKeyword).where(NewsKeyword.keyword == word)
                    result = await db.execute(stmt)
                    existing = result.scalar_one_or_none()

                    if existing:
                        existing.frequency = freq
                        existing.last_seen = datetime.utcnow()
                    else:
                        new_keyword = NewsKeyword(
                            keyword=word,
                            category='auto',
                            weight=1.0,
                            frequency=freq,
                            last_seen=datetime.utcnow()
                        )
                        db.add(new_keyword)

                    updated_count += 1

                await db.commit()
                return updated_count

        updated_count = asyncio.run(_update_keywords())

        result = {
            "updated_keywords": updated_count,
            "update_date": datetime.utcnow().isoformat()
        }

        logger.info(f"关键词更新完成: {result}")
        return result

    except Exception as e:
        logger.error(f"更新关键词失败: {e}")
        return {
            "error": str(e),
            "updated_keywords": 0
        }


def _extract_key_insights(analytics: Dict[str, Any], hot_news: List[Dict[str, Any]]) -> List[str]:
    """提取关键洞察"""
    insights = []

    try:
        # 情感分析洞察
        sentiment_dist = analytics.get('sentiment_analysis', {}).get('distribution', {})
        total_news = analytics.get('total_news', 0)

        if total_news > 0:
            positive_pct = (sentiment_dist.get('positive', 0) / total_news) * 100
            negative_pct = (sentiment_dist.get('negative', 0) / total_news) * 100

            if positive_pct > 60:
                insights.append(f"市场情绪偏向乐观，{positive_pct:.1f}% 的新闻呈正面情感")
            elif negative_pct > 60:
                insights.append(f"市场情绪偏向悲观，{negative_pct:.1f}% 的新闻呈负面情感")
            else:
                insights.append("市场情绪相对平衡")

        # 重要新闻洞察
        high_importance_news = [news for news in hot_news if news.get('importance', 1) >= 4]
        if high_importance_news:
            insights.append(f"发现 {len(high_importance_news)} 条高重要性新闻")

        # 来源分布洞察
        source_dist = analytics.get('source_distribution', {})
        if source_dist:
            top_source = max(source_dist, key=source_dist.get)
            insights.append(f"主要新闻来源: {top_source} ({source_dist[top_source]} 条)")

    except Exception as e:
        logger.error(f"提取关键洞察失败: {e}")
        insights.append("洞察分析暂时不可用")

    return insights


async def _push_news_notification(result: Dict[str, Any]):
    """推送新闻通知"""
    try:
        if result.get('saved_count', 0) > 0:
            notification = {
                "type": "news_update",
                "message": f"新增 {result['saved_count']} 条新闻",
                "data": result,
                "timestamp": datetime.utcnow().isoformat()
            }

            # 推送到所有订阅新闻的客户端
            await connection_manager.push_news_data("all", notification)

    except Exception as e:
        logger.error(f"推送新闻通知失败: {e}")


# 定期任务配置
@celery_app.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    """设置定期任务"""

    # 每30分钟采集一次新闻
    sender.add_periodic_task(
        30 * 60,  # 30分钟
        collect_news_task.s(max_articles_per_source=20),
        name='定期新闻采集'
    )

    # 每天凌晨2点清理旧新闻
    sender.add_periodic_task(
        crontab(hour=2, minute=0),
        cleanup_old_news_task.s(days_to_keep=30),
        name='清理旧新闻'
    )

    # 每天凌晨3点更新关键词
    sender.add_periodic_task(
        crontab(hour=3, minute=0),
        update_news_keywords_task.s(),
        name='更新新闻关键词'
    )

    # 每小时生成新闻摘要
    sender.add_periodic_task(
        60 * 60,  # 1小时
        generate_news_summary_task.s(time_range_hours=1),
        name='生成新闻摘要'
    )
