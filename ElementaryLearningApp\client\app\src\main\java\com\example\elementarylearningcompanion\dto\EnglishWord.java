package com.example.elementarylearningcompanion.dto;

public class EnglishWord {
    private Long id;
    private Integer textbookVersionId;
    private Integer grade;
    private String unit;
    private String word;
    private String phonetic;
    private String meaningChinese;
    private String partOfSpeech;
    private String exampleSentence;
    private String exampleTranslation;
    private String audioUrl;
    private String imageUrl;
    private Integer difficultyLevel;
    private String createdAt;

    // Constructors
    public EnglishWord() {}

    public EnglishWord(String word, String meaningChinese) {
        this.word = word;
        this.meaningChinese = meaningChinese;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getTextbookVersionId() {
        return textbookVersionId;
    }

    public void setTextbookVersionId(Integer textbookVersionId) {
        this.textbookVersionId = textbookVersionId;
    }

    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getWord() {
        return word;
    }

    public void setWord(String word) {
        this.word = word;
    }

    public String getPhonetic() {
        return phonetic;
    }

    public void setPhonetic(String phonetic) {
        this.phonetic = phonetic;
    }

    public String getMeaningChinese() {
        return meaningChinese;
    }

    public void setMeaningChinese(String meaningChinese) {
        this.meaningChinese = meaningChinese;
    }

    public String getPartOfSpeech() {
        return partOfSpeech;
    }

    public void setPartOfSpeech(String partOfSpeech) {
        this.partOfSpeech = partOfSpeech;
    }

    public String getExampleSentence() {
        return exampleSentence;
    }

    public void setExampleSentence(String exampleSentence) {
        this.exampleSentence = exampleSentence;
    }

    public String getExampleTranslation() {
        return exampleTranslation;
    }

    public void setExampleTranslation(String exampleTranslation) {
        this.exampleTranslation = exampleTranslation;
    }

    public String getAudioUrl() {
        return audioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Integer getDifficultyLevel() {
        return difficultyLevel;
    }

    public void setDifficultyLevel(Integer difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }
}
