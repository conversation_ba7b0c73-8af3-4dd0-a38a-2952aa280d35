/**
 * 新闻页面
 */

import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Row,
  Col,
  Card,
  List,
  Typography,
  Space,
  Button,
  Input,
  Select,
  Tag,
  Avatar,
  Divider,
  Skeleton,
  Empty,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  LikeOutlined,
  ShareAltOutlined,
  FireOutlined,
  TrendingUpOutlined,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '@/store';
import {
  fetchNewsAsync,
  fetchHotTopicsAsync,
  selectNews,
  selectNewsLoading,
  selectHotTopics,
  selectHotTopicsLoading,
} from '@/store/slices/newsSlice';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;

interface NewsItem {
  id: string;
  title: string;
  summary: string;
  content: string;
  source: string;
  author: string;
  publish_time: string;
  url: string;
  category: string;
  tags: string[];
  sentiment: 'positive' | 'negative' | 'neutral';
  importance: number;
  view_count: number;
  like_count: number;
}

interface HotTopic {
  topic: string;
  count: number;
  trend: 'up' | 'down' | 'stable';
  heat_score: number;
}

const NewsPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  // 状态
  const news = useAppSelector(selectNews);
  const newsLoading = useAppSelector(selectNewsLoading);
  const hotTopics = useAppSelector(selectHotTopics);
  const hotTopicsLoading = useAppSelector(selectHotTopicsLoading);
  
  // 本地状态
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSentiment, setSelectedSentiment] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('publish_time');

  // 初始化数据
  useEffect(() => {
    const params = {
      limit: 50,
      category: selectedCategory !== 'all' ? selectedCategory : undefined,
      sentiment: selectedSentiment !== 'all' ? selectedSentiment : undefined,
      search: searchText || undefined,
      sort_by: sortBy,
    };
    
    dispatch(fetchNewsAsync(params));
    dispatch(fetchHotTopicsAsync({ limit: 20 }));
  }, [dispatch, selectedCategory, selectedSentiment, searchText, sortBy]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  // 刷新数据
  const handleRefresh = () => {
    const params = {
      limit: 50,
      category: selectedCategory !== 'all' ? selectedCategory : undefined,
      sentiment: selectedSentiment !== 'all' ? selectedSentiment : undefined,
      search: searchText || undefined,
      sort_by: sortBy,
    };
    
    dispatch(fetchNewsAsync(params));
    dispatch(fetchHotTopicsAsync({ limit: 20 }));
  };

  // 格式化时间
  const formatTime = (timeStr: string) => {
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now.getTime() - time.getTime();
    
    if (diff < 60000) {
      return '刚刚';
    } else if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return time.toLocaleDateString('zh-CN');
    }
  };

  // 获取情绪标签颜色
  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return 'green';
      case 'negative':
        return 'red';
      default:
        return 'default';
    }
  };

  // 获取情绪标签文本
  const getSentimentText = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return '利好';
      case 'negative':
        return '利空';
      default:
        return '中性';
    }
  };

  // 获取趋势图标
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUpOutlined style={{ color: '#52c41a' }} />;
      case 'down':
        return <TrendingUpOutlined style={{ color: '#ff4d4f', transform: 'rotate(180deg)' }} />;
      default:
        return <TrendingUpOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  return (
    <div>
      {/* 页面标题 */}
      <div className="flex-between" style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          财经新闻
        </Title>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={newsLoading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 筛选工具栏 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索新闻标题或内容"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={8} sm={4} md={3}>
            <Select
              value={selectedCategory}
              onChange={setSelectedCategory}
              style={{ width: '100%' }}
            >
              <Option value="all">全部分类</Option>
              <Option value="market">市场动态</Option>
              <Option value="policy">政策法规</Option>
              <Option value="company">公司新闻</Option>
              <Option value="macro">宏观经济</Option>
            </Select>
          </Col>
          <Col xs={8} sm={4} md={3}>
            <Select
              value={selectedSentiment}
              onChange={setSelectedSentiment}
              style={{ width: '100%' }}
            >
              <Option value="all">全部情绪</Option>
              <Option value="positive">利好</Option>
              <Option value="negative">利空</Option>
              <Option value="neutral">中性</Option>
            </Select>
          </Col>
          <Col xs={8} sm={4} md={3}>
            <Select
              value={sortBy}
              onChange={setSortBy}
              style={{ width: '100%' }}
            >
              <Option value="publish_time">发布时间</Option>
              <Option value="importance">重要程度</Option>
              <Option value="view_count">阅读量</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      <Row gutter={[24, 24]}>
        {/* 左侧新闻列表 */}
        <Col xs={24} lg={16}>
          <Card title="最新新闻">
            <List
              loading={newsLoading}
              dataSource={news}
              locale={{
                emptyText: <Empty description="暂无新闻" />,
              }}
              renderItem={(item: NewsItem) => (
                <List.Item
                  key={item.id}
                  className="cursor-pointer"
                  onClick={() => navigate(`/news/${item.id}`)}
                  style={{ padding: '16px 0' }}
                >
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        size={64}
                        shape="square"
                        style={{ backgroundColor: '#f0f0f0' }}
                        icon={<FireOutlined />}
                      />
                    }
                    title={
                      <div>
                        <div className="flex-between">
                          <Text strong className="news-title" style={{ fontSize: 16 }}>
                            {item.title}
                          </Text>
                          <Tag color={getSentimentColor(item.sentiment)}>
                            {getSentimentText(item.sentiment)}
                          </Tag>
                        </div>
                        <Space size="small" style={{ marginTop: 4 }}>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            {item.source}
                          </Text>
                          <Divider type="vertical" />
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            <ClockCircleOutlined /> {formatTime(item.publish_time)}
                          </Text>
                        </Space>
                      </div>
                    }
                    description={
                      <div>
                        <Paragraph
                          ellipsis={{ rows: 2 }}
                          style={{ margin: '8px 0', color: '#666' }}
                        >
                          {item.summary}
                        </Paragraph>
                        <div className="flex-between">
                          <Space size="small">
                            {item.tags?.slice(0, 3).map((tag, index) => (
                              <Tag key={index} size="small">
                                {tag}
                              </Tag>
                            ))}
                          </Space>
                          <Space size="middle">
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              <EyeOutlined /> {item.view_count || 0}
                            </Text>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              <LikeOutlined /> {item.like_count || 0}
                            </Text>
                          </Space>
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
              pagination={{
                total: news.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条新闻`,
              }}
            />
          </Card>
        </Col>

        {/* 右侧热点话题 */}
        <Col xs={24} lg={8}>
          <Card
            title={
              <Space>
                <FireOutlined />
                <span>热点话题</span>
              </Space>
            }
            loading={hotTopicsLoading}
          >
            <List
              dataSource={hotTopics}
              locale={{
                emptyText: <Empty description="暂无热点话题" />,
              }}
              renderItem={(topic: HotTopic, index) => (
                <List.Item style={{ padding: '8px 0' }}>
                  <div className="flex-between full-width">
                    <div className="flex-center">
                      <div
                        style={{
                          width: 20,
                          height: 20,
                          borderRadius: '50%',
                          background: index < 3 ? '#ff4d4f' : '#d9d9d9',
                          color: '#fff',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontSize: 10,
                          fontWeight: 'bold',
                          marginRight: 8,
                        }}
                      >
                        {index + 1}
                      </div>
                      <div>
                        <Text strong style={{ fontSize: 14 }}>
                          {topic.topic}
                        </Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {topic.count} 条相关新闻
                        </Text>
                      </div>
                    </div>
                    <div className="text-right">
                      {getTrendIcon(topic.trend)}
                      <br />
                      <Text type="secondary" style={{ fontSize: 10 }}>
                        热度 {topic.heat_score}
                      </Text>
                    </div>
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default NewsPage;
