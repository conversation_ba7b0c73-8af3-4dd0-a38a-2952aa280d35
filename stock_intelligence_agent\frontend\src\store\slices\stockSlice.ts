/**
 * 股票数据状态管理
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { message } from 'antd';
import { 
  Stock, 
  StockQuote, 
  RealtimeQuote, 
  HotStock, 
  MarketOverview,
  SearchResponse,
  TechnicalAnalysis,
  PaginationResponse
} from '@/types';
import { apiService } from '@/services/api';

// 状态接口
interface StockState {
  // 股票列表
  stocks: Stock[];
  stocksTotal: number;
  stocksLoading: boolean;
  
  // 当前股票
  currentStock: Stock | null;
  currentStockLoading: boolean;
  
  // 股票行情
  quotes: StockQuote[];
  quotesLoading: boolean;
  
  // 实时行情
  realtimeQuotes: Record<string, RealtimeQuote>;
  
  // 热门股票
  hotStocks: HotStock[];
  hotStocksLoading: boolean;
  
  // 市场概览
  marketOverview: MarketOverview | null;
  marketOverviewLoading: boolean;
  
  // 搜索结果
  searchResults: SearchResponse | null;
  searchLoading: boolean;
  
  // 技术分析
  technicalAnalysis: Record<string, TechnicalAnalysis>;
  technicalAnalysisLoading: boolean;
  
  // 错误信息
  error: string | null;
}

// 初始状态
const initialState: StockState = {
  stocks: [],
  stocksTotal: 0,
  stocksLoading: false,
  
  currentStock: null,
  currentStockLoading: false,
  
  quotes: [],
  quotesLoading: false,
  
  realtimeQuotes: {},
  
  hotStocks: [],
  hotStocksLoading: false,
  
  marketOverview: null,
  marketOverviewLoading: false,
  
  searchResults: null,
  searchLoading: false,
  
  technicalAnalysis: {},
  technicalAnalysisLoading: false,
  
  error: null,
};

// 异步 actions
export const fetchStocksAsync = createAsyncThunk(
  'stock/fetchStocks',
  async (params: {
    skip?: number;
    limit?: number;
    search?: string;
    market?: string;
    industry?: string;
  } = {}, { rejectWithValue }) => {
    try {
      return await apiService.getStocks(params);
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取股票列表失败';
      return rejectWithValue(errorMessage);
    }
  }
);

export const fetchStockAsync = createAsyncThunk(
  'stock/fetchStock',
  async (stockCode: string, { rejectWithValue }) => {
    try {
      return await apiService.getStock(stockCode);
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取股票信息失败';
      return rejectWithValue(errorMessage);
    }
  }
);

export const fetchStockQuotesAsync = createAsyncThunk(
  'stock/fetchStockQuotes',
  async (params: {
    stockCode: string;
    start_date?: string;
    end_date?: string;
    limit?: number;
  }, { rejectWithValue }) => {
    try {
      return await apiService.getStockQuotes(params.stockCode, {
        start_date: params.start_date,
        end_date: params.end_date,
        limit: params.limit,
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取股票行情失败';
      return rejectWithValue(errorMessage);
    }
  }
);

export const fetchRealtimeQuoteAsync = createAsyncThunk(
  'stock/fetchRealtimeQuote',
  async (stockCode: string, { rejectWithValue }) => {
    try {
      const quote = await apiService.getRealtimeQuote(stockCode);
      return { stockCode, quote };
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取实时行情失败';
      return rejectWithValue(errorMessage);
    }
  }
);

export const fetchHotStocksAsync = createAsyncThunk(
  'stock/fetchHotStocks',
  async (params: {
    limit?: number;
    sort_by?: string;
    order?: string;
  } = {}, { rejectWithValue }) => {
    try {
      return await apiService.getHotStocks(params);
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取热门股票失败';
      return rejectWithValue(errorMessage);
    }
  }
);

export const fetchMarketOverviewAsync = createAsyncThunk(
  'stock/fetchMarketOverview',
  async (_, { rejectWithValue }) => {
    try {
      return await apiService.getMarketOverview();
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取市场概览失败';
      return rejectWithValue(errorMessage);
    }
  }
);

export const searchStocksAsync = createAsyncThunk(
  'stock/searchStocks',
  async (query: string, { rejectWithValue }) => {
    try {
      return await apiService.searchStocks(query);
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '搜索股票失败';
      return rejectWithValue(errorMessage);
    }
  }
);

export const fetchTechnicalAnalysisAsync = createAsyncThunk(
  'stock/fetchTechnicalAnalysis',
  async (stockCode: string, { rejectWithValue }) => {
    try {
      const analysis = await apiService.getTechnicalAnalysis(stockCode);
      return { stockCode, analysis };
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取技术分析失败';
      return rejectWithValue(errorMessage);
    }
  }
);

// 创建 slice
const stockSlice = createSlice({
  name: 'stock',
  initialState,
  reducers: {
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
    
    // 更新实时行情
    updateRealtimeQuote: (state, action: PayloadAction<{ stockCode: string; quote: RealtimeQuote }>) => {
      const { stockCode, quote } = action.payload;
      state.realtimeQuotes[stockCode] = quote;
    },
    
    // 批量更新实时行情
    updateRealtimeQuotes: (state, action: PayloadAction<Record<string, RealtimeQuote>>) => {
      state.realtimeQuotes = { ...state.realtimeQuotes, ...action.payload };
    },
    
    // 清除搜索结果
    clearSearchResults: (state) => {
      state.searchResults = null;
    },
    
    // 设置当前股票
    setCurrentStock: (state, action: PayloadAction<Stock | null>) => {
      state.currentStock = action.payload;
    },
  },
  extraReducers: (builder) => {
    // 获取股票列表
    builder
      .addCase(fetchStocksAsync.pending, (state) => {
        state.stocksLoading = true;
        state.error = null;
      })
      .addCase(fetchStocksAsync.fulfilled, (state, action) => {
        state.stocksLoading = false;
        state.stocks = action.payload.data;
        state.stocksTotal = action.payload.total;
        state.error = null;
      })
      .addCase(fetchStocksAsync.rejected, (state, action) => {
        state.stocksLoading = false;
        state.error = action.payload as string;
      });

    // 获取股票详情
    builder
      .addCase(fetchStockAsync.pending, (state) => {
        state.currentStockLoading = true;
        state.error = null;
      })
      .addCase(fetchStockAsync.fulfilled, (state, action) => {
        state.currentStockLoading = false;
        state.currentStock = action.payload;
        state.error = null;
      })
      .addCase(fetchStockAsync.rejected, (state, action) => {
        state.currentStockLoading = false;
        state.error = action.payload as string;
      });

    // 获取股票行情
    builder
      .addCase(fetchStockQuotesAsync.pending, (state) => {
        state.quotesLoading = true;
        state.error = null;
      })
      .addCase(fetchStockQuotesAsync.fulfilled, (state, action) => {
        state.quotesLoading = false;
        state.quotes = action.payload;
        state.error = null;
      })
      .addCase(fetchStockQuotesAsync.rejected, (state, action) => {
        state.quotesLoading = false;
        state.error = action.payload as string;
      });

    // 获取实时行情
    builder
      .addCase(fetchRealtimeQuoteAsync.fulfilled, (state, action) => {
        const { stockCode, quote } = action.payload;
        state.realtimeQuotes[stockCode] = quote;
      });

    // 获取热门股票
    builder
      .addCase(fetchHotStocksAsync.pending, (state) => {
        state.hotStocksLoading = true;
        state.error = null;
      })
      .addCase(fetchHotStocksAsync.fulfilled, (state, action) => {
        state.hotStocksLoading = false;
        state.hotStocks = action.payload;
        state.error = null;
      })
      .addCase(fetchHotStocksAsync.rejected, (state, action) => {
        state.hotStocksLoading = false;
        state.error = action.payload as string;
      });

    // 获取市场概览
    builder
      .addCase(fetchMarketOverviewAsync.pending, (state) => {
        state.marketOverviewLoading = true;
        state.error = null;
      })
      .addCase(fetchMarketOverviewAsync.fulfilled, (state, action) => {
        state.marketOverviewLoading = false;
        state.marketOverview = action.payload;
        state.error = null;
      })
      .addCase(fetchMarketOverviewAsync.rejected, (state, action) => {
        state.marketOverviewLoading = false;
        state.error = action.payload as string;
      });

    // 搜索股票
    builder
      .addCase(searchStocksAsync.pending, (state) => {
        state.searchLoading = true;
        state.error = null;
      })
      .addCase(searchStocksAsync.fulfilled, (state, action) => {
        state.searchLoading = false;
        state.searchResults = action.payload;
        state.error = null;
      })
      .addCase(searchStocksAsync.rejected, (state, action) => {
        state.searchLoading = false;
        state.error = action.payload as string;
      });

    // 获取技术分析
    builder
      .addCase(fetchTechnicalAnalysisAsync.pending, (state) => {
        state.technicalAnalysisLoading = true;
        state.error = null;
      })
      .addCase(fetchTechnicalAnalysisAsync.fulfilled, (state, action) => {
        state.technicalAnalysisLoading = false;
        const { stockCode, analysis } = action.payload;
        state.technicalAnalysis[stockCode] = analysis;
        state.error = null;
      })
      .addCase(fetchTechnicalAnalysisAsync.rejected, (state, action) => {
        state.technicalAnalysisLoading = false;
        state.error = action.payload as string;
      });
  },
});

// 导出 actions
export const {
  clearError,
  updateRealtimeQuote,
  updateRealtimeQuotes,
  clearSearchResults,
  setCurrentStock,
} = stockSlice.actions;

// 选择器
export const selectStocks = (state: { stock: StockState }) => state.stock.stocks;
export const selectStocksTotal = (state: { stock: StockState }) => state.stock.stocksTotal;
export const selectStocksLoading = (state: { stock: StockState }) => state.stock.stocksLoading;
export const selectCurrentStock = (state: { stock: StockState }) => state.stock.currentStock;
export const selectCurrentStockLoading = (state: { stock: StockState }) => state.stock.currentStockLoading;
export const selectQuotes = (state: { stock: StockState }) => state.stock.quotes;
export const selectQuotesLoading = (state: { stock: StockState }) => state.stock.quotesLoading;
export const selectRealtimeQuotes = (state: { stock: StockState }) => state.stock.realtimeQuotes;
export const selectRealtimeQuote = (stockCode: string) => (state: { stock: StockState }) => 
  state.stock.realtimeQuotes[stockCode];
export const selectHotStocks = (state: { stock: StockState }) => state.stock.hotStocks;
export const selectHotStocksLoading = (state: { stock: StockState }) => state.stock.hotStocksLoading;
export const selectMarketOverview = (state: { stock: StockState }) => state.stock.marketOverview;
export const selectMarketOverviewLoading = (state: { stock: StockState }) => state.stock.marketOverviewLoading;
export const selectSearchResults = (state: { stock: StockState }) => state.stock.searchResults;
export const selectSearchLoading = (state: { stock: StockState }) => state.stock.searchLoading;
export const selectTechnicalAnalysis = (stockCode: string) => (state: { stock: StockState }) =>
  state.stock.technicalAnalysis[stockCode];
export const selectTechnicalAnalysisLoading = (state: { stock: StockState }) => state.stock.technicalAnalysisLoading;
export const selectStockError = (state: { stock: StockState }) => state.stock.error;

export default stockSlice.reducer;
