#!/usr/bin/env python3
"""
基于Playwright MCP的研报数据API
支持多数据源研报爬取和智能排序
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
import asyncio

from ..data_sources.research_crawler import research_crawler

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/research", tags=["research"])

@router.get("/reports/{stock_code}")
async def get_research_reports(
    stock_code: str,
    max_sources: int = Query(default=3, ge=1, le=5, description="最大数据源数量"),
    time_range: str = Query(default="1year", description="时间范围: 1week, 1month, 6months, 1year, 2years")
) -> Dict[str, Any]:
    """
    获取股票研报数据
    
    Args:
        stock_code: 股票代码 (如: 300750)
        max_sources: 最大尝试的数据源数量
        time_range: 时间范围
        
    Returns:
        Dict: 包含研报数据和统计信息的字典
    """
    try:
        logger.info(f"开始获取股票 {stock_code} 的研报数据")
        
        # 验证股票代码格式
        if not stock_code or len(stock_code) != 6 or not stock_code.isdigit():
            raise HTTPException(status_code=400, detail="股票代码格式错误，应为6位数字")
        
        # 获取研报数据
        result = await research_crawler.get_research_reports(
            code=stock_code,
            max_sources=max_sources
        )
        
        if not result.get("research_reports"):
            logger.warning(f"未获取到股票 {stock_code} 的研报数据")
            return {
                "success": False,
                "message": "未获取到研报数据",
                "data": {
                    "stock_code": stock_code,
                    "research_reports": [],
                    "rating_summary": {
                        "total_reports": 0,
                        "rating_distribution": {},
                        "avg_target_price": 0,
                        "consensus_rating": "无数据"
                    },
                    "analysis_time": datetime.now().isoformat(),
                    "data_source": "无可用数据源",
                    "source_details": {
                        "successful_sources": [],
                        "failed_sources": [],
                        "total_sources_tried": 0
                    }
                }
            }
        
        logger.info(f"成功获取股票 {stock_code} 的 {len(result['research_reports'])} 条研报数据")
        
        return {
            "success": True,
            "message": "研报数据获取成功",
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取研报数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get("/sources/status")
async def get_data_sources_status() -> Dict[str, Any]:
    """
    获取数据源状态信息
    
    Returns:
        Dict: 数据源状态信息
    """
    try:
        status = research_crawler.get_source_status()
        
        # 计算总体健康度
        total_sources = len(status)
        healthy_sources = sum(1 for s in status.values() if s['failure_count'] < 3)
        health_score = (healthy_sources / total_sources) * 100 if total_sources > 0 else 0
        
        return {
            "success": True,
            "data": {
                "sources": status,
                "summary": {
                    "total_sources": total_sources,
                    "healthy_sources": healthy_sources,
                    "health_score": round(health_score, 2),
                    "last_updated": datetime.now().isoformat()
                }
            }
        }
        
    except Exception as e:
        logger.error(f"获取数据源状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get("/ratings/distribution/{stock_code}")
async def get_rating_distribution(
    stock_code: str,
    days: int = Query(default=90, ge=7, le=365, description="统计天数")
) -> Dict[str, Any]:
    """
    获取股票评级分布统计
    
    Args:
        stock_code: 股票代码
        days: 统计天数
        
    Returns:
        Dict: 评级分布统计
    """
    try:
        logger.info(f"开始统计股票 {stock_code} 近 {days} 天的评级分布")
        
        # 获取研报数据
        result = await research_crawler.get_research_reports(code=stock_code)
        
        if not result.get("research_reports"):
            return {
                "success": False,
                "message": "无研报数据",
                "data": {}
            }
        
        reports = result["research_reports"]
        
        # 统计评级分布
        rating_stats = {}
        institution_stats = {}
        monthly_stats = {}
        
        for report in reports:
            rating = report.get("rating", "未知")
            institution = report.get("institution", "未知")
            date_str = report.get("publish_date", "")
            
            # 评级统计
            rating_stats[rating] = rating_stats.get(rating, 0) + 1
            
            # 机构统计
            institution_stats[institution] = institution_stats.get(institution, 0) + 1
            
            # 月度统计
            if date_str:
                try:
                    month_key = date_str[:7]  # YYYY-MM
                    monthly_stats[month_key] = monthly_stats.get(month_key, 0) + 1
                except:
                    pass
        
        # 计算一致性评级
        total_reports = len(reports)
        positive_ratings = rating_stats.get("买入", 0) + rating_stats.get("增持", 0) + rating_stats.get("强烈推荐", 0)
        consensus_score = (positive_ratings / total_reports) * 100 if total_reports > 0 else 0
        
        return {
            "success": True,
            "data": {
                "stock_code": stock_code,
                "period_days": days,
                "total_reports": total_reports,
                "rating_distribution": rating_stats,
                "institution_distribution": dict(sorted(institution_stats.items(), key=lambda x: x[1], reverse=True)[:10]),
                "monthly_distribution": dict(sorted(monthly_stats.items())),
                "consensus_analysis": {
                    "positive_ratio": round(consensus_score, 2),
                    "consensus_level": "强烈看好" if consensus_score >= 80 else "看好" if consensus_score >= 60 else "中性" if consensus_score >= 40 else "谨慎",
                    "most_common_rating": max(rating_stats.items(), key=lambda x: x[1])[0] if rating_stats else "无数据"
                },
                "analysis_time": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"获取评级分布失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.get("/trending")
async def get_trending_research() -> Dict[str, Any]:
    """
    获取热门研报趋势
    
    Returns:
        Dict: 热门研报趋势数据
    """
    try:
        # 热门股票代码列表（可以从数据库或配置文件获取）
        hot_stocks = ["300750", "000858", "002594", "300274", "688981"]
        
        trending_data = []
        
        for stock_code in hot_stocks:
            try:
                result = await research_crawler.get_research_reports(code=stock_code, max_sources=2)
                
                if result.get("research_reports"):
                    recent_reports = result["research_reports"][:3]  # 只取最近3条
                    rating_summary = result.get("rating_summary", {})
                    
                    trending_data.append({
                        "stock_code": stock_code,
                        "recent_reports_count": len(recent_reports),
                        "consensus_rating": rating_summary.get("consensus_rating", "中性"),
                        "avg_target_price": rating_summary.get("avg_target_price", 0),
                        "latest_report": recent_reports[0] if recent_reports else None,
                        "data_sources": result.get("source_details", {}).get("successful_sources", [])
                    })
                    
            except Exception as e:
                logger.warning(f"获取股票 {stock_code} 趋势数据失败: {e}")
                continue
        
        # 按研报数量排序
        trending_data.sort(key=lambda x: x["recent_reports_count"], reverse=True)
        
        return {
            "success": True,
            "data": {
                "trending_stocks": trending_data,
                "update_time": datetime.now().isoformat(),
                "total_stocks": len(trending_data)
            }
        }
        
    except Exception as e:
        logger.error(f"获取热门研报趋势失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

@router.post("/sources/refresh")
async def refresh_data_sources() -> Dict[str, Any]:
    """
    刷新数据源状态
    
    Returns:
        Dict: 刷新结果
    """
    try:
        # 重置失败计数
        for source_id in research_crawler.data_sources:
            research_crawler.data_sources[source_id]['failure_count'] = 0
            research_crawler.data_sources[source_id]['last_success'] = None
        
        logger.info("数据源状态已刷新")
        
        return {
            "success": True,
            "message": "数据源状态已刷新",
            "data": {
                "refresh_time": datetime.now().isoformat(),
                "sources_refreshed": list(research_crawler.data_sources.keys())
            }
        }
        
    except Exception as e:
        logger.error(f"刷新数据源状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
