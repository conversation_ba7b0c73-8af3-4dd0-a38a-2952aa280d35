"""
预警相关 API 端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.services.alert_service import AlertService
from app.auth.security import get_current_user_from_token
from app.schemas.alert import (
    AlertResponse,
    AlertCreateRequest,
    AlertUpdateRequest,
    AlertTriggerResponse
)
from app.schemas.base import BaseResponse, PaginationResponse

router = APIRouter()


@router.get("/", response_model=PaginationResponse[AlertResponse])
async def get_user_alerts(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    is_active: Optional[bool] = Query(None, description="是否活跃"),
    stock_code: Optional[str] = Query(None, description="股票代码"),
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取用户预警列表"""
    alert_service = AlertService(db)
    
    alerts, total = await alert_service.get_user_alerts(
        user_id=current_user["user_id"],
        skip=skip,
        limit=limit,
        is_active=is_active,
        stock_code=stock_code
    )
    
    return PaginationResponse(
        data=[AlertResponse.from_orm(alert) for alert in alerts],
        total=total,
        skip=skip,
        limit=limit,
        has_more=skip + len(alerts) < total
    )


@router.post("/", response_model=AlertResponse)
async def create_alert(
    alert_data: AlertCreateRequest,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """创建预警"""
    alert_service = AlertService(db)
    
    try:
        alert = await alert_service.create_alert(
            user_id=current_user["user_id"],
            name=alert_data.name,
            stock_code=alert_data.stock_code,
            alert_type=alert_data.alert_type,
            condition=alert_data.condition,
            notification_methods=alert_data.notification_methods
        )
        
        return AlertResponse.from_orm(alert)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/{alert_id}", response_model=AlertResponse)
async def get_alert_by_id(
    alert_id: str,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取预警详情"""
    alert_service = AlertService(db)
    alert = await alert_service.get_alert_by_id(
        alert_id=alert_id,
        user_id=current_user["user_id"]
    )
    
    if not alert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="预警不存在"
        )
    
    return AlertResponse.from_orm(alert)


@router.put("/{alert_id}", response_model=AlertResponse)
async def update_alert(
    alert_id: str,
    alert_data: AlertUpdateRequest,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """更新预警"""
    alert_service = AlertService(db)
    
    # 过滤掉空值
    update_data = {k: v for k, v in alert_data.dict().items() if v is not None}
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="没有提供更新数据"
        )
    
    try:
        alert = await alert_service.update_alert(
            alert_id=alert_id,
            user_id=current_user["user_id"],
            **update_data
        )
        
        if not alert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="预警不存在"
            )
        
        return AlertResponse.from_orm(alert)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{alert_id}", response_model=BaseResponse)
async def delete_alert(
    alert_id: str,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """删除预警"""
    alert_service = AlertService(db)
    success = await alert_service.delete_alert(
        alert_id=alert_id,
        user_id=current_user["user_id"]
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="预警不存在"
        )
    
    return BaseResponse(
        success=True,
        message="预警已删除"
    )


@router.post("/{alert_id}/toggle", response_model=AlertResponse)
async def toggle_alert(
    alert_id: str,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """切换预警状态"""
    alert_service = AlertService(db)
    
    try:
        alert = await alert_service.toggle_alert(
            alert_id=alert_id,
            user_id=current_user["user_id"]
        )
        
        if not alert:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="预警不存在"
            )
        
        return AlertResponse.from_orm(alert)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="切换预警状态失败"
        )


@router.get("/triggers/", response_model=PaginationResponse[AlertTriggerResponse])
async def get_alert_triggers(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(50, ge=1, le=100, description="返回的记录数"),
    is_read: Optional[bool] = Query(None, description="是否已读"),
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取预警触发记录"""
    alert_service = AlertService(db)
    
    triggers, total = await alert_service.get_alert_triggers(
        user_id=current_user["user_id"],
        skip=skip,
        limit=limit,
        is_read=is_read
    )
    
    return PaginationResponse(
        data=[AlertTriggerResponse.from_orm(trigger) for trigger in triggers],
        total=total,
        skip=skip,
        limit=limit,
        has_more=skip + len(triggers) < total
    )


@router.post("/triggers/{trigger_id}/read", response_model=BaseResponse)
async def mark_trigger_as_read(
    trigger_id: str,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """标记触发记录为已读"""
    alert_service = AlertService(db)
    success = await alert_service.mark_trigger_as_read(
        trigger_id=trigger_id,
        user_id=current_user["user_id"]
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="触发记录不存在"
        )
    
    return BaseResponse(
        success=True,
        message="已标记为已读"
    )


@router.post("/check", response_model=dict)
async def trigger_alert_check(
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """手动触发预警检查"""
    # 检查用户权限
    if not current_user.get("is_superuser", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    alert_service = AlertService(db)
    result = await alert_service.check_alerts()
    
    return {
        "success": True,
        "message": "预警检查完成",
        "result": result
    }


@router.get("/types/", response_model=List[dict])
async def get_alert_types(
    current_user: dict = Depends(get_current_user_from_token)
):
    """获取预警类型列表"""
    alert_types = [
        {
            "type": "price",
            "name": "价格预警",
            "description": "股价达到指定值时触发",
            "fields": [
                {"name": "operator", "type": "select", "options": [">=", "<=", ">", "<", "=="], "required": True},
                {"name": "value", "type": "number", "required": True, "min": 0}
            ]
        },
        {
            "type": "change",
            "name": "涨跌额预警",
            "description": "涨跌金额达到指定值时触发",
            "fields": [
                {"name": "operator", "type": "select", "options": [">=", "<=", ">", "<", "=="], "required": True},
                {"name": "value", "type": "number", "required": True}
            ]
        },
        {
            "type": "pct_change",
            "name": "涨跌幅预警",
            "description": "涨跌幅达到指定百分比时触发",
            "fields": [
                {"name": "operator", "type": "select", "options": [">=", "<=", ">", "<", "=="], "required": True},
                {"name": "value", "type": "number", "required": True, "min": -100, "max": 100}
            ]
        },
        {
            "type": "volume",
            "name": "成交量预警",
            "description": "成交量达到指定值时触发",
            "fields": [
                {"name": "operator", "type": "select", "options": [">=", "<=", ">", "<", "=="], "required": True},
                {"name": "value", "type": "number", "required": True, "min": 0}
            ]
        },
        {
            "type": "technical",
            "name": "技术指标预警",
            "description": "技术指标达到指定条件时触发",
            "fields": [
                {"name": "indicator", "type": "select", "options": ["ma5", "ma10", "ma20", "rsi", "macd"], "required": True},
                {"name": "operator", "type": "select", "options": [">=", "<=", ">", "<", "=="], "required": True},
                {"name": "value", "type": "number", "required": True}
            ]
        }
    ]
    
    return alert_types


@router.get("/notification-methods/", response_model=List[dict])
async def get_notification_methods(
    current_user: dict = Depends(get_current_user_from_token)
):
    """获取通知方式列表"""
    methods = [
        {
            "method": "websocket",
            "name": "实时推送",
            "description": "通过WebSocket实时推送到客户端",
            "enabled": True
        },
        {
            "method": "email",
            "name": "邮件通知",
            "description": "发送邮件通知",
            "enabled": False  # TODO: 实现邮件通知
        },
        {
            "method": "sms",
            "name": "短信通知",
            "description": "发送短信通知",
            "enabled": False  # TODO: 实现短信通知
        },
        {
            "method": "push",
            "name": "推送通知",
            "description": "移动端推送通知",
            "enabled": False  # TODO: 实现推送通知
        }
    ]
    
    return methods
