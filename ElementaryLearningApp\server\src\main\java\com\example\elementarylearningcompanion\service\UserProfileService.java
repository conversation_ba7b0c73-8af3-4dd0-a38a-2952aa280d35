package com.example.elementarylearningcompanion.service;

import com.example.elementarylearningcompanion.model.Student;
import com.example.elementarylearningcompanion.model.Parent;
import com.example.elementarylearningcompanion.repository.StudentRepository;
import com.example.elementarylearningcompanion.repository.ParentRepository;
import com.example.elementarylearningcompanion.repository.LearningRecordRepository;
import com.example.elementarylearningcompanion.repository.StudentAnswerRepository;
import com.example.elementarylearningcompanion.repository.WrongQuestionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import java.util.Map;
import java.util.HashMap;

@Service
public class UserProfileService {

    @Autowired
    private StudentRepository studentRepository;

    @Autowired
    private ParentRepository parentRepository;

    @Autowired
    private LearningRecordRepository learningRecordRepository;

    @Autowired
    private StudentAnswerRepository studentAnswerRepository;

    @Autowired
    private WrongQuestionRepository wrongQuestionRepository;

    public Optional<Student> getStudentProfile(Long studentId) {
        return studentRepository.findById(studentId);
    }

    public Optional<Parent> getParentProfile(Long parentId) {
        return parentRepository.findById(parentId);
    }

    @Transactional
    public boolean updateStudentProfile(Long studentId, String name, Integer grade, Integer textbookVersionId) {
        Optional<Student> studentOpt = studentRepository.findById(studentId);
        if (studentOpt.isPresent()) {
            Student student = studentOpt.get();
            if (name != null && !name.trim().isEmpty()) {
                student.setName(name.trim());
            }
            if (grade != null && grade >= 1 && grade <= 6) {
                student.setGrade(grade);
            }
            if (textbookVersionId != null) {
                student.setTextbookVersionId(textbookVersionId);
            }
            studentRepository.save(student);
            return true;
        }
        return false;
    }

    @Transactional
    public boolean updateParentProfile(Long parentId, String name, String phone, String email) {
        Optional<Parent> parentOpt = parentRepository.findById(parentId);
        if (parentOpt.isPresent()) {
            Parent parent = parentOpt.get();
            if (name != null && !name.trim().isEmpty()) {
                parent.setName(name.trim());
            }
            if (phone != null && !phone.trim().isEmpty()) {
                parent.setPhone(phone.trim());
            }
            if (email != null && !email.trim().isEmpty()) {
                parent.setEmail(email.trim());
            }
            parentRepository.save(parent);
            return true;
        }
        return false;
    }

    public Map<String, Object> getStudentLearningStatistics(Long studentId) {
        Map<String, Object> stats = new HashMap<>();
        
        // Get basic student info
        Optional<Student> studentOpt = studentRepository.findById(studentId);
        if (studentOpt.isPresent()) {
            Student student = studentOpt.get();
            stats.put("studentName", student.getName());
            stats.put("grade", student.getGrade());
            stats.put("textbookVersionId", student.getTextbookVersionId());
        }

        // Calculate time periods
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime weekStart = now.minus(7, ChronoUnit.DAYS);
        LocalDateTime monthStart = now.minus(30, ChronoUnit.DAYS);

        // Learning time statistics
        Long weeklyStudyTime = learningRecordRepository.getTotalStudyTime(studentId, weekStart, now);
        Long monthlyStudyTime = learningRecordRepository.getTotalStudyTime(studentId, monthStart, now);
        
        stats.put("weeklyStudyTime", weeklyStudyTime != null ? weeklyStudyTime : 0);
        stats.put("monthlyStudyTime", monthlyStudyTime != null ? monthlyStudyTime : 0);

        // Accuracy statistics
        Long correctAnswers = learningRecordRepository.countCorrectAnswers(studentId, "math_exercise");
        Long wrongAnswers = learningRecordRepository.countWrongAnswers(studentId, "math_exercise");
        
        if (correctAnswers + wrongAnswers > 0) {
            double accuracy = (double) correctAnswers / (correctAnswers + wrongAnswers) * 100;
            stats.put("mathAccuracy", Math.round(accuracy * 100.0) / 100.0);
        } else {
            stats.put("mathAccuracy", 0.0);
        }

        // English word mastery
        correctAnswers = learningRecordRepository.countCorrectAnswers(studentId, "english_word");
        wrongAnswers = learningRecordRepository.countWrongAnswers(studentId, "english_word");
        
        if (correctAnswers + wrongAnswers > 0) {
            double accuracy = (double) correctAnswers / (correctAnswers + wrongAnswers) * 100;
            stats.put("englishAccuracy", Math.round(accuracy * 100.0) / 100.0);
        } else {
            stats.put("englishAccuracy", 0.0);
        }

        // Wrong questions count
        Long mathWrongCount = wrongQuestionRepository.countUnmasteredQuestions(studentId, "math_exercise");
        Long englishWrongCount = wrongQuestionRepository.countUnmasteredQuestions(studentId, "english_word");
        Long generalWrongCount = wrongQuestionRepository.countUnmasteredQuestions(studentId, "question");
        
        stats.put("wrongQuestions", Map.of(
            "math", mathWrongCount,
            "english", englishWrongCount,
            "general", generalWrongCount,
            "total", mathWrongCount + englishWrongCount + generalWrongCount
        ));

        // Recent activity
        stats.put("lastActiveDate", now.toString());
        
        return stats;
    }

    public Map<String, Object> getParentDashboard(Long parentId) {
        Map<String, Object> dashboard = new HashMap<>();
        
        // Get parent info
        Optional<Parent> parentOpt = parentRepository.findById(parentId);
        if (parentOpt.isPresent()) {
            Parent parent = parentOpt.get();
            dashboard.put("parentName", parent.getName());
            dashboard.put("parentEmail", parent.getEmail());
            dashboard.put("parentPhone", parent.getPhone());
            
            // Get children statistics
            // Note: This would require a relationship between Parent and Student
            // For now, we'll use a placeholder
            dashboard.put("childrenCount", 1);
            dashboard.put("children", Map.of(
                "totalStudyTime", 0,
                "averageAccuracy", 0.0,
                "totalWrongQuestions", 0
            ));
        }
        
        return dashboard;
    }

    public Map<String, Object> getAppSettings(Long userId) {
        Map<String, Object> settings = new HashMap<>();
        
        // Default app settings
        settings.put("notifications", Map.of(
            "studyReminder", true,
            "progressReport", true,
            "wrongQuestionReview", true
        ));
        
        settings.put("display", Map.of(
            "fontSize", "medium",
            "theme", "light",
            "language", "zh-CN"
        ));
        
        settings.put("learning", Map.of(
            "autoPlayAudio", true,
            "showExplanations", true,
            "difficultyAdjustment", "auto"
        ));
        
        return settings;
    }

    @Transactional
    public boolean updateAppSettings(Long userId, Map<String, Object> settings) {
        // In a real implementation, you would save these settings to a user_settings table
        // For now, we'll just return true to indicate success
        return true;
    }

    public Map<String, Object> getAchievements(Long studentId) {
        Map<String, Object> achievements = new HashMap<>();
        
        // Calculate achievements based on learning records
        Long totalCorrectAnswers = learningRecordRepository.countCorrectAnswers(studentId, "math_exercise") +
                                  learningRecordRepository.countCorrectAnswers(studentId, "english_word");
        
        achievements.put("totalCorrectAnswers", totalCorrectAnswers);
        
        // Achievement badges
        achievements.put("badges", Map.of(
            "mathMaster", totalCorrectAnswers >= 100,
            "englishExplorer", totalCorrectAnswers >= 50,
            "consistentLearner", true, // Would check for consecutive days
            "problemSolver", wrongQuestionRepository.countUnmasteredQuestions(studentId, "question") == 0
        ));
        
        return achievements;
    }
}
