package com.example.elementarylearningcompanion.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "math_exercises")
public class MathExercise {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private Integer textbookVersionId;

    @Column(nullable = false)
    private Integer grade;

    private String chapter;

    @Column(nullable = false)
    private String exerciseType; // addition, subtraction, multiplication, division

    @Column(nullable = false)
    private Integer difficultyLevel = 1; // 1-5

    @Column(nullable = false)
    private String question; // e.g., "3 + 5 = ?"

    @Column(nullable = false)
    private String correctAnswer;

    @Column(columnDefinition = "JSON")
    private String options; // JSON format for multiple choice options

    @Column(columnDefinition = "TEXT")
    private String explanation;

    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }

    // Constructors
    public MathExercise() {}

    public MathExercise(Integer textbookVersionId, Integer grade, String exerciseType, 
                       Integer difficultyLevel, String question, String correctAnswer) {
        this.textbookVersionId = textbookVersionId;
        this.grade = grade;
        this.exerciseType = exerciseType;
        this.difficultyLevel = difficultyLevel;
        this.question = question;
        this.correctAnswer = correctAnswer;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getTextbookVersionId() {
        return textbookVersionId;
    }

    public void setTextbookVersionId(Integer textbookVersionId) {
        this.textbookVersionId = textbookVersionId;
    }

    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public String getChapter() {
        return chapter;
    }

    public void setChapter(String chapter) {
        this.chapter = chapter;
    }

    public String getExerciseType() {
        return exerciseType;
    }

    public void setExerciseType(String exerciseType) {
        this.exerciseType = exerciseType;
    }

    public Integer getDifficultyLevel() {
        return difficultyLevel;
    }

    public void setDifficultyLevel(Integer difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getCorrectAnswer() {
        return correctAnswer;
    }

    public void setCorrectAnswer(String correctAnswer) {
        this.correctAnswer = correctAnswer;
    }

    public String getOptions() {
        return options;
    }

    public void setOptions(String options) {
        this.options = options;
    }

    public String getExplanation() {
        return explanation;
    }

    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
}
