"""
API v1 路由汇总
"""

from fastapi import APIRouter

from app.api.v1.endpoints import stocks, health, sectors, analysis
from app.api.v1 import auth, news, alerts, users, watchlists
from app.websocket import websocket_router

api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(
    health.router,
    prefix="/health",
    tags=["health"],
)

api_router.include_router(
    stocks.router,
    prefix="/stocks",
    tags=["stocks"],
)

api_router.include_router(
    sectors.router,
    prefix="/sectors",
    tags=["sectors"],
)

api_router.include_router(
    news.router,
    prefix="/news",
    tags=["news"],
)

api_router.include_router(
    analysis.router,
    prefix="/analysis",
    tags=["analysis"],
)

api_router.include_router(
    alerts.router,
    prefix="/alerts",
    tags=["alerts"],
)

api_router.include_router(
    websocket_router,
    prefix="/websocket",
    tags=["websocket"],
)
