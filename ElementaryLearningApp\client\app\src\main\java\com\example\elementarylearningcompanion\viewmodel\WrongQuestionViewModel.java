package com.example.elementarylearningcompanion.viewmodel;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.example.elementarylearningcompanion.dto.ApiResponse;
import com.example.elementarylearningcompanion.dto.WrongQuestion;
import com.example.elementarylearningcompanion.network.RetrofitClient;

import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class WrongQuestionViewModel extends ViewModel {

    private MutableLiveData<List<WrongQuestion>> wrongQuestions = new MutableLiveData<>();
    private MutableLiveData<List<Map<String, Object>>> detailedWrongQuestions = new MutableLiveData<>();
    private MutableLiveData<Map<String, Object>> statistics = new MutableLiveData<>();
    private MutableLiveData<Boolean> isLoading = new MutableLiveData<>();
    private MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private MutableLiveData<ApiResponse> operationResult = new MutableLiveData<>();

    public LiveData<List<WrongQuestion>> getWrongQuestions() {
        return wrongQuestions;
    }

    public LiveData<List<Map<String, Object>>> getDetailedWrongQuestions() {
        return detailedWrongQuestions;
    }

    public LiveData<Map<String, Object>> getStatistics() {
        return statistics;
    }

    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public LiveData<ApiResponse> getOperationResult() {
        return operationResult;
    }

    public void loadAllWrongQuestions(long studentId) {
        isLoading.setValue(true);
        RetrofitClient.getApiService().getAllWrongQuestions(studentId).enqueue(new Callback<List<WrongQuestion>>() {
            @Override
            public void onResponse(Call<List<WrongQuestion>> call, Response<List<WrongQuestion>> response) {
                isLoading.setValue(false);
                if (response.isSuccessful() && response.body() != null) {
                    wrongQuestions.setValue(response.body());
                } else {
                    errorMessage.setValue("加载错题本失败");
                }
            }

            @Override
            public void onFailure(Call<List<WrongQuestion>> call, Throwable t) {
                isLoading.setValue(false);
                errorMessage.setValue("网络错误：" + t.getMessage());
            }
        });
    }

    public void loadWrongQuestionsByType(long studentId, String contentType) {
        isLoading.setValue(true);
        RetrofitClient.getApiService().getWrongQuestionsByType(studentId, contentType)
                .enqueue(new Callback<List<WrongQuestion>>() {
                    @Override
                    public void onResponse(Call<List<WrongQuestion>> call, Response<List<WrongQuestion>> response) {
                        isLoading.setValue(false);
                        if (response.isSuccessful() && response.body() != null) {
                            wrongQuestions.setValue(response.body());
                        } else {
                            errorMessage.setValue("加载错题失败");
                        }
                    }

                    @Override
                    public void onFailure(Call<List<WrongQuestion>> call, Throwable t) {
                        isLoading.setValue(false);
                        errorMessage.setValue("网络错误：" + t.getMessage());
                    }
                });
    }

    public void loadDetailedWrongQuestions(long studentId) {
        isLoading.setValue(true);
        RetrofitClient.getApiService().getWrongQuestionsWithDetails(studentId)
                .enqueue(new Callback<List<Map<String, Object>>>() {
                    @Override
                    public void onResponse(Call<List<Map<String, Object>>> call, Response<List<Map<String, Object>>> response) {
                        isLoading.setValue(false);
                        if (response.isSuccessful() && response.body() != null) {
                            detailedWrongQuestions.setValue(response.body());
                        } else {
                            errorMessage.setValue("加载详细错题失败");
                        }
                    }

                    @Override
                    public void onFailure(Call<List<Map<String, Object>>> call, Throwable t) {
                        isLoading.setValue(false);
                        errorMessage.setValue("网络错误：" + t.getMessage());
                    }
                });
    }

    public void loadStatistics(long studentId) {
        RetrofitClient.getApiService().getWrongQuestionStatistics(studentId)
                .enqueue(new Callback<Map<String, Object>>() {
                    @Override
                    public void onResponse(Call<Map<String, Object>> call, Response<Map<String, Object>> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            statistics.setValue(response.body());
                        } else {
                            errorMessage.setValue("加载统计信息失败");
                        }
                    }

                    @Override
                    public void onFailure(Call<Map<String, Object>> call, Throwable t) {
                        errorMessage.setValue("网络错误：" + t.getMessage());
                    }
                });
    }

    public void markAsMastered(long studentId, long wrongQuestionId) {
        RetrofitClient.getApiService().markWrongQuestionAsMastered(studentId, wrongQuestionId)
                .enqueue(new Callback<ApiResponse>() {
                    @Override
                    public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            operationResult.setValue(response.body());
                            if (response.body().isSuccess()) {
                                // Refresh the list
                                loadAllWrongQuestions(studentId);
                            }
                        } else {
                            operationResult.setValue(new ApiResponse(false, "标记失败"));
                        }
                    }

                    @Override
                    public void onFailure(Call<ApiResponse> call, Throwable t) {
                        operationResult.setValue(new ApiResponse(false, "网络错误：" + t.getMessage()));
                    }
                });
    }

    public void deleteWrongQuestion(long studentId, long wrongQuestionId) {
        RetrofitClient.getApiService().deleteWrongQuestion(studentId, wrongQuestionId)
                .enqueue(new Callback<ApiResponse>() {
                    @Override
                    public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            operationResult.setValue(response.body());
                            if (response.body().isSuccess()) {
                                // Refresh the list
                                loadAllWrongQuestions(studentId);
                            }
                        } else {
                            operationResult.setValue(new ApiResponse(false, "删除失败"));
                        }
                    }

                    @Override
                    public void onFailure(Call<ApiResponse> call, Throwable t) {
                        operationResult.setValue(new ApiResponse(false, "网络错误：" + t.getMessage()));
                    }
                });
    }

    public void clearOperationResult() {
        operationResult.setValue(null);
    }

    public void clearErrorMessage() {
        errorMessage.setValue(null);
    }
}
