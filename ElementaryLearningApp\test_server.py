#!/usr/bin/env python3
"""
简单的测试服务器 - 用于验证Android客户端网络连接
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse

class TestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(b'<h1>Elementary Learning App Test Server</h1><p>Server is running!</p>')
        
        elif self.path == '/api/auth/test':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            response = {
                "success": True,
                "message": "Test server is working!",
                "timestamp": "2025-01-16T23:30:00"
            }
            self.wfile.write(json.dumps(response).encode())

        elif self.path == '/api/math/practice-types':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            response = [
                {
                    "type": "addition",
                    "name": "加法练习",
                    "description": "练习两位数以内的加法运算"
                },
                {
                    "type": "subtraction",
                    "name": "减法练习",
                    "description": "练习两位数以内的减法运算"
                },
                {
                    "type": "multiplication",
                    "name": "乘法练习",
                    "description": "练习九九乘法表"
                },
                {
                    "type": "division",
                    "name": "除法练习",
                    "description": "练习简单的除法运算"
                }
            ]
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

        elif self.path.startswith('/api/math/exercises/generate'):
            # 解析查询参数
            from urllib.parse import urlparse, parse_qs
            parsed_url = urlparse(self.path)
            params = parse_qs(parsed_url.query)

            exercise_type = params.get('exerciseType', ['addition'])[0]
            difficulty = int(params.get('difficultyLevel', ['1'])[0])
            count = int(params.get('count', ['5'])[0])

            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()

            import random
            exercises = []
            for i in range(count):
                if exercise_type == 'addition':
                    a = random.randint(1, 50)
                    b = random.randint(1, 50)
                    question = f"{a} + {b} = ?"
                    answer = str(a + b)
                    explanation = f"{a} + {b} = {a + b}"
                elif exercise_type == 'subtraction':
                    a = random.randint(10, 99)
                    b = random.randint(1, a)
                    question = f"{a} - {b} = ?"
                    answer = str(a - b)
                    explanation = f"{a} - {b} = {a - b}"
                elif exercise_type == 'multiplication':
                    a = random.randint(1, 9)
                    b = random.randint(1, 9)
                    question = f"{a} × {b} = ?"
                    answer = str(a * b)
                    explanation = f"{a} × {b} = {a * b}"
                else:  # division
                    b = random.randint(2, 9)
                    result = random.randint(1, 9)
                    a = b * result
                    question = f"{a} ÷ {b} = ?"
                    answer = str(result)
                    explanation = f"{a} ÷ {b} = {result}"

                exercises.append({
                    "id": i + 1,
                    "question": question,
                    "correctAnswer": answer,
                    "explanation": explanation,
                    "difficultyLevel": difficulty,
                    "exerciseType": exercise_type
                })

            self.wfile.write(json.dumps(exercises, ensure_ascii=False).encode('utf-8'))

        elif self.path.startswith('/api/content/textbooks'):
            # 教材版本API
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()

            response = [
                {
                    "id": 1,
                    "name": "人教版",
                    "subject": "语文",
                    "description": "人民教育出版社语文教材"
                },
                {
                    "id": 2,
                    "name": "苏教版",
                    "subject": "语文",
                    "description": "江苏教育出版社语文教材"
                },
                {
                    "id": 3,
                    "name": "北师大版",
                    "subject": "语文",
                    "description": "北京师范大学出版社语文教材"
                },
                {
                    "id": 4,
                    "name": "人教版",
                    "subject": "数学",
                    "description": "人民教育出版社数学教材"
                },
                {
                    "id": 5,
                    "name": "苏教版",
                    "subject": "数学",
                    "description": "江苏教育出版社数学教材"
                },
                {
                    "id": 6,
                    "name": "人教版",
                    "subject": "英语",
                    "description": "人民教育出版社英语教材"
                },
                {
                    "id": 7,
                    "name": "外研版",
                    "subject": "英语",
                    "description": "外语教学与研究出版社英语教材"
                }
            ]
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

        elif self.path.startswith('/api/content/lessons'):
            # 课程列表API
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()

            response = [
                {
                    "id": 1,
                    "title": "春天的故事",
                    "lessonNumber": 1,
                    "grade": 1,
                    "textbookVersionId": 1
                },
                {
                    "id": 2,
                    "title": "小蝌蚪找妈妈",
                    "lessonNumber": 2,
                    "grade": 1,
                    "textbookVersionId": 1
                },
                {
                    "id": 3,
                    "title": "荷花",
                    "lessonNumber": 3,
                    "grade": 1,
                    "textbookVersionId": 1
                }
            ]
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

        else:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b'Not Found')

    def do_POST(self):
        """处理POST请求"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        if self.path == '/api/auth/register':
            # 模拟注册响应
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response = {
                "success": True,
                "message": "注册成功！(测试服务器)",
                "data": {
                    "userId": 1,
                    "token": "test_token_123"
                }
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        
        elif self.path == '/api/auth/login':
            # 模拟登录响应
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response = {
                "success": True,
                "message": "登录成功！(测试服务器)",
                "data": {
                    "userId": 1,
                    "token": "test_token_123"
                }
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

        elif self.path == '/api/math/submit-answer':
            # 模拟数学答案提交 - 总是返回正确以避免验证问题
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()

            # 暂时总是返回正确，避免输入法和验证问题
            response = {
                "success": True,
                "message": "回答正确！继续加油！",
                "data": {
                    "isCorrect": True,
                    "pointsEarned": 10
                }
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

        else:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b'API Not Found')

    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{self.date_time_string()}] {format % args}")

def run_server():
    """启动测试服务器"""
    server_address = ('', 8080)
    httpd = HTTPServer(server_address, TestHandler)
    
    print("=" * 50)
    print("小学学习伴侣 - 测试服务器")
    print("=" * 50)
    print(f"服务器启动在: http://localhost:8080")
    print(f"Android模拟器访问: http://********:8080")
    print("按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n服务器已停止")
        httpd.server_close()

if __name__ == '__main__':
    run_server()
