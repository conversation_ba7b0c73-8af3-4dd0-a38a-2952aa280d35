#!/usr/bin/env python3
"""
简单的测试服务器 - 用于验证Android客户端网络连接
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse

class TestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(b'<h1>Elementary Learning App Test Server</h1><p>Server is running!</p>')
        
        elif self.path == '/api/auth/test':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            response = {
                "success": True,
                "message": "Test server is working!",
                "timestamp": "2025-01-16T23:30:00"
            }
            self.wfile.write(json.dumps(response).encode())
        
        else:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b'Not Found')

    def do_POST(self):
        """处理POST请求"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        if self.path == '/api/auth/register':
            # 模拟注册响应
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response = {
                "success": True,
                "message": "注册成功！(测试服务器)",
                "data": {
                    "userId": 1,
                    "token": "test_token_123"
                }
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        
        elif self.path == '/api/auth/login':
            # 模拟登录响应
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response = {
                "success": True,
                "message": "登录成功！(测试服务器)",
                "data": {
                    "userId": 1,
                    "token": "test_token_123"
                }
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        
        else:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b'API Not Found')

    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{self.date_time_string()}] {format % args}")

def run_server():
    """启动测试服务器"""
    server_address = ('', 8080)
    httpd = HTTPServer(server_address, TestHandler)
    
    print("=" * 50)
    print("小学学习伴侣 - 测试服务器")
    print("=" * 50)
    print(f"服务器启动在: http://localhost:8080")
    print(f"Android模拟器访问: http://********:8080")
    print("按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n服务器已停止")
        httpd.server_close()

if __name__ == '__main__':
    run_server()
