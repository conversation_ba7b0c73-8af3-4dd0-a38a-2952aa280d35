/**
 * 自选股相关状态管理
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../index';
import { watchlistApi } from '@/services/api';

// 类型定义
export interface WatchlistItem {
  id: string;
  stock_code: string;
  stock_name: string;
  market: string;
  industry: string;
  close_price: number;
  change: number;
  pct_change: number;
  volume: number;
  amount: number;
  turnover_rate: number;
  pe_ratio: number;
  pb_ratio: number;
  added_time: string;
  alert_enabled: boolean;
  alert_price_high?: number;
  alert_price_low?: number;
}

export interface WatchlistState {
  // 自选股列表
  watchlist: WatchlistItem[];
  watchlistLoading: boolean;
  watchlistError: string | null;
  
  // 操作状态
  addLoading: boolean;
  removeLoading: boolean;
  updateLoading: boolean;
  
  // 筛选和排序
  filters: {
    search: string;
    sort_by: string;
    sort_order: 'asc' | 'desc';
  };
}

// 初始状态
const initialState: WatchlistState = {
  watchlist: [],
  watchlistLoading: false,
  watchlistError: null,
  
  addLoading: false,
  removeLoading: false,
  updateLoading: false,
  
  filters: {
    search: '',
    sort_by: 'added_time',
    sort_order: 'desc',
  },
};

// 异步 actions
export const fetchWatchlistAsync = createAsyncThunk(
  'watchlist/fetchWatchlist',
  async () => {
    const response = await watchlistApi.getWatchlist();
    return response.data;
  }
);

export const addToWatchlistAsync = createAsyncThunk(
  'watchlist/addToWatchlist',
  async (stockCode: string) => {
    const response = await watchlistApi.addToWatchlist(stockCode);
    return response.data;
  }
);

export const removeFromWatchlistAsync = createAsyncThunk(
  'watchlist/removeFromWatchlist',
  async (id: string) => {
    await watchlistApi.removeFromWatchlist(id);
    return id;
  }
);

export const updateWatchlistItemAsync = createAsyncThunk(
  'watchlist/updateWatchlistItem',
  async (params: { id: string; data: Partial<WatchlistItem> }) => {
    const response = await watchlistApi.updateWatchlistItem(params.id, params.data);
    return response.data;
  }
);

export const batchRemoveFromWatchlistAsync = createAsyncThunk(
  'watchlist/batchRemoveFromWatchlist',
  async (ids: string[]) => {
    await Promise.all(ids.map(id => watchlistApi.removeFromWatchlist(id)));
    return ids;
  }
);

// Slice
const watchlistSlice = createSlice({
  name: 'watchlist',
  initialState,
  reducers: {
    // 设置筛选条件
    setFilters: (state, action: PayloadAction<Partial<WatchlistState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    // 清除错误
    clearErrors: (state) => {
      state.watchlistError = null;
    },
    
    // 本地更新股价（WebSocket推送）
    updateStockPrices: (state, action: PayloadAction<{ [stockCode: string]: Partial<WatchlistItem> }>) => {
      const updates = action.payload;
      state.watchlist = state.watchlist.map(item => {
        const update = updates[item.stock_code];
        return update ? { ...item, ...update } : item;
      });
    },
  },
  extraReducers: (builder) => {
    // 获取自选股列表
    builder
      .addCase(fetchWatchlistAsync.pending, (state) => {
        state.watchlistLoading = true;
        state.watchlistError = null;
      })
      .addCase(fetchWatchlistAsync.fulfilled, (state, action) => {
        state.watchlistLoading = false;
        state.watchlist = action.payload;
      })
      .addCase(fetchWatchlistAsync.rejected, (state, action) => {
        state.watchlistLoading = false;
        state.watchlistError = action.error.message || '获取自选股失败';
      });
    
    // 添加到自选股
    builder
      .addCase(addToWatchlistAsync.pending, (state) => {
        state.addLoading = true;
      })
      .addCase(addToWatchlistAsync.fulfilled, (state, action) => {
        state.addLoading = false;
        state.watchlist.unshift(action.payload);
      })
      .addCase(addToWatchlistAsync.rejected, (state, action) => {
        state.addLoading = false;
        state.watchlistError = action.error.message || '添加自选股失败';
      });
    
    // 移除自选股
    builder
      .addCase(removeFromWatchlistAsync.pending, (state) => {
        state.removeLoading = true;
      })
      .addCase(removeFromWatchlistAsync.fulfilled, (state, action) => {
        state.removeLoading = false;
        state.watchlist = state.watchlist.filter(item => item.id !== action.payload);
      })
      .addCase(removeFromWatchlistAsync.rejected, (state, action) => {
        state.removeLoading = false;
        state.watchlistError = action.error.message || '移除自选股失败';
      });
    
    // 更新自选股
    builder
      .addCase(updateWatchlistItemAsync.pending, (state) => {
        state.updateLoading = true;
      })
      .addCase(updateWatchlistItemAsync.fulfilled, (state, action) => {
        state.updateLoading = false;
        const index = state.watchlist.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.watchlist[index] = action.payload;
        }
      })
      .addCase(updateWatchlistItemAsync.rejected, (state, action) => {
        state.updateLoading = false;
        state.watchlistError = action.error.message || '更新自选股失败';
      });
    
    // 批量移除自选股
    builder
      .addCase(batchRemoveFromWatchlistAsync.pending, (state) => {
        state.removeLoading = true;
      })
      .addCase(batchRemoveFromWatchlistAsync.fulfilled, (state, action) => {
        state.removeLoading = false;
        state.watchlist = state.watchlist.filter(item => !action.payload.includes(item.id));
      })
      .addCase(batchRemoveFromWatchlistAsync.rejected, (state, action) => {
        state.removeLoading = false;
        state.watchlistError = action.error.message || '批量移除失败';
      });
  },
});

// 导出 actions
export const { setFilters, clearErrors, updateStockPrices } = watchlistSlice.actions;

// 选择器
export const selectWatchlist = (state: RootState) => state.watchlist.watchlist;
export const selectWatchlistLoading = (state: RootState) => state.watchlist.watchlistLoading;
export const selectWatchlistError = (state: RootState) => state.watchlist.watchlistError;

export const selectAddLoading = (state: RootState) => state.watchlist.addLoading;
export const selectRemoveLoading = (state: RootState) => state.watchlist.removeLoading;
export const selectUpdateLoading = (state: RootState) => state.watchlist.updateLoading;

export const selectWatchlistFilters = (state: RootState) => state.watchlist.filters;

// 派生选择器
export const selectFilteredWatchlist = (state: RootState) => {
  const { watchlist, filters } = state.watchlist;
  const { search, sort_by, sort_order } = filters;
  
  // 搜索过滤
  let filtered = watchlist;
  if (search) {
    filtered = watchlist.filter(item =>
      item.stock_code.toLowerCase().includes(search.toLowerCase()) ||
      item.stock_name.toLowerCase().includes(search.toLowerCase())
    );
  }
  
  // 排序
  filtered.sort((a, b) => {
    const aValue = a[sort_by as keyof WatchlistItem];
    const bValue = b[sort_by as keyof WatchlistItem];
    
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sort_order === 'asc' ? aValue - bValue : bValue - aValue;
    }
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sort_order === 'asc' 
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }
    
    return 0;
  });
  
  return filtered;
};

export const selectIsInWatchlist = (state: RootState, stockCode: string) => {
  return state.watchlist.watchlist.some(item => item.stock_code === stockCode);
};

export default watchlistSlice.reducer;
