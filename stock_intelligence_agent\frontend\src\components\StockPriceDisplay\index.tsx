/**
 * 股票价格显示组件
 */

import React from 'react';
import { Typography, Space } from 'antd';
import { CaretUpOutlined, CaretDownOutlined, MinusOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface StockPriceDisplayProps {
  price?: number;
  change?: number;
  pctChange?: number;
  size?: 'small' | 'default' | 'large';
  showIcon?: boolean;
  precision?: number;
}

const StockPriceDisplay: React.FC<StockPriceDisplayProps> = ({
  price,
  change,
  pctChange,
  size = 'default',
  showIcon = true,
  precision = 2,
}) => {
  // 确定变化方向
  const getChangeDirection = () => {
    if (!change && !pctChange) return 'neutral';
    const changeValue = change || pctChange || 0;
    if (changeValue > 0) return 'positive';
    if (changeValue < 0) return 'negative';
    return 'neutral';
  };

  const direction = getChangeDirection();

  // 获取样式类名
  const getClassName = () => {
    switch (direction) {
      case 'positive':
        return 'stock-change-positive';
      case 'negative':
        return 'stock-change-negative';
      default:
        return 'stock-change-neutral';
    }
  };

  // 获取图标
  const getIcon = () => {
    if (!showIcon) return null;
    
    switch (direction) {
      case 'positive':
        return <CaretUpOutlined />;
      case 'negative':
        return <CaretDownOutlined />;
      default:
        return <MinusOutlined />;
    }
  };

  // 获取字体大小
  const getFontSize = () => {
    switch (size) {
      case 'small':
        return { price: 14, change: 12 };
      case 'large':
        return { price: 20, change: 16 };
      default:
        return { price: 16, change: 14 };
    }
  };

  const fontSize = getFontSize();
  const className = getClassName();

  // 格式化数字
  const formatNumber = (value?: number, showSign = false) => {
    if (value === undefined || value === null) return '--';
    
    const formatted = value.toFixed(precision);
    if (showSign && value > 0) {
      return `+${formatted}`;
    }
    return formatted;
  };

  return (
    <div>
      {/* 价格 */}
      {price !== undefined && (
        <div>
          <Text
            className="stock-price"
            style={{ fontSize: fontSize.price }}
          >
            ¥{formatNumber(price)}
          </Text>
        </div>
      )}

      {/* 涨跌幅 */}
      {(change !== undefined || pctChange !== undefined) && (
        <div>
          <Space size={4} className={className}>
            {getIcon()}
            <Text style={{ fontSize: fontSize.change }}>
              {change !== undefined && (
                <span>{formatNumber(change, true)}</span>
              )}
              {change !== undefined && pctChange !== undefined && (
                <span> </span>
              )}
              {pctChange !== undefined && (
                <span>({formatNumber(pctChange, true)}%)</span>
              )}
            </Text>
          </Space>
        </div>
      )}
    </div>
  );
};

export default StockPriceDisplay;
