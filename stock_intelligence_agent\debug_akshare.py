#!/usr/bin/env python3
"""
调试AKShare接口问题
"""

import akshare as ak
import time

def test_akshare_interfaces():
    """测试AKShare各种接口"""
    print("🧪 测试AKShare接口...")
    
    # 1. 测试基础接口
    try:
        print("\n1️⃣ 测试基础股票数据...")
        df = ak.stock_zh_a_spot_em()
        print(f"✅ 基础股票数据获取成功，共 {len(df)} 只股票")
    except Exception as e:
        print(f"❌ 基础股票数据获取失败: {e}")
    
    # 2. 测试个股信息
    try:
        print("\n2️⃣ 测试个股信息...")
        info_df = ak.stock_individual_info_em(symbol="300750")
        print(f"✅ 个股信息获取成功")
        print(f"📋 信息字段: {list(info_df.columns) if hasattr(info_df, 'columns') else '非DataFrame格式'}")
    except Exception as e:
        print(f"❌ 个股信息获取失败: {e}")
    
    # 3. 测试研报接口
    try:
        print("\n3️⃣ 测试研报接口...")
        
        # 检查接口是否存在
        if hasattr(ak, 'stock_research_report_em'):
            print("✅ stock_research_report_em 接口存在")
            
            # 设置超时测试
            print("⏰ 开始获取研报数据（10秒超时）...")
            start_time = time.time()
            
            try:
                research_df = ak.stock_research_report_em(symbol="300750")
                end_time = time.time()
                
                print(f"✅ 研报数据获取成功，耗时 {end_time - start_time:.2f} 秒")
                print(f"📊 数据条数: {len(research_df)}")
                
                if len(research_df) > 0:
                    print(f"📋 数据列名: {list(research_df.columns)}")
                    print("📄 前2条数据:")
                    for i, (_, row) in enumerate(research_df.head(2).iterrows()):
                        print(f"  {i+1}. {dict(row)}")
                else:
                    print("⚠️ 返回数据为空")
                    
            except Exception as e:
                end_time = time.time()
                print(f"❌ 研报数据获取失败，耗时 {end_time - start_time:.2f} 秒")
                print(f"错误信息: {e}")
                
        else:
            print("❌ stock_research_report_em 接口不存在")
            
            # 查找相关接口
            all_funcs = [name for name in dir(ak) if 'stock' in name and 'research' in name]
            print(f"🔍 包含research的股票接口: {all_funcs}")
            
    except Exception as e:
        print(f"❌ 研报接口测试失败: {e}")
    
    # 4. 测试其他可能的研报相关接口
    try:
        print("\n4️⃣ 测试其他研报相关接口...")
        
        # 查找所有可能的研报接口
        research_funcs = [name for name in dir(ak) if 'research' in name.lower() or 'report' in name.lower()]
        print(f"🔍 所有研报相关接口: {research_funcs}")
        
        # 测试盈利预测接口（通常包含研报信息）
        if hasattr(ak, 'stock_profit_forecast'):
            try:
                forecast_df = ak.stock_profit_forecast(symbol="300750")
                print(f"✅ 盈利预测数据获取成功，共 {len(forecast_df)} 条")
            except Exception as e:
                print(f"❌ 盈利预测数据获取失败: {e}")
                
    except Exception as e:
        print(f"❌ 其他接口测试失败: {e}")

if __name__ == "__main__":
    print("🔧 AKShare接口调试工具")
    print("=" * 50)
    test_akshare_interfaces()
    print("\n🎉 调试完成！")
