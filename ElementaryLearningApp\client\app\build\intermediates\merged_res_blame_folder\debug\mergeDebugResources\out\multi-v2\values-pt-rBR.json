{"logs": [{"outputFile": "com.example.elementarylearningcompanion.app-mergeDebugResources-58:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a4df73b4c2d29b2ee6d6405fd9c7e463\\transformed\\ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "48,49,50,51,52,57,58,174,175,176,177,179,180,184,187,188,189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4525,4620,4706,4803,4902,5283,5366,16362,16453,16540,16612,16758,16843,17179,17435,17511,17578", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "4615,4701,4798,4897,4983,5361,5458,16448,16535,16607,16676,16838,16928,17250,17506,17573,17686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0a92ec79585cffeb394139b023740469\\transformed\\material-1.12.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1062,1126,1218,1297,1357,1447,1511,1582,1645,1720,1784,1838,1965,2023,2085,2139,2218,2359,2446,2522,2617,2698,2780,2919,3002,3086,3225,3312,3392,3448,3499,3565,3639,3719,3790,3873,3946,4023,4092,4166,4268,4356,4433,4526,4622,4696,4776,4873,4925,5009,5075,5162,5250,5312,5376,5439,5507,5616,5727,5831,5941,6001,6056,6133,6216,6293", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "268,349,427,511,606,695,796,916,997,1057,1121,1213,1292,1352,1442,1506,1577,1640,1715,1779,1833,1960,2018,2080,2134,2213,2354,2441,2517,2612,2693,2775,2914,2997,3081,3220,3307,3387,3443,3494,3560,3634,3714,3785,3868,3941,4018,4087,4161,4263,4351,4428,4521,4617,4691,4771,4868,4920,5004,5070,5157,5245,5307,5371,5434,5502,5611,5722,5826,5936,5996,6051,6128,6211,6288,6367"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,56,59,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,182,183,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3061,3142,3220,3304,3399,4223,4324,4444,4988,5048,5112,5204,5463,11663,11753,11817,11888,11951,12026,12090,12144,12271,12329,12391,12445,12524,12665,12752,12828,12923,13004,13086,13225,13308,13392,13531,13618,13698,13754,13805,13871,13945,14025,14096,14179,14252,14329,14398,14472,14574,14662,14739,14832,14928,15002,15082,15179,15231,15315,15381,15468,15556,15618,15682,15745,15813,15922,16033,16137,16247,16307,16681,17019,17102,17255", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,56,59,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,182,183,185", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "318,3137,3215,3299,3394,3483,4319,4439,4520,5043,5107,5199,5278,5518,11748,11812,11883,11946,12021,12085,12139,12266,12324,12386,12440,12519,12660,12747,12823,12918,12999,13081,13220,13303,13387,13526,13613,13693,13749,13800,13866,13940,14020,14091,14174,14247,14324,14393,14467,14569,14657,14734,14827,14923,14997,15077,15174,15226,15310,15376,15463,15551,15613,15677,15740,15808,15917,16028,16132,16242,16302,16357,16753,17097,17174,17329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a90387b92ad49178bbd95d8e99c39d57\\transformed\\core-1.13.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,186", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3488,3585,3687,3786,3886,3993,4103,17334", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3580,3682,3781,3881,3988,4098,4218,17430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\586aaef9cc7b931892e6d835ad6bc99e\\transformed\\material3-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,411,527,629,726,840,974,1092,1244,1328,1429,1524,1624,1739,1869,1975,2114,2250,2381,2547,2674,2794,2918,3038,3134,3231,3351,3467,3567,3678,3787,3927,4072,4182,4285,4371,4465,4557,4647,4736,4837,4917,5001,5102,5208,5300,5399,5487,5599,5700,5804,5923,6003,6103", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "169,290,406,522,624,721,835,969,1087,1239,1323,1424,1519,1619,1734,1864,1970,2109,2245,2376,2542,2669,2789,2913,3033,3129,3226,3346,3462,3562,3673,3782,3922,4067,4177,4280,4366,4460,4552,4642,4731,4832,4912,4996,5097,5203,5295,5394,5482,5594,5695,5799,5918,5998,6098,6190"}, "to": {"startLines": "60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5523,5642,5763,5879,5995,6097,6194,6308,6442,6560,6712,6796,6897,6992,7092,7207,7337,7443,7582,7718,7849,8015,8142,8262,8386,8506,8602,8699,8819,8935,9035,9146,9255,9395,9540,9650,9753,9839,9933,10025,10115,10204,10305,10385,10469,10570,10676,10768,10867,10955,11067,11168,11272,11391,11471,11571", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "5637,5758,5874,5990,6092,6189,6303,6437,6555,6707,6791,6892,6987,7087,7202,7332,7438,7577,7713,7844,8010,8137,8257,8381,8501,8597,8694,8814,8930,9030,9141,9250,9390,9535,9645,9748,9834,9928,10020,10110,10199,10300,10380,10464,10565,10671,10763,10862,10950,11062,11163,11267,11386,11466,11566,11658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fd01bc6fe90ff89257de9b3fddbd915b\\transformed\\foundation-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "190,191", "startColumns": "4,4", "startOffsets": "17691,17774", "endColumns": "82,84", "endOffsets": "17769,17854"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\95602d9e58c05302d7b199e835d43608\\transformed\\appcompat-1.6.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,16933", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,17014"}}]}]}