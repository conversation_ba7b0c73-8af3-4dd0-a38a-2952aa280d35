"""
用户管理 API 端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.services.user_service import UserService
from app.auth.security import get_current_user_from_token, require_superuser
from app.schemas.user import (
    UserResponse,
    UserUpdateRequest,
    UserProfileResponse,
    UserProfileUpdateRequest,
    UserListResponse,
    UserStatsResponse
)
from app.schemas.base import BaseResponse, PaginationResponse

router = APIRouter()


@router.get("/me", response_model=UserResponse)
async def get_current_user_profile(
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取当前用户信息"""
    user_service = UserService(db)
    user = await user_service.get_user_by_id(current_user["user_id"])
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return UserResponse.from_orm(user)


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_data: UserUpdateRequest,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """更新当前用户信息"""
    user_service = UserService(db)
    
    # 过滤掉空值
    update_data = {k: v for k, v in user_data.dict().items() if v is not None}
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="没有提供更新数据"
        )
    
    user = await user_service.update_user(
        user_id=current_user["user_id"],
        **update_data
    )
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return UserResponse.from_orm(user)


@router.get("/me/profile", response_model=UserProfileResponse)
async def get_current_user_profile_detail(
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取当前用户详细档案"""
    user_service = UserService(db)
    profile = await user_service.get_user_profile(current_user["user_id"])
    
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户档案不存在"
        )
    
    return UserProfileResponse.from_orm(profile)


@router.put("/me/profile", response_model=UserProfileResponse)
async def update_current_user_profile(
    profile_data: UserProfileUpdateRequest,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """更新当前用户档案"""
    user_service = UserService(db)
    
    # 过滤掉空值
    update_data = {k: v for k, v in profile_data.dict().items() if v is not None}
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="没有提供更新数据"
        )
    
    profile = await user_service.update_user_profile(
        user_id=current_user["user_id"],
        **update_data
    )
    
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户档案不存在"
        )
    
    return UserProfileResponse.from_orm(profile)


@router.delete("/me", response_model=BaseResponse)
async def delete_current_user(
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """删除当前用户账号"""
    user_service = UserService(db)
    success = await user_service.delete_user(current_user["user_id"])
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return BaseResponse(
        success=True,
        message="用户账号已删除"
    )


# 管理员专用端点
@router.get("/", response_model=PaginationResponse[UserResponse])
async def get_users_list(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_active: Optional[bool] = Query(None, description="是否活跃"),
    is_superuser: Optional[bool] = Query(None, description="是否超级用户"),
    current_user: dict = Depends(require_superuser),
    db: AsyncSession = Depends(get_db)
):
    """获取用户列表（管理员）"""
    user_service = UserService(db)
    users, total = await user_service.get_users_list(
        skip=skip,
        limit=limit,
        search=search,
        is_active=is_active,
        is_superuser=is_superuser
    )
    
    return PaginationResponse(
        data=[UserResponse.from_orm(user) for user in users],
        total=total,
        skip=skip,
        limit=limit,
        has_more=skip + len(users) < total
    )


@router.get("/stats", response_model=UserStatsResponse)
async def get_user_statistics(
    current_user: dict = Depends(require_superuser),
    db: AsyncSession = Depends(get_db)
):
    """获取用户统计信息（管理员）"""
    user_service = UserService(db)
    stats = await user_service.get_user_statistics()
    
    return UserStatsResponse(**stats)


@router.get("/{user_id}", response_model=UserResponse)
async def get_user_by_id(
    user_id: str,
    current_user: dict = Depends(require_superuser),
    db: AsyncSession = Depends(get_db)
):
    """根据ID获取用户信息（管理员）"""
    user_service = UserService(db)
    user = await user_service.get_user_by_id(user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return UserResponse.from_orm(user)


@router.put("/{user_id}", response_model=UserResponse)
async def update_user_by_id(
    user_id: str,
    user_data: UserUpdateRequest,
    current_user: dict = Depends(require_superuser),
    db: AsyncSession = Depends(get_db)
):
    """更新用户信息（管理员）"""
    user_service = UserService(db)
    
    # 过滤掉空值
    update_data = {k: v for k, v in user_data.dict().items() if v is not None}
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="没有提供更新数据"
        )
    
    user = await user_service.update_user(
        user_id=user_id,
        **update_data
    )
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return UserResponse.from_orm(user)


@router.post("/{user_id}/activate", response_model=BaseResponse)
async def activate_user(
    user_id: str,
    current_user: dict = Depends(require_superuser),
    db: AsyncSession = Depends(get_db)
):
    """激活用户（管理员）"""
    user_service = UserService(db)
    success = await user_service.activate_user(user_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return BaseResponse(
        success=True,
        message="用户已激活"
    )


@router.post("/{user_id}/deactivate", response_model=BaseResponse)
async def deactivate_user(
    user_id: str,
    current_user: dict = Depends(require_superuser),
    db: AsyncSession = Depends(get_db)
):
    """停用用户（管理员）"""
    user_service = UserService(db)
    success = await user_service.deactivate_user(user_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return BaseResponse(
        success=True,
        message="用户已停用"
    )


@router.delete("/{user_id}", response_model=BaseResponse)
async def delete_user_by_id(
    user_id: str,
    current_user: dict = Depends(require_superuser),
    db: AsyncSession = Depends(get_db)
):
    """删除用户（管理员）"""
    user_service = UserService(db)
    success = await user_service.delete_user(user_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return BaseResponse(
        success=True,
        message="用户已删除"
    )
