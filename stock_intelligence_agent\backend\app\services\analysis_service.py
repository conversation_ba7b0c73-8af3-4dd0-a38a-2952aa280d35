"""
分析服务层
提供技术分析和基本面分析功能
"""

import logging
import numpy as np
import pandas as pd
import talib
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc
from decimal import Decimal

from app.models.stock import Stock, StockQuote, TechnicalIndicator, FinancialData
from app.core.config import settings

logger = logging.getLogger(__name__)


class AnalysisService:
    """分析服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def calculate_technical_indicators(
        self,
        stock_id: str,
        period: int = 100
    ) -> Dict[str, Any]:
        """
        计算技术指标
        
        Args:
            stock_id: 股票ID
            period: 计算周期
            
        Returns:
            技术指标数据
        """
        try:
            # 获取历史行情数据
            stmt = (
                select(StockQuote)
                .where(StockQuote.stock_id == stock_id)
                .order_by(desc(StockQuote.trade_date))
                .limit(period)
            )
            
            result = await self.db.execute(stmt)
            quotes = list(result.scalars().all())
            
            if len(quotes) < 20:  # 至少需要20个数据点
                return {"error": "数据不足，无法计算技术指标"}
            
            # 反转数据顺序（talib需要时间正序）
            quotes.reverse()
            
            # 转换为numpy数组
            close_prices = np.array([float(q.close_price) for q in quotes if q.close_price])
            high_prices = np.array([float(q.high_price) for q in quotes if q.high_price])
            low_prices = np.array([float(q.low_price) for q in quotes if q.low_price])
            volumes = np.array([float(q.volume) for q in quotes if q.volume])
            
            indicators = {}
            
            # 移动平均线
            indicators['ma5'] = talib.SMA(close_prices, timeperiod=5)
            indicators['ma10'] = talib.SMA(close_prices, timeperiod=10)
            indicators['ma20'] = talib.SMA(close_prices, timeperiod=20)
            indicators['ma60'] = talib.SMA(close_prices, timeperiod=60)
            
            # MACD
            macd_dif, macd_dea, macd_histogram = talib.MACD(close_prices)
            indicators['macd_dif'] = macd_dif
            indicators['macd_dea'] = macd_dea
            indicators['macd_histogram'] = macd_histogram
            
            # RSI
            indicators['rsi6'] = talib.RSI(close_prices, timeperiod=6)
            indicators['rsi12'] = talib.RSI(close_prices, timeperiod=12)
            indicators['rsi24'] = talib.RSI(close_prices, timeperiod=24)
            
            # KDJ
            kdj_k, kdj_d = talib.STOCH(high_prices, low_prices, close_prices)
            kdj_j = 3 * kdj_k - 2 * kdj_d
            indicators['kdj_k'] = kdj_k
            indicators['kdj_d'] = kdj_d
            indicators['kdj_j'] = kdj_j
            
            # 布林带
            boll_upper, boll_mid, boll_lower = talib.BBANDS(close_prices)
            indicators['boll_upper'] = boll_upper
            indicators['boll_mid'] = boll_mid
            indicators['boll_lower'] = boll_lower
            
            # 成交量移动平均
            indicators['volume_ma5'] = talib.SMA(volumes, timeperiod=5)
            indicators['volume_ma10'] = talib.SMA(volumes, timeperiod=10)
            
            # 保存技术指标到数据库
            await self._save_technical_indicators(stock_id, quotes, indicators)
            
            # 返回最新值
            latest_indicators = {}
            for key, values in indicators.items():
                if len(values) > 0 and not np.isnan(values[-1]):
                    latest_indicators[key] = float(values[-1])
                else:
                    latest_indicators[key] = None
            
            return {
                "stock_id": stock_id,
                "calculation_date": datetime.now().isoformat(),
                "data_points": len(quotes),
                "indicators": latest_indicators
            }
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            raise
    
    async def _save_technical_indicators(
        self,
        stock_id: str,
        quotes: List[StockQuote],
        indicators: Dict[str, np.ndarray]
    ):
        """保存技术指标到数据库"""
        try:
            for i, quote in enumerate(quotes):
                # 检查是否已存在
                stmt = select(TechnicalIndicator).where(
                    and_(
                        TechnicalIndicator.stock_id == stock_id,
                        TechnicalIndicator.trade_date == quote.trade_date
                    )
                )
                result = await self.db.execute(stmt)
                existing = result.scalar_one_or_none()
                
                # 构建指标数据
                indicator_data = {}
                for key, values in indicators.items():
                    if i < len(values) and not np.isnan(values[i]):
                        indicator_data[key] = Decimal(str(values[i]))
                
                if existing:
                    # 更新现有记录
                    for key, value in indicator_data.items():
                        if hasattr(existing, key):
                            setattr(existing, key, value)
                else:
                    # 创建新记录
                    technical_indicator = TechnicalIndicator(
                        stock_id=stock_id,
                        trade_date=quote.trade_date,
                        **indicator_data
                    )
                    self.db.add(technical_indicator)
            
            await self.db.commit()
            
        except Exception as e:
            logger.error(f"保存技术指标失败: {e}")
            await self.db.rollback()
            raise
    
    async def analyze_trend(self, stock_id: str, period: int = 20) -> Dict[str, Any]:
        """
        趋势分析
        
        Args:
            stock_id: 股票ID
            period: 分析周期
            
        Returns:
            趋势分析结果
        """
        try:
            # 获取最近的技术指标
            stmt = (
                select(TechnicalIndicator)
                .where(TechnicalIndicator.stock_id == stock_id)
                .order_by(desc(TechnicalIndicator.trade_date))
                .limit(period)
            )
            
            result = await self.db.execute(stmt)
            indicators = list(result.scalars().all())
            
            if not indicators:
                return {"error": "暂无技术指标数据"}
            
            latest = indicators[0]
            
            # 趋势判断
            trend_analysis = {
                "trend_direction": "unknown",
                "trend_strength": "weak",
                "support_level": None,
                "resistance_level": None,
                "signals": []
            }
            
            # 移动平均线趋势
            if latest.ma5 and latest.ma10 and latest.ma20:
                ma5 = float(latest.ma5)
                ma10 = float(latest.ma10)
                ma20 = float(latest.ma20)
                
                if ma5 > ma10 > ma20:
                    trend_analysis["trend_direction"] = "upward"
                    trend_analysis["trend_strength"] = "strong"
                    trend_analysis["signals"].append("多头排列，强势上涨")
                elif ma5 < ma10 < ma20:
                    trend_analysis["trend_direction"] = "downward"
                    trend_analysis["trend_strength"] = "strong"
                    trend_analysis["signals"].append("空头排列，强势下跌")
                elif ma5 > ma20:
                    trend_analysis["trend_direction"] = "upward"
                    trend_analysis["trend_strength"] = "moderate"
                elif ma5 < ma20:
                    trend_analysis["trend_direction"] = "downward"
                    trend_analysis["trend_strength"] = "moderate"
                else:
                    trend_analysis["trend_direction"] = "sideways"
                    trend_analysis["signals"].append("横盘整理")
            
            # MACD信号
            if latest.macd_dif and latest.macd_dea:
                macd_dif = float(latest.macd_dif)
                macd_dea = float(latest.macd_dea)
                
                if macd_dif > macd_dea and macd_dif > 0:
                    trend_analysis["signals"].append("MACD金叉，买入信号")
                elif macd_dif < macd_dea and macd_dif < 0:
                    trend_analysis["signals"].append("MACD死叉，卖出信号")
            
            # RSI超买超卖
            if latest.rsi12:
                rsi = float(latest.rsi12)
                if rsi > 80:
                    trend_analysis["signals"].append("RSI超买，注意回调风险")
                elif rsi < 20:
                    trend_analysis["signals"].append("RSI超卖，可能反弹")
            
            # KDJ信号
            if latest.kdj_k and latest.kdj_d:
                kdj_k = float(latest.kdj_k)
                kdj_d = float(latest.kdj_d)
                
                if kdj_k > kdj_d and kdj_k < 80:
                    trend_analysis["signals"].append("KDJ金叉，短期看涨")
                elif kdj_k < kdj_d and kdj_k > 20:
                    trend_analysis["signals"].append("KDJ死叉，短期看跌")
            
            # 布林带支撑阻力
            if latest.boll_upper and latest.boll_lower:
                trend_analysis["resistance_level"] = float(latest.boll_upper)
                trend_analysis["support_level"] = float(latest.boll_lower)
            
            return trend_analysis
            
        except Exception as e:
            logger.error(f"趋势分析失败: {e}")
            raise
    
    async def fundamental_analysis(self, stock_id: str) -> Dict[str, Any]:
        """
        基本面分析
        
        Args:
            stock_id: 股票ID
            
        Returns:
            基本面分析结果
        """
        try:
            # 获取最新财务数据
            stmt = (
                select(FinancialData)
                .where(FinancialData.stock_id == stock_id)
                .order_by(desc(FinancialData.report_date))
                .limit(4)  # 最近4个季度
            )
            
            result = await self.db.execute(stmt)
            financial_data = list(result.scalars().all())
            
            if not financial_data:
                return {"error": "暂无财务数据"}
            
            latest = financial_data[0]
            
            analysis = {
                "report_date": latest.report_date.isoformat(),
                "profitability": {},
                "growth": {},
                "solvency": {},
                "efficiency": {},
                "valuation": {},
                "overall_score": 0,
                "rating": "unknown"
            }
            
            # 盈利能力分析
            if latest.roe:
                roe = float(latest.roe)
                analysis["profitability"]["roe"] = roe
                analysis["profitability"]["roe_rating"] = self._rate_roe(roe)
            
            if latest.roa:
                roa = float(latest.roa)
                analysis["profitability"]["roa"] = roa
                analysis["profitability"]["roa_rating"] = self._rate_roa(roa)
            
            if latest.gross_margin:
                gross_margin = float(latest.gross_margin)
                analysis["profitability"]["gross_margin"] = gross_margin
                analysis["profitability"]["gross_margin_rating"] = self._rate_margin(gross_margin)
            
            if latest.net_margin:
                net_margin = float(latest.net_margin)
                analysis["profitability"]["net_margin"] = net_margin
                analysis["profitability"]["net_margin_rating"] = self._rate_margin(net_margin)
            
            # 成长能力分析
            if len(financial_data) >= 2:
                current = financial_data[0]
                previous = financial_data[1]
                
                if current.revenue and previous.revenue:
                    revenue_growth = (float(current.revenue) - float(previous.revenue)) / float(previous.revenue) * 100
                    analysis["growth"]["revenue_growth"] = revenue_growth
                    analysis["growth"]["revenue_growth_rating"] = self._rate_growth(revenue_growth)
                
                if current.net_profit and previous.net_profit:
                    profit_growth = (float(current.net_profit) - float(previous.net_profit)) / float(previous.net_profit) * 100
                    analysis["growth"]["profit_growth"] = profit_growth
                    analysis["growth"]["profit_growth_rating"] = self._rate_growth(profit_growth)
            
            # 偿债能力分析
            if latest.debt_ratio:
                debt_ratio = float(latest.debt_ratio)
                analysis["solvency"]["debt_ratio"] = debt_ratio
                analysis["solvency"]["debt_ratio_rating"] = self._rate_debt_ratio(debt_ratio)
            
            if latest.current_ratio:
                current_ratio = float(latest.current_ratio)
                analysis["solvency"]["current_ratio"] = current_ratio
                analysis["solvency"]["current_ratio_rating"] = self._rate_current_ratio(current_ratio)
            
            # 营运能力分析
            if latest.total_asset_turnover:
                asset_turnover = float(latest.total_asset_turnover)
                analysis["efficiency"]["asset_turnover"] = asset_turnover
                analysis["efficiency"]["asset_turnover_rating"] = self._rate_turnover(asset_turnover)
            
            # 估值分析
            if latest.eps:
                eps = float(latest.eps)
                analysis["valuation"]["eps"] = eps
            
            if latest.bps:
                bps = float(latest.bps)
                analysis["valuation"]["bps"] = bps
            
            # 计算综合评分
            analysis["overall_score"] = self._calculate_overall_score(analysis)
            analysis["rating"] = self._get_rating(analysis["overall_score"])
            
            return analysis
            
        except Exception as e:
            logger.error(f"基本面分析失败: {e}")
            raise
    
    def _rate_roe(self, roe: float) -> str:
        """ROE评级"""
        if roe >= 20:
            return "excellent"
        elif roe >= 15:
            return "good"
        elif roe >= 10:
            return "average"
        else:
            return "poor"
    
    def _rate_roa(self, roa: float) -> str:
        """ROA评级"""
        if roa >= 10:
            return "excellent"
        elif roa >= 5:
            return "good"
        elif roa >= 2:
            return "average"
        else:
            return "poor"
    
    def _rate_margin(self, margin: float) -> str:
        """利润率评级"""
        if margin >= 30:
            return "excellent"
        elif margin >= 20:
            return "good"
        elif margin >= 10:
            return "average"
        else:
            return "poor"
    
    def _rate_growth(self, growth: float) -> str:
        """增长率评级"""
        if growth >= 30:
            return "excellent"
        elif growth >= 15:
            return "good"
        elif growth >= 5:
            return "average"
        else:
            return "poor"
    
    def _rate_debt_ratio(self, debt_ratio: float) -> str:
        """资产负债率评级"""
        if debt_ratio <= 30:
            return "excellent"
        elif debt_ratio <= 50:
            return "good"
        elif debt_ratio <= 70:
            return "average"
        else:
            return "poor"
    
    def _rate_current_ratio(self, current_ratio: float) -> str:
        """流动比率评级"""
        if current_ratio >= 2:
            return "excellent"
        elif current_ratio >= 1.5:
            return "good"
        elif current_ratio >= 1:
            return "average"
        else:
            return "poor"
    
    def _rate_turnover(self, turnover: float) -> str:
        """周转率评级"""
        if turnover >= 1.5:
            return "excellent"
        elif turnover >= 1:
            return "good"
        elif turnover >= 0.5:
            return "average"
        else:
            return "poor"
    
    def _calculate_overall_score(self, analysis: Dict[str, Any]) -> int:
        """计算综合评分"""
        score = 0
        count = 0
        
        # 评级映射
        rating_scores = {
            "excellent": 4,
            "good": 3,
            "average": 2,
            "poor": 1
        }
        
        # 盈利能力权重40%
        profitability_score = 0
        profitability_count = 0
        for key, value in analysis["profitability"].items():
            if key.endswith("_rating") and value in rating_scores:
                profitability_score += rating_scores[value]
                profitability_count += 1
        
        if profitability_count > 0:
            score += (profitability_score / profitability_count) * 0.4
            count += 0.4
        
        # 成长能力权重30%
        growth_score = 0
        growth_count = 0
        for key, value in analysis["growth"].items():
            if key.endswith("_rating") and value in rating_scores:
                growth_score += rating_scores[value]
                growth_count += 1
        
        if growth_count > 0:
            score += (growth_score / growth_count) * 0.3
            count += 0.3
        
        # 偿债能力权重20%
        solvency_score = 0
        solvency_count = 0
        for key, value in analysis["solvency"].items():
            if key.endswith("_rating") and value in rating_scores:
                solvency_score += rating_scores[value]
                solvency_count += 1
        
        if solvency_count > 0:
            score += (solvency_score / solvency_count) * 0.2
            count += 0.2
        
        # 营运能力权重10%
        efficiency_score = 0
        efficiency_count = 0
        for key, value in analysis["efficiency"].items():
            if key.endswith("_rating") and value in rating_scores:
                efficiency_score += rating_scores[value]
                efficiency_count += 1
        
        if efficiency_count > 0:
            score += (efficiency_score / efficiency_count) * 0.1
            count += 0.1
        
        if count > 0:
            return int((score / count) * 25)  # 转换为100分制
        else:
            return 0
    
    def _get_rating(self, score: int) -> str:
        """根据评分获取评级"""
        if score >= 80:
            return "A"
        elif score >= 70:
            return "B"
        elif score >= 60:
            return "C"
        elif score >= 50:
            return "D"
        else:
            return "E"
    
    async def risk_assessment(self, stock_id: str) -> Dict[str, Any]:
        """
        风险评估
        
        Args:
            stock_id: 股票ID
            
        Returns:
            风险评估结果
        """
        try:
            # 获取历史行情数据
            stmt = (
                select(StockQuote)
                .where(StockQuote.stock_id == stock_id)
                .order_by(desc(StockQuote.trade_date))
                .limit(252)  # 一年的交易日
            )
            
            result = await self.db.execute(stmt)
            quotes = list(result.scalars().all())
            
            if len(quotes) < 30:
                return {"error": "数据不足，无法进行风险评估"}
            
            # 计算收益率
            returns = []
            for i in range(1, len(quotes)):
                if quotes[i].close_price and quotes[i-1].close_price:
                    ret = (float(quotes[i-1].close_price) - float(quotes[i].close_price)) / float(quotes[i].close_price)
                    returns.append(ret)
            
            if not returns:
                return {"error": "无法计算收益率"}
            
            returns = np.array(returns)
            
            # 风险指标计算
            risk_metrics = {
                "volatility": float(np.std(returns) * np.sqrt(252)),  # 年化波动率
                "max_drawdown": self._calculate_max_drawdown(quotes),
                "var_95": float(np.percentile(returns, 5)),  # 95% VaR
                "var_99": float(np.percentile(returns, 1)),  # 99% VaR
                "sharpe_ratio": self._calculate_sharpe_ratio(returns),
                "risk_level": "unknown"
            }
            
            # 风险等级评估
            volatility = risk_metrics["volatility"]
            if volatility < 0.2:
                risk_metrics["risk_level"] = "low"
            elif volatility < 0.4:
                risk_metrics["risk_level"] = "medium"
            elif volatility < 0.6:
                risk_metrics["risk_level"] = "high"
            else:
                risk_metrics["risk_level"] = "very_high"
            
            return risk_metrics
            
        except Exception as e:
            logger.error(f"风险评估失败: {e}")
            raise
    
    def _calculate_max_drawdown(self, quotes: List[StockQuote]) -> float:
        """计算最大回撤"""
        prices = [float(q.close_price) for q in reversed(quotes) if q.close_price]
        if len(prices) < 2:
            return 0.0
        
        peak = prices[0]
        max_dd = 0.0
        
        for price in prices:
            if price > peak:
                peak = price
            
            drawdown = (peak - price) / peak
            if drawdown > max_dd:
                max_dd = drawdown
        
        return max_dd
    
    def _calculate_sharpe_ratio(self, returns: np.ndarray, risk_free_rate: float = 0.03) -> float:
        """计算夏普比率"""
        if len(returns) == 0:
            return 0.0
        
        excess_returns = returns - risk_free_rate / 252  # 日无风险收益率
        if np.std(excess_returns) == 0:
            return 0.0
        
        return float(np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252))
