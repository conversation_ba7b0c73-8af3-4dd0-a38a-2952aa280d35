"""
股票服务层
提供股票相关的业务逻辑处理
"""

import json
import logging
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, asc, func, text
from sqlalchemy.orm import selectinload
import redis.asyncio as redis

from app.models.stock import Stock, StockQuote, RealtimeQuote, TechnicalIndicator, FinancialData
from app.data_collector.tasks import (
    collect_realtime_quotes_task,
    collect_historical_quotes_task,
    collect_financial_data_task
)
from app.core.config import settings

logger = logging.getLogger(__name__)


class StockService:
    """股票服务类"""
    
    def __init__(self, db: AsyncSession, redis_client: Optional[redis.Redis] = None):
        self.db = db
        self.redis = redis_client
    
    async def get_stocks(
        self,
        skip: int = 0,
        limit: int = 100,
        market: Optional[str] = None,
        industry: Optional[str] = None,
        search: Optional[str] = None,
    ) -> Tuple[List[Stock], int]:
        """
        获取股票列表
        
        Args:
            skip: 跳过记录数
            limit: 返回记录数
            market: 市场筛选
            industry: 行业筛选
            search: 搜索关键词
            
        Returns:
            (股票列表, 总数)
        """
        try:
            # 构建查询条件
            conditions = [Stock.is_active == True]
            
            if market:
                conditions.append(Stock.market == market.upper())
            
            if industry:
                conditions.append(Stock.industry == industry)
            
            if search:
                search_condition = or_(
                    Stock.code.ilike(f"%{search}%"),
                    Stock.name.ilike(f"%{search}%")
                )
                conditions.append(search_condition)
            
            # 查询总数
            count_stmt = select(func.count(Stock.id)).where(and_(*conditions))
            count_result = await self.db.execute(count_stmt)
            total = count_result.scalar()
            
            # 查询数据
            stmt = select(Stock).where(and_(*conditions)).offset(skip).limit(limit).order_by(Stock.code)
            result = await self.db.execute(stmt)
            stocks = result.scalars().all()
            
            return list(stocks), total
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            raise
    
    async def get_stock_by_code(self, stock_code: str) -> Optional[Stock]:
        """
        根据股票代码获取股票信息
        
        Args:
            stock_code: 股票代码
            
        Returns:
            股票信息
        """
        try:
            stmt = select(Stock).where(Stock.code == stock_code)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"获取股票信息失败: {e}")
            raise
    
    async def get_stock_quotes(
        self,
        stock_id: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 100,
    ) -> List[StockQuote]:
        """
        获取股票历史行情
        
        Args:
            stock_id: 股票ID
            start_date: 开始日期
            end_date: 结束日期
            limit: 返回记录数
            
        Returns:
            历史行情列表
        """
        try:
            conditions = [StockQuote.stock_id == stock_id]
            
            if start_date:
                conditions.append(StockQuote.trade_date >= start_date)
            
            if end_date:
                conditions.append(StockQuote.trade_date <= end_date)
            
            stmt = (
                select(StockQuote)
                .where(and_(*conditions))
                .order_by(desc(StockQuote.trade_date))
                .limit(limit)
            )
            
            result = await self.db.execute(stmt)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error(f"获取股票行情失败: {e}")
            raise
    
    async def get_realtime_quote_from_cache(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """
        从缓存获取实时行情
        
        Args:
            stock_code: 股票代码
            
        Returns:
            实时行情数据
        """
        try:
            if not self.redis:
                return None
            
            cache_key = f"realtime_quote:{stock_code}"
            cached_data = await self.redis.hgetall(cache_key)
            
            if not cached_data:
                return None
            
            # 转换数据类型
            realtime_quote = {
                'code': stock_code,
                'current_price': float(cached_data.get('current_price', 0)),
                'change': float(cached_data.get('change', 0)),
                'pct_change': float(cached_data.get('pct_change', 0)),
                'volume': float(cached_data.get('volume', 0)),
                'amount': float(cached_data.get('amount', 0)),
                'timestamp': datetime.fromisoformat(cached_data.get('timestamp', datetime.now().isoformat())),
            }
            
            return realtime_quote
            
        except Exception as e:
            logger.error(f"从缓存获取实时行情失败: {e}")
            return None
    
    async def get_latest_realtime_quote(self, stock_id: str) -> Optional[RealtimeQuote]:
        """
        获取最新实时行情
        
        Args:
            stock_id: 股票ID
            
        Returns:
            最新实时行情
        """
        try:
            stmt = (
                select(RealtimeQuote)
                .where(RealtimeQuote.stock_id == stock_id)
                .order_by(desc(RealtimeQuote.timestamp))
                .limit(1)
            )
            
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"获取最新实时行情失败: {e}")
            raise
    
    async def get_technical_analysis(
        self,
        stock_id: str,
        period: str = "daily",
        indicators: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        获取技术分析数据
        
        Args:
            stock_id: 股票ID
            period: 周期
            indicators: 指标列表
            
        Returns:
            技术分析数据
        """
        try:
            # 获取最近的技术指标数据
            stmt = (
                select(TechnicalIndicator)
                .where(TechnicalIndicator.stock_id == stock_id)
                .order_by(desc(TechnicalIndicator.trade_date))
                .limit(100)
            )
            
            result = await self.db.execute(stmt)
            technical_data = list(result.scalars().all())
            
            if not technical_data:
                return {"message": "暂无技术分析数据"}
            
            # 构建返回数据
            analysis_result = {
                "period": period,
                "data_count": len(technical_data),
                "latest_date": technical_data[0].trade_date.isoformat() if technical_data else None,
                "indicators": {}
            }
            
            # 如果指定了特定指标，只返回这些指标
            if indicators:
                for indicator in indicators:
                    if hasattr(technical_data[0], indicator):
                        analysis_result["indicators"][indicator] = [
                            getattr(item, indicator) for item in technical_data
                            if getattr(item, indicator) is not None
                        ]
            else:
                # 返回所有指标
                latest = technical_data[0]
                analysis_result["indicators"] = {
                    "ma5": float(latest.ma5) if latest.ma5 else None,
                    "ma10": float(latest.ma10) if latest.ma10 else None,
                    "ma20": float(latest.ma20) if latest.ma20 else None,
                    "ma60": float(latest.ma60) if latest.ma60 else None,
                    "macd_dif": float(latest.macd_dif) if latest.macd_dif else None,
                    "macd_dea": float(latest.macd_dea) if latest.macd_dea else None,
                    "rsi6": float(latest.rsi6) if latest.rsi6 else None,
                    "rsi12": float(latest.rsi12) if latest.rsi12 else None,
                    "kdj_k": float(latest.kdj_k) if latest.kdj_k else None,
                    "kdj_d": float(latest.kdj_d) if latest.kdj_d else None,
                    "kdj_j": float(latest.kdj_j) if latest.kdj_j else None,
                }
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"获取技术分析失败: {e}")
            raise
    
    async def search_stocks(self, query: str, limit: int = 10) -> List[Stock]:
        """
        搜索股票
        
        Args:
            query: 搜索关键词
            limit: 返回记录数
            
        Returns:
            股票列表
        """
        try:
            # 构建搜索条件
            search_condition = or_(
                Stock.code.ilike(f"%{query}%"),
                Stock.name.ilike(f"%{query}%")
            )
            
            stmt = (
                select(Stock)
                .where(and_(Stock.is_active == True, search_condition))
                .order_by(
                    # 优先显示代码完全匹配的
                    func.case(
                        (Stock.code == query, 1),
                        else_=2
                    ),
                    # 然后按代码排序
                    Stock.code
                )
                .limit(limit)
            )
            
            result = await self.db.execute(stmt)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error(f"搜索股票失败: {e}")
            raise
    
    async def get_hot_stocks(
        self,
        limit: int = 20,
        sort_by: str = "pct_change",
        order: str = "desc",
    ) -> List[Dict[str, Any]]:
        """
        获取热门股票
        
        Args:
            limit: 返回记录数
            sort_by: 排序字段
            order: 排序方向
            
        Returns:
            热门股票列表
        """
        try:
            # 获取今日最新行情数据
            today = date.today()
            
            # 构建排序条件
            sort_column = getattr(StockQuote, sort_by, StockQuote.pct_change)
            order_func = desc if order == "desc" else asc
            
            stmt = (
                select(Stock, StockQuote)
                .join(StockQuote, Stock.id == StockQuote.stock_id)
                .where(
                    and_(
                        Stock.is_active == True,
                        StockQuote.trade_date == today
                    )
                )
                .order_by(order_func(sort_column))
                .limit(limit)
            )
            
            result = await self.db.execute(stmt)
            rows = result.fetchall()
            
            hot_stocks = []
            for stock, quote in rows:
                hot_stock = {
                    "code": stock.code,
                    "name": stock.name,
                    "market": stock.market,
                    "industry": stock.industry,
                    "close_price": float(quote.close_price) if quote.close_price else None,
                    "change": float(quote.change) if quote.change else None,
                    "pct_change": float(quote.pct_change) if quote.pct_change else None,
                    "volume": float(quote.volume) if quote.volume else None,
                    "amount": float(quote.amount) if quote.amount else None,
                    "turnover_rate": float(quote.turnover_rate) if quote.turnover_rate else None,
                }
                hot_stocks.append(hot_stock)
            
            return hot_stocks
            
        except Exception as e:
            logger.error(f"获取热门股票失败: {e}")
            raise
    
    async def trigger_data_refresh(self, stock_code: str) -> str:
        """
        触发数据刷新任务
        
        Args:
            stock_code: 股票代码
            
        Returns:
            任务ID
        """
        try:
            # 触发实时行情采集
            task = collect_realtime_quotes_task.delay([stock_code])
            
            logger.info(f"已触发股票 {stock_code} 数据刷新任务，任务ID: {task.id}")
            return task.id
            
        except Exception as e:
            logger.error(f"触发数据刷新失败: {e}")
            raise
    
    async def get_financial_summary(self, stock_id: str) -> Optional[Dict[str, Any]]:
        """
        获取财务摘要
        
        Args:
            stock_id: 股票ID
            
        Returns:
            财务摘要数据
        """
        try:
            # 获取最新财务数据
            stmt = (
                select(FinancialData)
                .where(FinancialData.stock_id == stock_id)
                .order_by(desc(FinancialData.report_date))
                .limit(1)
            )
            
            result = await self.db.execute(stmt)
            financial_data = result.scalar_one_or_none()
            
            if not financial_data:
                return None
            
            summary = {
                "report_date": financial_data.report_date.isoformat(),
                "report_type": financial_data.report_type,
                "revenue": float(financial_data.revenue) if financial_data.revenue else None,
                "net_profit": float(financial_data.net_profit) if financial_data.net_profit else None,
                "roe": float(financial_data.roe) if financial_data.roe else None,
                "roa": float(financial_data.roa) if financial_data.roa else None,
                "gross_margin": float(financial_data.gross_margin) if financial_data.gross_margin else None,
                "net_margin": float(financial_data.net_margin) if financial_data.net_margin else None,
                "debt_ratio": float(financial_data.debt_ratio) if financial_data.debt_ratio else None,
                "eps": float(financial_data.eps) if financial_data.eps else None,
                "bps": float(financial_data.bps) if financial_data.bps else None,
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"获取财务摘要失败: {e}")
            raise
    
    async def get_stock_statistics(self) -> Dict[str, Any]:
        """
        获取股票统计信息
        
        Returns:
            统计信息
        """
        try:
            # 总股票数
            total_stmt = select(func.count(Stock.id)).where(Stock.is_active == True)
            total_result = await self.db.execute(total_stmt)
            total_stocks = total_result.scalar()
            
            # 按市场统计
            market_stmt = (
                select(Stock.market, func.count(Stock.id))
                .where(Stock.is_active == True)
                .group_by(Stock.market)
            )
            market_result = await self.db.execute(market_stmt)
            market_stats = {market: count for market, count in market_result.fetchall()}
            
            # 按行业统计（前10）
            industry_stmt = (
                select(Stock.industry, func.count(Stock.id))
                .where(and_(Stock.is_active == True, Stock.industry.isnot(None)))
                .group_by(Stock.industry)
                .order_by(desc(func.count(Stock.id)))
                .limit(10)
            )
            industry_result = await self.db.execute(industry_stmt)
            industry_stats = {industry: count for industry, count in industry_result.fetchall()}
            
            return {
                "total_stocks": total_stocks,
                "market_distribution": market_stats,
                "top_industries": industry_stats,
                "last_updated": datetime.utcnow().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"获取股票统计信息失败: {e}")
            raise
