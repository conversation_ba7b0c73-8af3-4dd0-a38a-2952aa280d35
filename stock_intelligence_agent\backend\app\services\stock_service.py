"""
股票服务层
提供股票相关的业务逻辑处理
"""

import logging
import numpy as np
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, asc, func
import redis.asyncio as redis

from app.models.stock import Stock, StockQuote, RealtimeQuote, TechnicalIndicator, FinancialData
from app.data_collector.tasks import (
    collect_realtime_quotes_task,
    collect_historical_quotes_task,
    collect_financial_data_task
)
from app.core.config import settings

logger = logging.getLogger(__name__)


class StockService:
    """股票服务类"""
    
    def __init__(self, db: AsyncSession, redis_client: Optional[redis.Redis] = None):
        self.db = db
        self.redis = redis_client
    
    async def get_stocks(
        self,
        skip: int = 0,
        limit: int = 100,
        market: Optional[str] = None,
        industry: Optional[str] = None,
        search: Optional[str] = None,
    ) -> Tu<PERSON>[List[Stock], int]:
        """
        获取股票列表
        
        Args:
            skip: 跳过记录数
            limit: 返回记录数
            market: 市场筛选
            industry: 行业筛选
            search: 搜索关键词
            
        Returns:
            (股票列表, 总数)
        """
        try:
            # 构建查询条件
            conditions = [Stock.is_active == True]
            
            if market:
                conditions.append(Stock.market == market.upper())
            
            if industry:
                conditions.append(Stock.industry == industry)
            
            if search:
                search_condition = or_(
                    Stock.code.ilike(f"%{search}%"),
                    Stock.name.ilike(f"%{search}%")
                )
                conditions.append(search_condition)
            
            # 查询总数
            count_stmt = select(func.count(Stock.id)).where(and_(*conditions))
            count_result = await self.db.execute(count_stmt)
            total = count_result.scalar()
            
            # 查询数据
            stmt = select(Stock).where(and_(*conditions)).offset(skip).limit(limit).order_by(Stock.code)
            result = await self.db.execute(stmt)
            stocks = result.scalars().all()
            
            return list(stocks), total
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            raise
    
    async def get_stock_by_code(self, stock_code: str) -> Optional[Stock]:
        """
        根据股票代码获取股票信息
        
        Args:
            stock_code: 股票代码
            
        Returns:
            股票信息
        """
        try:
            stmt = select(Stock).where(Stock.code == stock_code)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"获取股票信息失败: {e}")
            raise
    
    async def get_stock_quotes(
        self,
        stock_id: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 100,
    ) -> List[StockQuote]:
        """
        获取股票历史行情
        
        Args:
            stock_id: 股票ID
            start_date: 开始日期
            end_date: 结束日期
            limit: 返回记录数
            
        Returns:
            历史行情列表
        """
        try:
            conditions = [StockQuote.stock_id == stock_id]
            
            if start_date:
                conditions.append(StockQuote.trade_date >= start_date)
            
            if end_date:
                conditions.append(StockQuote.trade_date <= end_date)
            
            stmt = (
                select(StockQuote)
                .where(and_(*conditions))
                .order_by(desc(StockQuote.trade_date))
                .limit(limit)
            )
            
            result = await self.db.execute(stmt)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error(f"获取股票行情失败: {e}")
            raise
    
    async def get_realtime_quote_from_cache(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """
        从缓存获取实时行情
        
        Args:
            stock_code: 股票代码
            
        Returns:
            实时行情数据
        """
        try:
            if not self.redis:
                return None
            
            cache_key = f"realtime_quote:{stock_code}"
            cached_data = await self.redis.hgetall(cache_key)
            
            if not cached_data:
                return None
            
            # 转换数据类型
            realtime_quote = {
                'code': stock_code,
                'current_price': float(cached_data.get('current_price', 0)),
                'change': float(cached_data.get('change', 0)),
                'pct_change': float(cached_data.get('pct_change', 0)),
                'volume': float(cached_data.get('volume', 0)),
                'amount': float(cached_data.get('amount', 0)),
                'timestamp': datetime.fromisoformat(cached_data.get('timestamp', datetime.now().isoformat())),
            }
            
            return realtime_quote
            
        except Exception as e:
            logger.error(f"从缓存获取实时行情失败: {e}")
            return None
    
    async def get_latest_realtime_quote(self, stock_id: str) -> Optional[RealtimeQuote]:
        """
        获取最新实时行情
        
        Args:
            stock_id: 股票ID
            
        Returns:
            最新实时行情
        """
        try:
            stmt = (
                select(RealtimeQuote)
                .where(RealtimeQuote.stock_id == stock_id)
                .order_by(desc(RealtimeQuote.timestamp))
                .limit(1)
            )
            
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"获取最新实时行情失败: {e}")
            raise
    
    async def get_technical_analysis(
        self,
        stock_id: str,
        period: str = "daily",
        indicators: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        获取技术分析数据
        
        Args:
            stock_id: 股票ID
            period: 周期
            indicators: 指标列表
            
        Returns:
            技术分析数据
        """
        try:
            # 获取最近的技术指标数据
            stmt = (
                select(TechnicalIndicator)
                .where(TechnicalIndicator.stock_id == stock_id)
                .order_by(desc(TechnicalIndicator.trade_date))
                .limit(100)
            )
            
            result = await self.db.execute(stmt)
            technical_data = list(result.scalars().all())
            
            if not technical_data:
                return {"message": "暂无技术分析数据"}
            
            # 构建返回数据
            analysis_result = {
                "period": period,
                "data_count": len(technical_data),
                "latest_date": technical_data[0].trade_date.isoformat() if technical_data else None,
                "indicators": {}
            }
            
            # 如果指定了特定指标，只返回这些指标
            if indicators:
                for indicator in indicators:
                    if hasattr(technical_data[0], indicator):
                        analysis_result["indicators"][indicator] = [
                            getattr(item, indicator) for item in technical_data
                            if getattr(item, indicator) is not None
                        ]
            else:
                # 返回所有指标
                latest = technical_data[0]
                analysis_result["indicators"] = {
                    "ma5": float(latest.ma5) if latest.ma5 else None,
                    "ma10": float(latest.ma10) if latest.ma10 else None,
                    "ma20": float(latest.ma20) if latest.ma20 else None,
                    "ma60": float(latest.ma60) if latest.ma60 else None,
                    "macd_dif": float(latest.macd_dif) if latest.macd_dif else None,
                    "macd_dea": float(latest.macd_dea) if latest.macd_dea else None,
                    "rsi6": float(latest.rsi6) if latest.rsi6 else None,
                    "rsi12": float(latest.rsi12) if latest.rsi12 else None,
                    "kdj_k": float(latest.kdj_k) if latest.kdj_k else None,
                    "kdj_d": float(latest.kdj_d) if latest.kdj_d else None,
                    "kdj_j": float(latest.kdj_j) if latest.kdj_j else None,
                }
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"获取技术分析失败: {e}")
            raise
    
    async def search_stocks(self, query: str, limit: int = 10) -> List[Stock]:
        """
        搜索股票
        
        Args:
            query: 搜索关键词
            limit: 返回记录数
            
        Returns:
            股票列表
        """
        try:
            # 构建搜索条件
            search_condition = or_(
                Stock.code.ilike(f"%{query}%"),
                Stock.name.ilike(f"%{query}%")
            )
            
            stmt = (
                select(Stock)
                .where(and_(Stock.is_active == True, search_condition))
                .order_by(
                    # 优先显示代码完全匹配的
                    func.case(
                        (Stock.code == query, 1),
                        else_=2
                    ),
                    # 然后按代码排序
                    Stock.code
                )
                .limit(limit)
            )
            
            result = await self.db.execute(stmt)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error(f"搜索股票失败: {e}")
            raise
    
    async def get_hot_stocks(
        self,
        limit: int = 20,
        sort_by: str = "pct_change",
        order: str = "desc",
    ) -> List[Dict[str, Any]]:
        """
        获取热门股票
        
        Args:
            limit: 返回记录数
            sort_by: 排序字段
            order: 排序方向
            
        Returns:
            热门股票列表
        """
        try:
            # 获取今日最新行情数据
            today = date.today()
            
            # 构建排序条件
            sort_column = getattr(StockQuote, sort_by, StockQuote.pct_change)
            order_func = desc if order == "desc" else asc
            
            stmt = (
                select(Stock, StockQuote)
                .join(StockQuote, Stock.id == StockQuote.stock_id)
                .where(
                    and_(
                        Stock.is_active == True,
                        StockQuote.trade_date == today
                    )
                )
                .order_by(order_func(sort_column))
                .limit(limit)
            )
            
            result = await self.db.execute(stmt)
            rows = result.fetchall()
            
            hot_stocks = []
            for stock, quote in rows:
                hot_stock = {
                    "code": stock.code,
                    "name": stock.name,
                    "market": stock.market,
                    "industry": stock.industry,
                    "close_price": float(quote.close_price) if quote.close_price else None,
                    "change": float(quote.change) if quote.change else None,
                    "pct_change": float(quote.pct_change) if quote.pct_change else None,
                    "volume": float(quote.volume) if quote.volume else None,
                    "amount": float(quote.amount) if quote.amount else None,
                    "turnover_rate": float(quote.turnover_rate) if quote.turnover_rate else None,
                }
                hot_stocks.append(hot_stock)
            
            return hot_stocks
            
        except Exception as e:
            logger.error(f"获取热门股票失败: {e}")
            raise
    
    async def trigger_data_refresh(self, stock_code: str) -> str:
        """
        触发数据刷新任务
        
        Args:
            stock_code: 股票代码
            
        Returns:
            任务ID
        """
        try:
            # 触发实时行情采集
            task = collect_realtime_quotes_task.delay([stock_code])
            
            logger.info(f"已触发股票 {stock_code} 数据刷新任务，任务ID: {task.id}")
            return task.id
            
        except Exception as e:
            logger.error(f"触发数据刷新失败: {e}")
            raise
    
    async def get_financial_summary(self, stock_id: str) -> Optional[Dict[str, Any]]:
        """
        获取财务摘要
        
        Args:
            stock_id: 股票ID
            
        Returns:
            财务摘要数据
        """
        try:
            # 获取最新财务数据
            stmt = (
                select(FinancialData)
                .where(FinancialData.stock_id == stock_id)
                .order_by(desc(FinancialData.report_date))
                .limit(1)
            )
            
            result = await self.db.execute(stmt)
            financial_data = result.scalar_one_or_none()
            
            if not financial_data:
                return None
            
            summary = {
                "report_date": financial_data.report_date.isoformat(),
                "report_type": financial_data.report_type,
                "revenue": float(financial_data.revenue) if financial_data.revenue else None,
                "net_profit": float(financial_data.net_profit) if financial_data.net_profit else None,
                "roe": float(financial_data.roe) if financial_data.roe else None,
                "roa": float(financial_data.roa) if financial_data.roa else None,
                "gross_margin": float(financial_data.gross_margin) if financial_data.gross_margin else None,
                "net_margin": float(financial_data.net_margin) if financial_data.net_margin else None,
                "debt_ratio": float(financial_data.debt_ratio) if financial_data.debt_ratio else None,
                "eps": float(financial_data.eps) if financial_data.eps else None,
                "bps": float(financial_data.bps) if financial_data.bps else None,
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"获取财务摘要失败: {e}")
            raise
    
    async def get_stock_statistics(self) -> Dict[str, Any]:
        """
        获取股票统计信息
        
        Returns:
            统计信息
        """
        try:
            # 总股票数
            total_stmt = select(func.count(Stock.id)).where(Stock.is_active == True)
            total_result = await self.db.execute(total_stmt)
            total_stocks = total_result.scalar()
            
            # 按市场统计
            market_stmt = (
                select(Stock.market, func.count(Stock.id))
                .where(Stock.is_active == True)
                .group_by(Stock.market)
            )
            market_result = await self.db.execute(market_stmt)
            market_stats = {market: count for market, count in market_result.fetchall()}
            
            # 按行业统计（前10）
            industry_stmt = (
                select(Stock.industry, func.count(Stock.id))
                .where(and_(Stock.is_active == True, Stock.industry.isnot(None)))
                .group_by(Stock.industry)
                .order_by(desc(func.count(Stock.id)))
                .limit(10)
            )
            industry_result = await self.db.execute(industry_stmt)
            industry_stats = {industry: count for industry, count in industry_result.fetchall()}
            
            return {
                "total_stocks": total_stocks,
                "market_distribution": market_stats,
                "top_industries": industry_stats,
                "last_updated": datetime.utcnow().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"获取股票统计信息失败: {e}")
            raise

    async def get_hot_sectors(
        self,
        limit: int = 20,
        sort_by: str = "pct_change",
        order: str = "desc",
    ) -> List[Dict[str, Any]]:
        """
        获取热门板块

        Args:
            limit: 返回记录数
            sort_by: 排序字段
            order: 排序方向

        Returns:
            热门板块列表
        """
        try:
            today = date.today()

            # 按行业聚合统计
            stmt = (
                select(
                    Stock.industry,
                    func.count(Stock.id).label('stock_count'),
                    func.avg(StockQuote.pct_change).label('avg_pct_change'),
                    func.sum(StockQuote.amount).label('total_amount'),
                    func.avg(StockQuote.turnover_rate).label('avg_turnover_rate')
                )
                .join(StockQuote, Stock.id == StockQuote.stock_id)
                .where(
                    and_(
                        Stock.is_active == True,
                        Stock.industry.isnot(None),
                        StockQuote.trade_date == today
                    )
                )
                .group_by(Stock.industry)
                .having(func.count(Stock.id) >= 3)  # 至少3只股票的板块
            )

            # 添加排序
            if sort_by == "pct_change":
                stmt = stmt.order_by(desc(func.avg(StockQuote.pct_change)) if order == "desc" else asc(func.avg(StockQuote.pct_change)))
            elif sort_by == "amount":
                stmt = stmt.order_by(desc(func.sum(StockQuote.amount)) if order == "desc" else asc(func.sum(StockQuote.amount)))

            stmt = stmt.limit(limit)

            result = await self.db.execute(stmt)
            rows = result.fetchall()

            hot_sectors = []
            for row in rows:
                sector = {
                    "sector_name": row.industry,
                    "stock_count": row.stock_count,
                    "avg_pct_change": float(row.avg_pct_change) if row.avg_pct_change else 0,
                    "total_amount": float(row.total_amount) if row.total_amount else 0,
                    "avg_turnover_rate": float(row.avg_turnover_rate) if row.avg_turnover_rate else 0,
                }
                hot_sectors.append(sector)

            return hot_sectors

        except Exception as e:
            logger.error(f"获取热门板块失败: {e}")
            raise

    async def get_sector_funds_flow(
        self,
        sector: Optional[str] = None,
        date_filter: Optional[date] = None,
        limit: int = 20,
    ) -> List[Dict[str, Any]]:
        """
        获取板块资金流向

        Args:
            sector: 板块名称
            date_filter: 日期筛选
            limit: 返回记录数

        Returns:
            资金流向数据
        """
        try:
            if not date_filter:
                date_filter = date.today()

            conditions = [
                Stock.is_active == True,
                StockQuote.trade_date == date_filter
            ]

            if sector:
                conditions.append(Stock.industry == sector)

            stmt = (
                select(
                    Stock.industry,
                    func.sum(StockQuote.amount).label('total_amount'),
                    func.sum(
                        func.case(
                            (StockQuote.pct_change > 0, StockQuote.amount),
                            else_=0
                        )
                    ).label('inflow_amount'),
                    func.sum(
                        func.case(
                            (StockQuote.pct_change < 0, StockQuote.amount),
                            else_=0
                        )
                    ).label('outflow_amount'),
                    func.count(Stock.id).label('stock_count')
                )
                .join(StockQuote, Stock.id == StockQuote.stock_id)
                .where(and_(*conditions))
                .group_by(Stock.industry)
                .order_by(desc(func.sum(StockQuote.amount)))
                .limit(limit)
            )

            result = await self.db.execute(stmt)
            rows = result.fetchall()

            funds_flow = []
            for row in rows:
                if row.industry:
                    total_amount = float(row.total_amount) if row.total_amount else 0
                    inflow_amount = float(row.inflow_amount) if row.inflow_amount else 0
                    outflow_amount = float(row.outflow_amount) if row.outflow_amount else 0
                    net_flow = inflow_amount - outflow_amount

                    flow_data = {
                        "sector_name": row.industry,
                        "total_amount": total_amount,
                        "inflow_amount": inflow_amount,
                        "outflow_amount": outflow_amount,
                        "net_flow": net_flow,
                        "net_flow_ratio": (net_flow / total_amount * 100) if total_amount > 0 else 0,
                        "stock_count": row.stock_count,
                    }
                    funds_flow.append(flow_data)

            return funds_flow

        except Exception as e:
            logger.error(f"获取板块资金流向失败: {e}")
            raise

    async def get_sector_ranking(
        self,
        sort_by: str = "pct_change",
        order: str = "desc",
        limit: int = 50,
    ) -> List[Dict[str, Any]]:
        """
        获取板块排行榜

        Args:
            sort_by: 排序字段
            order: 排序方向
            limit: 返回记录数

        Returns:
            板块排行数据
        """
        try:
            today = date.today()

            stmt = (
                select(
                    Stock.industry,
                    func.count(Stock.id).label('stock_count'),
                    func.avg(StockQuote.pct_change).label('avg_pct_change'),
                    func.sum(StockQuote.amount).label('total_amount'),
                    func.avg(StockQuote.turnover_rate).label('avg_turnover_rate'),
                    func.max(StockQuote.pct_change).label('max_pct_change'),
                    func.min(StockQuote.pct_change).label('min_pct_change')
                )
                .join(StockQuote, Stock.id == StockQuote.stock_id)
                .where(
                    and_(
                        Stock.is_active == True,
                        Stock.industry.isnot(None),
                        StockQuote.trade_date == today
                    )
                )
                .group_by(Stock.industry)
                .having(func.count(Stock.id) >= 2)
            )

            # 添加排序
            if sort_by == "pct_change":
                order_column = func.avg(StockQuote.pct_change)
            elif sort_by == "amount":
                order_column = func.sum(StockQuote.amount)
            else:
                order_column = func.avg(StockQuote.pct_change)

            stmt = stmt.order_by(desc(order_column) if order == "desc" else asc(order_column))
            stmt = stmt.limit(limit)

            result = await self.db.execute(stmt)
            rows = result.fetchall()

            ranking = []
            for i, row in enumerate(rows, 1):
                sector_data = {
                    "rank": i,
                    "sector_name": row.industry,
                    "stock_count": row.stock_count,
                    "avg_pct_change": float(row.avg_pct_change) if row.avg_pct_change else 0,
                    "total_amount": float(row.total_amount) if row.total_amount else 0,
                    "avg_turnover_rate": float(row.avg_turnover_rate) if row.avg_turnover_rate else 0,
                    "max_pct_change": float(row.max_pct_change) if row.max_pct_change else 0,
                    "min_pct_change": float(row.min_pct_change) if row.min_pct_change else 0,
                }
                ranking.append(sector_data)

            return ranking

        except Exception as e:
            logger.error(f"获取板块排行失败: {e}")
            raise

    async def get_stocks_by_sector(
        self,
        sector_name: str,
        limit: int = 50,
        sort_by: str = "pct_change",
        order: str = "desc",
    ) -> List[Dict[str, Any]]:
        """
        获取板块内股票列表

        Args:
            sector_name: 板块名称
            limit: 返回记录数
            sort_by: 排序字段
            order: 排序方向

        Returns:
            板块内股票列表
        """
        try:
            today = date.today()

            # 构建排序条件
            sort_column = getattr(StockQuote, sort_by, StockQuote.pct_change)
            order_func = desc if order == "desc" else asc

            stmt = (
                select(Stock, StockQuote)
                .join(StockQuote, Stock.id == StockQuote.stock_id)
                .where(
                    and_(
                        Stock.is_active == True,
                        Stock.industry == sector_name,
                        StockQuote.trade_date == today
                    )
                )
                .order_by(order_func(sort_column))
                .limit(limit)
            )

            result = await self.db.execute(stmt)
            rows = result.fetchall()

            stocks = []
            for stock, quote in rows:
                stock_data = {
                    "code": stock.code,
                    "name": stock.name,
                    "market": stock.market,
                    "industry": stock.industry,
                    "close_price": float(quote.close_price) if quote.close_price else None,
                    "change": float(quote.change) if quote.change else None,
                    "pct_change": float(quote.pct_change) if quote.pct_change else None,
                    "volume": float(quote.volume) if quote.volume else None,
                    "amount": float(quote.amount) if quote.amount else None,
                    "turnover_rate": float(quote.turnover_rate) if quote.turnover_rate else None,
                    "pe_ratio": float(quote.pe_ratio) if quote.pe_ratio else None,
                    "pb_ratio": float(quote.pb_ratio) if quote.pb_ratio else None,
                }
                stocks.append(stock_data)

            return stocks

        except Exception as e:
            logger.error(f"获取板块股票失败: {e}")
            raise

    async def get_sector_analysis(
        self,
        sector_name: str,
        period: str = "daily",
    ) -> Dict[str, Any]:
        """
        获取板块分析报告

        Args:
            sector_name: 板块名称
            period: 分析周期

        Returns:
            板块分析数据
        """
        try:
            # 获取板块内股票的最近行情数据
            days = 30 if period == "daily" else 90
            start_date = date.today() - timedelta(days=days)

            stmt = (
                select(
                    StockQuote.trade_date,
                    func.avg(StockQuote.pct_change).label('avg_pct_change'),
                    func.sum(StockQuote.amount).label('total_amount'),
                    func.avg(StockQuote.turnover_rate).label('avg_turnover_rate'),
                    func.count(Stock.id).label('stock_count')
                )
                .join(Stock, StockQuote.stock_id == Stock.id)
                .where(
                    and_(
                        Stock.is_active == True,
                        Stock.industry == sector_name,
                        StockQuote.trade_date >= start_date
                    )
                )
                .group_by(StockQuote.trade_date)
                .order_by(desc(StockQuote.trade_date))
            )

            result = await self.db.execute(stmt)
            rows = result.fetchall()

            if not rows:
                return {"error": f"板块 {sector_name} 暂无数据"}

            # 计算统计指标
            pct_changes = [float(row.avg_pct_change) for row in rows if row.avg_pct_change]
            amounts = [float(row.total_amount) for row in rows if row.total_amount]

            analysis = {
                "sector_name": sector_name,
                "period": period,
                "data_points": len(rows),
                "latest_date": rows[0].trade_date.isoformat() if rows else None,
                "performance": {
                    "avg_pct_change": sum(pct_changes) / len(pct_changes) if pct_changes else 0,
                    "volatility": np.std(pct_changes) if len(pct_changes) > 1 else 0,
                    "max_gain": max(pct_changes) if pct_changes else 0,
                    "max_loss": min(pct_changes) if pct_changes else 0,
                },
                "trading": {
                    "avg_amount": sum(amounts) / len(amounts) if amounts else 0,
                    "total_amount": sum(amounts) if amounts else 0,
                    "avg_stock_count": sum(row.stock_count for row in rows) / len(rows) if rows else 0,
                },
                "trend": self._analyze_sector_trend(pct_changes),
            }

            return analysis

        except Exception as e:
            logger.error(f"获取板块分析失败: {e}")
            raise

    def _analyze_sector_trend(self, pct_changes: List[float]) -> str:
        """分析板块趋势"""
        if len(pct_changes) < 5:
            return "数据不足"

        recent_5 = pct_changes[:5]
        avg_recent = sum(recent_5) / len(recent_5)

        if avg_recent > 2:
            return "强势上涨"
        elif avg_recent > 0.5:
            return "温和上涨"
        elif avg_recent > -0.5:
            return "横盘整理"
        elif avg_recent > -2:
            return "温和下跌"
        else:
            return "强势下跌"
