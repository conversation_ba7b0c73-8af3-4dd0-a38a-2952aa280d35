/**
 * API 服务层
 * 统一管理所有 API 请求
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { message } from 'antd';
import { 
  BaseResponse, 
  PaginationResponse, 
  LoginRequest, 
  LoginResponse,
  User,
  Stock,
  StockQuote,
  RealtimeQuote,
  News,
  Alert,
  Watchlist,
  WatchlistWithItems,
  MarketOverview,
  SearchResponse,
  HotStock,
  TechnicalAnalysis
} from '@/types';

// API 基础配置
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

class ApiService {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加认证令牌
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error) => {
        const { response } = error;
        
        if (response?.status === 401) {
          // 令牌过期，尝试刷新
          const refreshToken = localStorage.getItem('refresh_token');
          if (refreshToken) {
            try {
              const newTokens = await this.refreshToken(refreshToken);
              localStorage.setItem('access_token', newTokens.access_token);
              localStorage.setItem('refresh_token', newTokens.refresh_token);
              
              // 重试原请求
              const originalRequest = error.config;
              originalRequest.headers.Authorization = `Bearer ${newTokens.access_token}`;
              return this.instance.request(originalRequest);
            } catch (refreshError) {
              // 刷新失败，清除令牌并跳转登录
              this.clearTokens();
              window.location.href = '/login';
            }
          } else {
            this.clearTokens();
            window.location.href = '/login';
          }
        } else if (response?.status >= 500) {
          message.error('服务器错误，请稍后重试');
        } else if (response?.data?.message) {
          message.error(response.data.message);
        }

        return Promise.reject(error);
      }
    );
  }

  private clearTokens() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
  }

  // 通用请求方法
  private async request<T>(config: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.request<T>(config);
    return response.data;
  }

  // 认证相关 API
  async login(data: LoginRequest): Promise<LoginResponse> {
    return this.request<LoginResponse>({
      method: 'POST',
      url: '/api/v1/auth/login',
      data,
    });
  }

  async refreshToken(refreshToken: string): Promise<LoginResponse> {
    return this.request<LoginResponse>({
      method: 'POST',
      url: '/api/v1/auth/refresh',
      data: { refresh_token: refreshToken },
    });
  }

  async logout(): Promise<void> {
    return this.request<void>({
      method: 'POST',
      url: '/api/v1/auth/logout',
    });
  }

  async register(data: {
    username: string;
    email: string;
    password: string;
    full_name?: string;
  }): Promise<User> {
    return this.request<User>({
      method: 'POST',
      url: '/api/v1/auth/register',
      data,
    });
  }

  // 用户相关 API
  async getCurrentUser(): Promise<User> {
    return this.request<User>({
      method: 'GET',
      url: '/api/v1/users/me',
    });
  }

  async updateUser(data: Partial<User>): Promise<User> {
    return this.request<User>({
      method: 'PUT',
      url: '/api/v1/users/me',
      data,
    });
  }

  async changePassword(data: {
    current_password: string;
    new_password: string;
    confirm_password: string;
  }): Promise<void> {
    return this.request<void>({
      method: 'POST',
      url: '/api/v1/users/change-password',
      data,
    });
  }

  // 股票相关 API
  async getStocks(params: {
    skip?: number;
    limit?: number;
    search?: string;
    market?: string;
    industry?: string;
  } = {}): Promise<PaginationResponse<Stock>> {
    return this.request<PaginationResponse<Stock>>({
      method: 'GET',
      url: '/api/v1/stocks',
      params,
    });
  }

  async getStock(stockCode: string): Promise<Stock> {
    return this.request<Stock>({
      method: 'GET',
      url: `/api/v1/stocks/${stockCode}`,
    });
  }

  async getStockQuotes(
    stockCode: string,
    params: {
      start_date?: string;
      end_date?: string;
      limit?: number;
    } = {}
  ): Promise<StockQuote[]> {
    return this.request<StockQuote[]>({
      method: 'GET',
      url: `/api/v1/stocks/${stockCode}/quotes`,
      params,
    });
  }

  async getRealtimeQuote(stockCode: string): Promise<RealtimeQuote> {
    return this.request<RealtimeQuote>({
      method: 'GET',
      url: `/api/v1/stocks/${stockCode}/realtime`,
    });
  }

  async getHotStocks(params: {
    limit?: number;
    sort_by?: string;
    order?: string;
  } = {}): Promise<HotStock[]> {
    return this.request<HotStock[]>({
      method: 'GET',
      url: '/api/v1/stocks/hot',
      params,
    });
  }

  async searchStocks(query: string): Promise<SearchResponse> {
    return this.request<SearchResponse>({
      method: 'GET',
      url: '/api/v1/stocks/search',
      params: { q: query },
    });
  }

  async getTechnicalAnalysis(stockCode: string): Promise<TechnicalAnalysis> {
    return this.request<TechnicalAnalysis>({
      method: 'GET',
      url: `/api/v1/stocks/${stockCode}/technical-analysis`,
    });
  }

  // 新闻相关 API
  async getNews(params: {
    skip?: number;
    limit?: number;
    category_id?: string;
    source?: string;
    start_date?: string;
    end_date?: string;
    importance_min?: number;
    sentiment?: string;
    search_query?: string;
  } = {}): Promise<PaginationResponse<News>> {
    return this.request<PaginationResponse<News>>({
      method: 'GET',
      url: '/api/v1/news',
      params,
    });
  }

  async getNewsById(newsId: string): Promise<News> {
    return this.request<News>({
      method: 'GET',
      url: `/api/v1/news/${newsId}`,
    });
  }

  async getHotNews(params: {
    limit?: number;
    hours?: number;
  } = {}): Promise<News[]> {
    return this.request<News[]>({
      method: 'GET',
      url: '/api/v1/news/hot',
      params,
    });
  }

  async getNewsByStock(stockCode: string, limit: number = 20): Promise<News[]> {
    return this.request<News[]>({
      method: 'GET',
      url: `/api/v1/news/stock/${stockCode}`,
      params: { limit },
    });
  }

  // 预警相关 API
  async getAlerts(params: {
    skip?: number;
    limit?: number;
    is_active?: boolean;
    stock_code?: string;
  } = {}): Promise<PaginationResponse<Alert>> {
    return this.request<PaginationResponse<Alert>>({
      method: 'GET',
      url: '/api/v1/alerts',
      params,
    });
  }

  async createAlert(data: {
    name: string;
    stock_code: string;
    alert_type: string;
    condition: Record<string, any>;
    notification_methods?: string[];
  }): Promise<Alert> {
    return this.request<Alert>({
      method: 'POST',
      url: '/api/v1/alerts',
      data,
    });
  }

  async updateAlert(alertId: string, data: Partial<Alert>): Promise<Alert> {
    return this.request<Alert>({
      method: 'PUT',
      url: `/api/v1/alerts/${alertId}`,
      data,
    });
  }

  async deleteAlert(alertId: string): Promise<void> {
    return this.request<void>({
      method: 'DELETE',
      url: `/api/v1/alerts/${alertId}`,
    });
  }

  async toggleAlert(alertId: string): Promise<Alert> {
    return this.request<Alert>({
      method: 'POST',
      url: `/api/v1/alerts/${alertId}/toggle`,
    });
  }

  // 自选股相关 API
  async getWatchlists(): Promise<Watchlist[]> {
    return this.request<Watchlist[]>({
      method: 'GET',
      url: '/api/v1/watchlists',
    });
  }

  async getWatchlist(watchlistId: string): Promise<WatchlistWithItems> {
    return this.request<WatchlistWithItems>({
      method: 'GET',
      url: `/api/v1/watchlists/${watchlistId}`,
    });
  }

  async createWatchlist(data: {
    name: string;
    description?: string;
    is_public?: boolean;
  }): Promise<Watchlist> {
    return this.request<Watchlist>({
      method: 'POST',
      url: '/api/v1/watchlists',
      data,
    });
  }

  async updateWatchlist(watchlistId: string, data: Partial<Watchlist>): Promise<Watchlist> {
    return this.request<Watchlist>({
      method: 'PUT',
      url: `/api/v1/watchlists/${watchlistId}`,
      data,
    });
  }

  async deleteWatchlist(watchlistId: string): Promise<void> {
    return this.request<void>({
      method: 'DELETE',
      url: `/api/v1/watchlists/${watchlistId}`,
    });
  }

  async addStockToWatchlist(
    watchlistId: string,
    stockCode: string,
    notes?: string
  ): Promise<void> {
    return this.request<void>({
      method: 'POST',
      url: `/api/v1/watchlists/${watchlistId}/stocks`,
      data: { stock_code: stockCode, notes },
    });
  }

  async removeStockFromWatchlist(watchlistId: string, stockCode: string): Promise<void> {
    return this.request<void>({
      method: 'DELETE',
      url: `/api/v1/watchlists/${watchlistId}/stocks/${stockCode}`,
    });
  }

  // 市场概览 API
  async getMarketOverview(): Promise<MarketOverview> {
    return this.request<MarketOverview>({
      method: 'GET',
      url: '/api/v1/market/overview',
    });
  }

  // 健康检查 API
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.request<{ status: string; timestamp: string }>({
      method: 'GET',
      url: '/api/v1/health',
    });
  }
}

// 导出单例实例
export const apiService = new ApiService();
export default apiService;
