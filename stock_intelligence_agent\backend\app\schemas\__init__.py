"""
Pydantic 模型包
"""

from .stock import (
    StockBase,
    StockCreate,
    StockUpdate,
    StockResponse,
    StockListResponse,
    StockQuoteBase,
    StockQuoteCreate,
    StockQuoteResponse,
    RealtimeQuoteBase,
    RealtimeQuoteCreate,
    RealtimeQuoteResponse,
    TechnicalIndicatorBase,
    TechnicalIndicatorResponse,
    FinancialDataBase,
    FinancialDataResponse,
    StockSearchSuggestion,
    StockSearchResponse,
    HotStock,
    HotStocksResponse,
    StockStatistics,
    DataRefreshTask,
)

from .analysis import (
    TechnicalAnalysisRequest,
    TechnicalIndicators,
    TechnicalAnalysisResponse,
    TrendAnalysisResponse,
    ProfitabilityAnalysis,
    GrowthAnalysis,
    SolvencyAnalysis,
    EfficiencyAnalysis,
    ValuationAnalysis,
    FundamentalAnalysisResponse,
    RiskMetrics,
    RiskAssessmentResponse,
    PortfolioAnalysisRequest,
    PortfolioMetrics,
    PortfolioHolding,
    PortfolioAnalysisResponse,
    MarketSentimentIndicator,
    MarketSentimentResponse,
    AnalysisTask,
    AnalysisTaskResponse,
    BatchAnalysisRequest,
    BatchAnalysisResponse,
    ComparisonRequest,
    ComparisonMetric,
    ComparisonResponse,
)

from .news import (
    NewsBase,
    NewsCreate,
    NewsUpdate,
    NewsResponse,
    NewsListResponse,
    NewsCategoryBase,
    NewsCategoryCreate,
    NewsCategoryResponse,
    NewsKeywordBase,
    NewsKeywordResponse,
    NewsSearchRequest,
    NewsAnalyticsRequest,
    SentimentAnalysis,
    TopicAnalysis,
    NewsAnalyticsResponse,
    NewsRecommendationRequest,
    NewsRecommendationResponse,
    NewsAlert,
    NewsAlertResponse,
    NewsStatistics,
    NewsCollectionTask,
    NewsCollectionResponse,
    NewsExportRequest,
    NewsExportResponse,
    NewsSubscription,
    NewsSubscriptionResponse,
)

from .user import (
    UserBase,
    UserCreate,
    UserUpdate,
    UserResponse,
    UserLogin,
    UserLoginResponse,
    TokenRefresh,
    PasswordChange,
    PasswordReset,
    PasswordResetConfirm,
    UserProfileBase,
    UserProfileUpdate,
    UserProfileResponse,
    AlertBase,
    AlertCreate,
    AlertUpdate,
    AlertResponse,
    AlertTrigger,
    AlertTriggerResponse,
    WatchlistBase,
    WatchlistCreate,
    WatchlistUpdate,
    WatchlistItemBase,
    WatchlistItemCreate,
    WatchlistItemResponse,
    WatchlistResponse,
    UserPreferences,
    UserPreferencesResponse,
    UserActivity,
    UserActivityResponse,
    UserStatistics,
    UserListResponse,
    UserSearchRequest,
)

__all__ = [
    # Stock schemas
    "StockBase",
    "StockCreate",
    "StockUpdate", 
    "StockResponse",
    "StockListResponse",
    "StockQuoteBase",
    "StockQuoteCreate",
    "StockQuoteResponse",
    "RealtimeQuoteBase",
    "RealtimeQuoteCreate",
    "RealtimeQuoteResponse",
    "TechnicalIndicatorBase",
    "TechnicalIndicatorResponse",
    "FinancialDataBase",
    "FinancialDataResponse",
    "StockSearchSuggestion",
    "StockSearchResponse",
    "HotStock",
    "HotStocksResponse",
    "StockStatistics",
    "DataRefreshTask",
    
    # Analysis schemas
    "TechnicalAnalysisRequest",
    "TechnicalIndicators",
    "TechnicalAnalysisResponse",
    "TrendAnalysisResponse",
    "ProfitabilityAnalysis",
    "GrowthAnalysis",
    "SolvencyAnalysis",
    "EfficiencyAnalysis",
    "ValuationAnalysis",
    "FundamentalAnalysisResponse",
    "RiskMetrics",
    "RiskAssessmentResponse",
    "PortfolioAnalysisRequest",
    "PortfolioMetrics",
    "PortfolioHolding",
    "PortfolioAnalysisResponse",
    "MarketSentimentIndicator",
    "MarketSentimentResponse",
    "AnalysisTask",
    "AnalysisTaskResponse",
    "BatchAnalysisRequest",
    "BatchAnalysisResponse",
    "ComparisonRequest",
    "ComparisonMetric",
    "ComparisonResponse",
    
    # News schemas
    "NewsBase",
    "NewsCreate",
    "NewsUpdate",
    "NewsResponse",
    "NewsListResponse",
    "NewsCategoryBase",
    "NewsCategoryCreate",
    "NewsCategoryResponse",
    "NewsKeywordBase",
    "NewsKeywordResponse",
    "NewsSearchRequest",
    "NewsAnalyticsRequest",
    "SentimentAnalysis",
    "TopicAnalysis",
    "NewsAnalyticsResponse",
    "NewsRecommendationRequest",
    "NewsRecommendationResponse",
    "NewsAlert",
    "NewsAlertResponse",
    "NewsStatistics",
    "NewsCollectionTask",
    "NewsCollectionResponse",
    "NewsExportRequest",
    "NewsExportResponse",
    "NewsSubscription",
    "NewsSubscriptionResponse",
    
    # User schemas
    "UserBase",
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserLogin",
    "UserLoginResponse",
    "TokenRefresh",
    "PasswordChange",
    "PasswordReset",
    "PasswordResetConfirm",
    "UserProfileBase",
    "UserProfileUpdate",
    "UserProfileResponse",
    "AlertBase",
    "AlertCreate",
    "AlertUpdate",
    "AlertResponse",
    "AlertTrigger",
    "AlertTriggerResponse",
    "WatchlistBase",
    "WatchlistCreate",
    "WatchlistUpdate",
    "WatchlistItemBase",
    "WatchlistItemCreate",
    "WatchlistItemResponse",
    "WatchlistResponse",
    "UserPreferences",
    "UserPreferencesResponse",
    "UserActivity",
    "UserActivityResponse",
    "UserStatistics",
    "UserListResponse",
    "UserSearchRequest",
]
