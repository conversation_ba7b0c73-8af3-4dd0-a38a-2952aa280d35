"""
认证相关 API 端点
"""

from datetime import <PERSON><PERSON><PERSON>
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.services.user_service import UserService
from app.auth.security import (
    verify_token,
    login_rate_limiter,
    check_rate_limit,
    security_manager
)
from app.schemas.auth import (
    LoginRequest,
    LoginResponse,
    RegisterRequest,
    RefreshTokenRequest,
    ChangePasswordRequest,
    UserResponse
)
from app.schemas.base import BaseResponse

router = APIRouter()
security = HTTPBearer()


@router.post("/login", response_model=LoginResponse)
async def login(
    request: Request,
    login_data: LoginRequest,
    db: AsyncSession = Depends(get_db)
):
    """用户登录"""
    # 检查登录速率限制
    client_ip = request.client.host
    check_rate_limit(
        login_rate_limiter,
        client_ip,
        "登录尝试过于频繁，请5分钟后再试"
    )
    
    user_service = UserService(db)
    
    # 验证用户凭据
    user = await user_service.authenticate_user(
        login_data.username,
        login_data.password
    )
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    # 创建令牌
    tokens = await user_service.create_user_tokens(user)
    
    return LoginResponse(
        access_token=tokens["access_token"],
        refresh_token=tokens["refresh_token"],
        token_type=tokens["token_type"],
        expires_in=tokens["expires_in"],
        user=UserResponse.from_orm(user)
    )


@router.post("/register", response_model=UserResponse)
async def register(
    register_data: RegisterRequest,
    db: AsyncSession = Depends(get_db)
):
    """用户注册"""
    user_service = UserService(db)
    
    try:
        user = await user_service.create_user(
            username=register_data.username,
            email=register_data.email,
            password=register_data.password,
            full_name=register_data.full_name
        )
        
        return UserResponse.from_orm(user)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/refresh", response_model=LoginResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
):
    """刷新访问令牌"""
    try:
        # 验证刷新令牌
        payload = verify_token(refresh_data.refresh_token, "refresh")
        user_id = payload.get("sub")
        
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )
        
        # 获取用户信息
        user_service = UserService(db)
        user = await user_service.get_user_by_id(user_id)
        
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在或已被禁用"
            )
        
        # 创建新令牌
        tokens = await user_service.create_user_tokens(user)
        
        return LoginResponse(
            access_token=tokens["access_token"],
            refresh_token=tokens["refresh_token"],
            token_type=tokens["token_type"],
            expires_in=tokens["expires_in"],
            user=UserResponse.from_orm(user)
        )
        
    except HTTPException:
        raise
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="刷新令牌失败"
        )


@router.post("/logout", response_model=BaseResponse)
async def logout():
    """用户登出"""
    # 注意：JWT 是无状态的，实际的令牌失效需要在客户端处理
    # 这里可以添加令牌黑名单逻辑（如果需要的话）
    return BaseResponse(
        success=True,
        message="登出成功"
    )


@router.post("/change-password", response_model=BaseResponse)
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """修改密码"""
    if password_data.new_password != password_data.confirm_password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="新密码和确认密码不匹配"
        )
    
    user_service = UserService(db)
    success = await user_service.change_password(
        user_id=current_user["user_id"],
        current_password=password_data.current_password,
        new_password=password_data.new_password
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码错误"
        )
    
    return BaseResponse(
        success=True,
        message="密码修改成功"
    )


@router.post("/forgot-password", response_model=BaseResponse)
async def forgot_password(
    email: str,
    db: AsyncSession = Depends(get_db)
):
    """忘记密码"""
    # TODO: 实现发送重置密码邮件的逻辑
    return BaseResponse(
        success=True,
        message="密码重置邮件已发送"
    )


@router.post("/reset-password", response_model=BaseResponse)
async def reset_password(
    token: str,
    new_password: str,
    db: AsyncSession = Depends(get_db)
):
    """重置密码"""
    # TODO: 实现密码重置逻辑
    return BaseResponse(
        success=True,
        message="密码重置成功"
    )


@router.get("/me", response_model=UserResponse)
async def get_current_user(
    current_user: dict = Depends(get_current_user_from_token),
    db: AsyncSession = Depends(get_db)
):
    """获取当前用户信息"""
    user_service = UserService(db)
    user = await user_service.get_user_by_id(current_user["user_id"])
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return UserResponse.from_orm(user)


@router.post("/verify-token", response_model=BaseResponse)
async def verify_user_token(
    token: str
):
    """验证令牌有效性"""
    try:
        payload = verify_token(token, "access")
        return BaseResponse(
            success=True,
            message="令牌有效",
            data={"user_id": payload.get("sub")}
        )
    except HTTPException:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌无效或已过期"
        )


# 导入依赖函数
from app.auth.security import get_current_user_from_token
