#!/usr/bin/env python3
"""
同花顺研报数据爬虫
支持登录和免登录两种模式
"""

import requests
import json
import time
import random
from typing import Dict, List, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import logging

logger = logging.getLogger(__name__)

class THSResearchCrawler:
    """同花顺研报爬虫"""
    
    def __init__(self, use_selenium=False, headless=True):
        self.use_selenium = use_selenium
        self.headless = headless
        self.session = requests.Session()
        self.driver = None
        
        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://data.10jqka.com.cn/',
        }
        self.session.headers.update(self.headers)
        
        if self.use_selenium:
            self._init_selenium()
    
    def _init_selenium(self):
        """初始化Selenium WebDriver"""
        try:
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument(f'--user-agent={self.headers["User-Agent"]}')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            logger.info("✅ Selenium WebDriver初始化成功")
            
        except Exception as e:
            logger.error(f"❌ Selenium WebDriver初始化失败: {e}")
            self.use_selenium = False
    
    def login_ths(self, username: str, password: str) -> bool:
        """
        登录同花顺账户
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            bool: 登录是否成功
        """
        if not self.use_selenium:
            logger.warning("⚠️ 需要启用Selenium才能进行登录")
            return False
            
        try:
            # 访问登录页面
            login_url = "https://upass.10jqka.com.cn/login"
            self.driver.get(login_url)
            
            # 等待页面加载
            wait = WebDriverWait(self.driver, 10)
            
            # 输入用户名
            username_input = wait.until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            username_input.clear()
            username_input.send_keys(username)
            
            # 输入密码
            password_input = self.driver.find_element(By.NAME, "password")
            password_input.clear()
            password_input.send_keys(password)
            
            # 点击登录按钮
            login_button = self.driver.find_element(By.CLASS_NAME, "login-btn")
            login_button.click()
            
            # 等待登录完成
            time.sleep(3)
            
            # 检查是否登录成功
            if "upass.10jqka.com.cn" not in self.driver.current_url:
                logger.info("✅ 同花顺登录成功")
                
                # 获取登录后的cookies
                cookies = self.driver.get_cookies()
                for cookie in cookies:
                    self.session.cookies.set(cookie['name'], cookie['value'])
                
                return True
            else:
                logger.error("❌ 同花顺登录失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 登录过程出错: {e}")
            return False
    
    def get_research_reports_by_requests(self, stock_code: str) -> List[Dict[str, Any]]:
        """
        使用requests获取研报数据（免登录）
        
        Args:
            stock_code: 股票代码
            
        Returns:
            List[Dict]: 研报数据列表
        """
        try:
            # 同花顺研报API（可能需要调整）
            api_urls = [
                f"https://data.10jqka.com.cn/ajax/research/stock/{stock_code}",
                f"https://basic.10jqka.com.cn/api/stock/export.php?export=research&type=last&code={stock_code}",
                f"https://d.10jqka.com.cn/v6/line/hs_{stock_code}/01/last.js"
            ]
            
            research_reports = []
            
            for api_url in api_urls:
                try:
                    # 添加随机延迟
                    time.sleep(random.uniform(1, 3))
                    
                    response = self.session.get(api_url, timeout=10)
                    
                    if response.status_code == 200:
                        # 尝试解析JSON数据
                        try:
                            data = response.json()
                            reports = self._parse_ths_research_data(data, stock_code)
                            if reports:
                                research_reports.extend(reports)
                                break
                        except json.JSONDecodeError:
                            # 如果不是JSON，尝试解析其他格式
                            text = response.text
                            if "research" in text.lower():
                                logger.info(f"获取到研报相关数据，但需要进一步解析")
                                
                except Exception as e:
                    logger.warning(f"API {api_url} 请求失败: {e}")
                    continue
            
            return research_reports
            
        except Exception as e:
            logger.error(f"获取研报数据失败: {e}")
            return []
    
    def get_research_reports_by_selenium(self, stock_code: str) -> List[Dict[str, Any]]:
        """
        使用Selenium获取研报数据（支持登录）
        
        Args:
            stock_code: 股票代码
            
        Returns:
            List[Dict]: 研报数据列表
        """
        if not self.use_selenium:
            logger.error("❌ Selenium未初始化")
            return []
            
        try:
            # 访问股票研报页面
            research_url = f"https://basic.10jqka.com.cn/{stock_code}/research.html"
            self.driver.get(research_url)
            
            # 等待页面加载
            wait = WebDriverWait(self.driver, 10)
            
            # 查找研报数据
            research_reports = []
            
            try:
                # 等待研报列表加载
                research_list = wait.until(
                    EC.presence_of_element_located((By.CLASS_NAME, "research-list"))
                )
                
                # 解析研报数据
                report_items = research_list.find_elements(By.CLASS_NAME, "report-item")
                
                for item in report_items[:5]:  # 只取前5条
                    try:
                        title = item.find_element(By.CLASS_NAME, "title").text
                        institution = item.find_element(By.CLASS_NAME, "institution").text
                        date = item.find_element(By.CLASS_NAME, "date").text
                        rating = item.find_element(By.CLASS_NAME, "rating").text if item.find_elements(By.CLASS_NAME, "rating") else ""
                        
                        research_reports.append({
                            'institution': institution,
                            'analyst': '',
                            'rating': rating,
                            'target_price': 0,
                            'publish_date': date,
                            'report_title': title,
                            'key_points': [title]
                        })
                        
                    except Exception as e:
                        logger.warning(f"解析单条研报数据失败: {e}")
                        continue
                        
            except Exception as e:
                logger.warning(f"未找到研报列表: {e}")
                
                # 尝试其他选择器
                try:
                    # 执行JavaScript获取数据
                    script = """
                    return window.research_data || window.reportData || [];
                    """
                    js_data = self.driver.execute_script(script)
                    if js_data:
                        research_reports = self._parse_js_research_data(js_data, stock_code)
                        
                except Exception as js_e:
                    logger.warning(f"JavaScript获取数据失败: {js_e}")
            
            return research_reports
            
        except Exception as e:
            logger.error(f"Selenium获取研报数据失败: {e}")
            return []
    
    def _parse_ths_research_data(self, data: Dict, stock_code: str) -> List[Dict[str, Any]]:
        """解析同花顺研报数据"""
        try:
            research_reports = []
            
            # 根据不同的数据格式进行解析
            if isinstance(data, dict):
                if 'data' in data and isinstance(data['data'], list):
                    for item in data['data'][:5]:
                        research_reports.append({
                            'institution': item.get('institution', ''),
                            'analyst': item.get('analyst', ''),
                            'rating': item.get('rating', ''),
                            'target_price': float(item.get('target_price', 0) or 0),
                            'publish_date': item.get('publish_date', ''),
                            'report_title': item.get('title', ''),
                            'key_points': [item.get('summary', '')]
                        })
                        
            return research_reports
            
        except Exception as e:
            logger.error(f"解析同花顺研报数据失败: {e}")
            return []
    
    def _parse_js_research_data(self, data: List, stock_code: str) -> List[Dict[str, Any]]:
        """解析JavaScript获取的研报数据"""
        try:
            research_reports = []
            
            for item in data[:5]:
                if isinstance(item, dict):
                    research_reports.append({
                        'institution': item.get('org_name', ''),
                        'analyst': item.get('analyst_name', ''),
                        'rating': item.get('rating_name', ''),
                        'target_price': float(item.get('target_price', 0) or 0),
                        'publish_date': item.get('publish_date', ''),
                        'report_title': item.get('report_title', ''),
                        'key_points': [item.get('summary', '')]
                    })
                    
            return research_reports
            
        except Exception as e:
            logger.error(f"解析JavaScript研报数据失败: {e}")
            return []
    
    def get_research_reports(self, stock_code: str, use_login: bool = False, 
                           username: str = None, password: str = None) -> List[Dict[str, Any]]:
        """
        获取研报数据的主入口
        
        Args:
            stock_code: 股票代码
            use_login: 是否使用登录
            username: 用户名（如果需要登录）
            password: 密码（如果需要登录）
            
        Returns:
            List[Dict]: 研报数据列表
        """
        try:
            # 如果需要登录
            if use_login and username and password:
                if not self.use_selenium:
                    self.use_selenium = True
                    self._init_selenium()
                
                if self.login_ths(username, password):
                    return self.get_research_reports_by_selenium(stock_code)
                else:
                    logger.warning("⚠️ 登录失败，尝试免登录方式")
            
            # 优先尝试Selenium方式
            if self.use_selenium:
                reports = self.get_research_reports_by_selenium(stock_code)
                if reports:
                    return reports
            
            # 回退到requests方式
            return self.get_research_reports_by_requests(stock_code)
            
        except Exception as e:
            logger.error(f"获取研报数据失败: {e}")
            return []
    
    def close(self):
        """关闭资源"""
        if self.driver:
            self.driver.quit()
        self.session.close()

# 使用示例
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    # 创建爬虫实例
    crawler = THSResearchCrawler(use_selenium=True, headless=False)
    
    try:
        # 测试获取研报数据
        stock_code = "300750"
        
        print(f"🧪 测试获取 {stock_code} 的研报数据...")
        
        # 免登录方式
        reports = crawler.get_research_reports(stock_code, use_login=False)
        
        if reports:
            print(f"✅ 成功获取 {len(reports)} 条研报数据")
            for i, report in enumerate(reports, 1):
                print(f"{i}. {report}")
        else:
            print("❌ 未获取到研报数据")
            
        # 如果需要登录，取消注释以下代码
        # reports_with_login = crawler.get_research_reports(
        #     stock_code, 
        #     use_login=True, 
        #     username="your_username", 
        #     password="your_password"
        # )
        
    finally:
        crawler.close()
