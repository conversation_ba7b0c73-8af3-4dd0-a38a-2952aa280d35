<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小化测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
        }
        .status.online {
            background: #d4edda;
            color: #155724;
        }
        .status.offline {
            background: #f8d7da;
            color: #721c24;
        }
        .quick-tests {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .quick-test-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .quick-test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            background: #e9ecef;
        }
        .quick-test-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .quick-test-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .quick-test-desc {
            font-size: 0.85em;
            color: #6c757d;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status-loading {
            background: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .json-viewer {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.85em;
            line-height: 1.4;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .debug {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 最小化测试页面</h1>
        
        <div id="status" class="status offline">
            🔴 检查服务状态中...
        </div>
        
        <div class="debug">
            <h4>调试信息</h4>
            <div id="debugInfo">页面加载中...</div>
        </div>
        
        <div class="quick-tests">
            <div class="quick-test-card" onclick="quickTest('health')">
                <div class="quick-test-icon">🔍</div>
                <div class="quick-test-title">健康检查</div>
                <div class="quick-test-desc">检查后端服务状态</div>
            </div>
            
            <div class="quick-test-card" onclick="quickTest('stocks')">
                <div class="quick-test-icon">📊</div>
                <div class="quick-test-title">股票数据</div>
                <div class="quick-test-desc">测试股票列表和详情</div>
            </div>
            
            <div class="quick-test-card" onclick="quickTest('news')">
                <div class="quick-test-icon">📰</div>
                <div class="quick-test-title">新闻分析</div>
                <div class="quick-test-desc">测试新闻和热点话题</div>
            </div>
            
            <div class="quick-test-card" onclick="quickTest('sectors')">
                <div class="quick-test-icon">🏪</div>
                <div class="quick-test-title">板块分析</div>
                <div class="quick-test-desc">测试概念板块功能</div>
            </div>
        </div>

        <div class="result">
            <div id="quickTestResult">
                <p>点击上方测试卡片开始测试...</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        // 调试信息
        function updateDebugInfo(message) {
            const debugDiv = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.innerHTML += `<br>[${timestamp}] ${message}`;
            console.log(`[${timestamp}] ${message}`);
        }
        
        // 页面加载时检查
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo('页面DOM加载完成');
            updateDebugInfo('API地址: ' + API_BASE);
            checkServerStatus();
        });
        
        // 检查服务器状态
        async function checkServerStatus() {
            const statusDiv = document.getElementById('status');
            updateDebugInfo('开始检查服务状态...');
            
            try {
                updateDebugInfo('发送请求到: ' + API_BASE + '/health');
                const response = await fetch(`${API_BASE}/health`);
                updateDebugInfo('收到响应，状态码: ' + response.status);
                
                const data = await response.json();
                updateDebugInfo('响应数据: ' + JSON.stringify(data));
                
                if (data.status === 'healthy') {
                    statusDiv.className = 'status online';
                    statusDiv.innerHTML = '🟢 服务在线';
                    updateDebugInfo('服务状态正常');
                } else {
                    throw new Error('Service unhealthy');
                }
            } catch (error) {
                updateDebugInfo('服务状态检查失败: ' + error.message);
                statusDiv.className = 'status offline';
                statusDiv.innerHTML = '🔴 服务离线 - ' + error.message;
            }
        }
        
        // 快速测试
        async function quickTest(testType) {
            updateDebugInfo(`开始快速测试: ${testType}`);
            const resultDiv = document.getElementById('quickTestResult');
            resultDiv.innerHTML = '<div class="status-loading">测试中...</div>';
            
            try {
                let url, title;
                
                switch(testType) {
                    case 'health':
                        url = '/health';
                        title = '健康检查';
                        break;
                    case 'stocks':
                        url = '/api/v1/stocks?limit=5';
                        title = '股票数据';
                        break;
                    case 'news':
                        url = '/api/v1/news?limit=3';
                        title = '新闻数据';
                        break;
                    case 'sectors':
                        url = '/api/v1/sectors/hot?limit=5';
                        title = '板块数据';
                        break;
                    default:
                        throw new Error('未知的测试类型: ' + testType);
                }
                
                updateDebugInfo(`请求URL: ${API_BASE}${url}`);
                const response = await fetch(`${API_BASE}${url}`);
                updateDebugInfo(`响应状态: ${response.status}`);
                
                const data = await response.json();
                updateDebugInfo(`数据大小: ${JSON.stringify(data).length} 字符`);
                
                resultDiv.innerHTML = `
                    <div class="status-success">✅ ${title}测试成功</div>
                    <div class="json-viewer">${JSON.stringify(data, null, 2)}</div>
                `;
                
                updateDebugInfo(`${title}测试成功`);
                
            } catch (error) {
                updateDebugInfo(`测试失败: ${error.message}`);
                resultDiv.innerHTML = `
                    <div class="status-error">❌ 测试失败: ${error.message}</div>
                `;
            }
        }
        
        // 页面错误处理
        window.addEventListener('error', function(event) {
            updateDebugInfo('JavaScript错误: ' + event.error.message);
            console.error('JavaScript错误:', event.error);
        });
        
        // 未处理的Promise错误
        window.addEventListener('unhandledrejection', function(event) {
            updateDebugInfo('未处理的Promise错误: ' + event.reason);
            console.error('未处理的Promise错误:', event.reason);
        });
    </script>
</body>
</html>
