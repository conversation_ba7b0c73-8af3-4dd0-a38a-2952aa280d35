package com.example.elementarylearningcompanion.controller;

import com.example.elementarylearningcompanion.dto.ApiResponse;
import com.example.elementarylearningcompanion.model.MathExercise;
import com.example.elementarylearningcompanion.model.WrongQuestion;
import com.example.elementarylearningcompanion.service.MathService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/math")
public class MathController {

    @Autowired
    private MathService mathService;

    @GetMapping("/exercises")
    public ResponseEntity<List<MathExercise>> getExercises(
            @RequestParam Integer textbookVersionId,
            @RequestParam Integer grade,
            @RequestParam String exerciseType) {
        List<MathExercise> exercises = mathService.getExercisesByGradeAndType(textbookVersionId, grade, exerciseType);
        return ResponseEntity.ok(exercises);
    }

    @GetMapping("/exercises/random")
    public ResponseEntity<List<MathExercise>> getRandomExercises(
            @RequestParam Integer textbookVersionId,
            @RequestParam Integer grade,
            @RequestParam String exerciseType,
            @RequestParam(defaultValue = "1") Integer difficultyLevel,
            @RequestParam(defaultValue = "10") Integer count) {
        List<MathExercise> exercises = mathService.getRandomExercises(textbookVersionId, grade, exerciseType, difficultyLevel, count);
        return ResponseEntity.ok(exercises);
    }

    @GetMapping("/exercises/generate")
    public ResponseEntity<List<MathExercise>> generateExercises(
            @RequestParam String exerciseType,
            @RequestParam(defaultValue = "1") Integer difficultyLevel,
            @RequestParam(defaultValue = "10") Integer count) {
        List<MathExercise> exercises = mathService.generateArithmeticExercises(exerciseType, difficultyLevel, count);
        return ResponseEntity.ok(exercises);
    }

    @GetMapping("/exercise-types")
    public ResponseEntity<List<String>> getExerciseTypes(
            @RequestParam Integer textbookVersionId,
            @RequestParam Integer grade) {
        List<String> types = mathService.getAvailableExerciseTypes(textbookVersionId, grade);
        return ResponseEntity.ok(types);
    }

    @PostMapping("/submit-answer")
    public ResponseEntity<ApiResponse> submitAnswer(@RequestBody Map<String, Object> request) {
        try {
            Long studentId = Long.valueOf(request.get("studentId").toString());
            Long exerciseId = Long.valueOf(request.get("exerciseId").toString());
            String answer = request.get("answer").toString();
            Integer timeSpent = request.get("timeSpent") != null ? 
                Integer.valueOf(request.get("timeSpent").toString()) : 0;

            boolean isCorrect = mathService.submitAnswer(studentId, exerciseId, answer, timeSpent);
            
            String message = isCorrect ? "答案正确！" : "答案错误，请再试一次。";
            return ResponseEntity.ok(new ApiResponse(isCorrect, message));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "提交答案失败：" + e.getMessage()));
        }
    }

    @GetMapping("/wrong-questions/{studentId}")
    public ResponseEntity<List<WrongQuestion>> getWrongQuestions(@PathVariable Long studentId) {
        List<WrongQuestion> wrongQuestions = mathService.getWrongQuestions(studentId);
        return ResponseEntity.ok(wrongQuestions);
    }

    @GetMapping("/practice-types")
    public ResponseEntity<List<Map<String, Object>>> getPracticeTypes() {
        List<Map<String, Object>> practiceTypes = List.of(
            Map.of("type", "addition", "name", "加法练习", "description", "练习加法运算"),
            Map.of("type", "subtraction", "name", "减法练习", "description", "练习减法运算"),
            Map.of("type", "multiplication", "name", "乘法练习", "description", "练习乘法运算"),
            Map.of("type", "division", "name", "除法练习", "description", "练习除法运算")
        );
        return ResponseEntity.ok(practiceTypes);
    }
}
