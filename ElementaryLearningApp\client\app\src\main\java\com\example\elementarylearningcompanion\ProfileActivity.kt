package com.example.elementarylearningcompanion

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.elementarylearningcompanion.ui.theme.ElementaryLearningCompanionTheme
import com.example.elementarylearningcompanion.viewmodel.ProfileViewModel

class ProfileActivity : ComponentActivity() {
    private val viewModel: ProfileViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ElementaryLearningCompanionTheme {
                Surface(modifier = Modifier.fillMaxSize(), color = MaterialTheme.colorScheme.background) {
                    ProfileScreen(viewModel)
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileScreen(viewModel: ProfileViewModel) {
    val studentProfile by viewModel.studentProfile.observeAsState()
    val statistics by viewModel.statistics.observeAsState(emptyMap())
    val settings by viewModel.settings.observeAsState(emptyMap())
    val availableGrades by viewModel.availableGrades.observeAsState(emptyMap())
    val isLoading by viewModel.isLoading.observeAsState(false)
    val errorMessage by viewModel.errorMessage.observeAsState()
    val updateResult by viewModel.updateResult.observeAsState()

    var showEditDialog by remember { mutableStateOf(false) }
    var showSettingsDialog by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        viewModel.loadStudentProfile(1L) // Using dummy student ID
        viewModel.loadStatistics(1L)
        viewModel.loadSettings(1L)
        viewModel.loadAvailableGrades()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 顶部返回按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            TextButton(
                onClick = { (context as? ComponentActivity)?.finish() }
            ) {
                Text("← 返回主页")
            }
        }

        Text(
            text = "个人中心",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        when {
            isLoading -> {
                Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                    CircularProgressIndicator()
                }
            }
            
            else -> {
                LazyColumn {
                    item {
                        // Profile Card
                        studentProfile?.let { student ->
                            ProfileCard(
                                student = student,
                                onEditClick = { showEditDialog = true }
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                        }
                    }

                    item {
                        // Statistics Card
                        if (statistics.isNotEmpty()) {
                            StatisticsCard(statistics)
                            Spacer(modifier = Modifier.height(16.dp))
                        }
                    }

                    item {
                        // Settings Card
                        SettingsCard(
                            onSettingsClick = { showSettingsDialog = true }
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                    }

                    item {
                        // Quick Actions
                        QuickActionsCard()
                    }
                }
            }
        }

        // Edit Profile Dialog
        if (showEditDialog && studentProfile != null) {
            EditProfileDialog(
                student = studentProfile!!,
                availableGrades = availableGrades,
                onDismiss = { showEditDialog = false },
                onSave = { name, grade, textbookVersionId ->
                    viewModel.updateStudentProfile(1L, name, grade, textbookVersionId)
                    showEditDialog = false
                }
            )
        }

        // Settings Dialog
        if (showSettingsDialog) {
            SettingsDialog(
                settings = settings,
                onDismiss = { showSettingsDialog = false },
                onSave = { newSettings ->
                    viewModel.updateSettings(1L, newSettings)
                    showSettingsDialog = false
                }
            )
        }

        errorMessage?.let { message ->
            LaunchedEffect(message) {
                // Show error message
            }
        }

        updateResult?.let { result ->
            LaunchedEffect(result) {
                // Show update result
                viewModel.clearUpdateResult()
            }
        }
    }
}

@Composable
fun ProfileCard(
    student: com.example.elementarylearningcompanion.dto.Student,
    onEditClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Person,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                    Column {
                        Text(
                            text = student.name,
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "${student.grade}年级",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                IconButton(onClick = onEditClick) {
                    Icon(Icons.Default.Edit, contentDescription = "编辑")
                }
            }
        }
    }
}

@Composable
fun StatisticsCard(statistics: Map<String, Any>) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "学习统计",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticItem(
                    "本周学习",
                    "${(statistics["weeklyStudyTime"] as? Double)?.toInt() ?: 0}分钟"
                )
                StatisticItem(
                    "数学正确率",
                    "${statistics["mathAccuracy"] ?: 0}%"
                )
                StatisticItem(
                    "英语正确率",
                    "${statistics["englishAccuracy"] ?: 0}%"
                )
            }
        }
    }
}

@Composable
fun StatisticItem(label: String, value: String) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
fun SettingsCard(onSettingsClick: () -> Unit) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = onSettingsClick
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                Icons.Default.Settings,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.width(16.dp))
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "应用设置",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "个性化设置和偏好",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
fun QuickActionsCard() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "快捷操作",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(16.dp))
            
            val actions = listOf(
                "查看错题本" to "复习错误题目",
                "学习报告" to "查看详细学习报告",
                "成就徽章" to "查看获得的成就",
                "帮助中心" to "获取使用帮助"
            )
            
            actions.forEach { (title, description) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = title,
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = description,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                if (actions.last() != (title to description)) {
                    Divider()
                }
            }
        }
    }
}

@Composable
fun EditProfileDialog(
    student: com.example.elementarylearningcompanion.dto.Student,
    availableGrades: Map<String, Any>,
    onDismiss: () -> Unit,
    onSave: (String, Int, Int?) -> Unit
) {
    var name by remember { mutableStateOf(student.name) }
    var selectedGrade by remember { mutableStateOf(student.grade) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("编辑个人信息") },
        text = {
            Column {
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("姓名") },
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(16.dp))
                
                // Grade selection would go here
                Text("年级：${selectedGrade}年级")
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onSave(name, selectedGrade, student.textbookVersionId)
                }
            ) {
                Text("保存")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

@Composable
fun SettingsDialog(
    settings: Map<String, Any>,
    onDismiss: () -> Unit,
    onSave: (Map<String, Any>) -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("应用设置") },
        text = {
            Column {
                Text("设置功能开发中...")
                // Settings UI would go here
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onSave(settings)
                }
            ) {
                Text("保存")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}
