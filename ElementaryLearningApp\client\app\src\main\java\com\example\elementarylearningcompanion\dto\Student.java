package com.example.elementarylearningcompanion.dto;

public class Student {
    private Long id;
    private Long parentId;
    private String name;
    private Integer grade;
    private Integer textbookVersionId;
    private String createdAt;

    // Constructors
    public Student() {}

    public Student(String name, Integer grade) {
        this.name = name;
        this.grade = grade;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getGrade() {
        return grade;
    }

    public void setGrade(Integer grade) {
        this.grade = grade;
    }

    public Integer getTextbookVersionId() {
        return textbookVersionId;
    }

    public void setTextbookVersionId(Integer textbookVersionId) {
        this.textbookVersionId = textbookVersionId;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }
}
