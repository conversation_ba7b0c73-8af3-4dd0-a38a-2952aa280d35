package com.example.elementarylearningcompanion

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.elementarylearningcompanion.ui.theme.ElementaryLearningCompanionTheme
import com.example.elementarylearningcompanion.viewmodel.EnglishViewModel

class EnglishActivity : ComponentActivity() {
    private val viewModel: EnglishViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ElementaryLearningCompanionTheme {
                Surface(modifier = Modifier.fillMaxSize(), color = MaterialTheme.colorScheme.background) {
                    EnglishScreen(viewModel)
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EnglishScreen(viewModel: EnglishViewModel) {
    val learningModes by viewModel.learningModes.observeAsState(emptyList())
    val words by viewModel.words.observeAsState(emptyList())
    val isLoading by viewModel.isLoading.observeAsState(false)
    val errorMessage by viewModel.errorMessage.observeAsState()
    val recordResult by viewModel.recordResult.observeAsState()

    var selectedMode by remember { mutableStateOf<Map<String, Any>?>(null) }
    var currentWordIndex by remember { mutableStateOf(0) }
    var userInput by remember { mutableStateOf("") }
    var showAnswer by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        viewModel.loadLearningModes()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 顶部返回按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            TextButton(
                onClick = { (context as? ComponentActivity)?.finish() }
            ) {
                Text("← 返回主页")
            }
        }

        Text(
            text = "英语学习",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        when {
            isLoading -> {
                Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                    CircularProgressIndicator()
                }
            }
            
            selectedMode == null -> {
                LearningModeSelection(learningModes) { mode ->
                    selectedMode = mode
                    when (mode["mode"] as String) {
                        "flashcard" -> viewModel.loadRandomWords(1, 1, 10, null) // Using dummy values
                        "spell" -> viewModel.loadSpellingTest(1, 1, 10)
                        "listen" -> viewModel.loadRandomWords(1, 1, 10, null)
                        "quiz" -> viewModel.loadRandomWords(1, 1, 10, null)
                    }
                }
            }
            
            words.isNotEmpty() -> {
                WordLearningScreen(
                    mode = selectedMode!!["mode"] as String,
                    words = words,
                    currentIndex = currentWordIndex,
                    userInput = userInput,
                    onInputChange = { userInput = it },
                    showAnswer = showAnswer,
                    onShowAnswer = { showAnswer = true },
                    onNext = {
                        if (currentWordIndex < words.size - 1) {
                            currentWordIndex++
                            userInput = ""
                            showAnswer = false
                            viewModel.clearRecordResult()
                        }
                    },
                    onBack = {
                        selectedMode = null
                        currentWordIndex = 0
                        userInput = ""
                        showAnswer = false
                        viewModel.clearRecordResult()
                    },
                    onSubmitSpelling = { answer ->
                        val word = words[currentWordIndex]
                        val isCorrect = word.getWord().equals(answer, ignoreCase = true)
                        word.getId()?.let { wordId ->
                            viewModel.recordLearning(1L, wordId, "spell", isCorrect) // Using dummy student ID
                        }
                        showAnswer = true
                    },
                    recordResult = recordResult
                )
            }
        }

        errorMessage?.let { message ->
            LaunchedEffect(message) {
                // Show error message
            }
        }
    }
}

@Composable
fun LearningModeSelection(
    learningModes: List<Map<String, Any>>,
    onModeSelected: (Map<String, Any>) -> Unit
) {
    LazyColumn {
        items(learningModes) { mode ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                onClick = { onModeSelected(mode) }
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = mode["name"] as String,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = mode["description"] as String,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Composable
fun WordLearningScreen(
    mode: String,
    words: List<com.example.elementarylearningcompanion.dto.EnglishWord>,
    currentIndex: Int,
    userInput: String,
    onInputChange: (String) -> Unit,
    showAnswer: Boolean,
    onShowAnswer: () -> Unit,
    onNext: () -> Unit,
    onBack: () -> Unit,
    onSubmitSpelling: (String) -> Unit,
    recordResult: com.example.elementarylearningcompanion.dto.ApiResponse?
) {
    val currentWord = words[currentIndex]

    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            TextButton(onClick = onBack) {
                Text("返回")
            }
            Text(
                text = "${currentIndex + 1} / ${words.size}",
                style = MaterialTheme.typography.bodyMedium
            )
        }

        Spacer(modifier = Modifier.height(32.dp))

        when (mode) {
            "flashcard" -> FlashcardMode(currentWord, showAnswer, onShowAnswer)
            "spell" -> SpellingMode(currentWord, userInput, onInputChange, showAnswer, onSubmitSpelling, recordResult)
            "listen" -> ListeningMode(currentWord, showAnswer, onShowAnswer)
            "quiz" -> QuizMode(currentWord, showAnswer, onShowAnswer)
        }

        Spacer(modifier = Modifier.height(24.dp))

        if (showAnswer) {
            if (currentIndex < words.size - 1) {
                Button(
                    onClick = onNext,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("下一个单词")
                }
            } else {
                Button(
                    onClick = onBack,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("完成学习")
                }
            }
        }
    }
}

@Composable
fun FlashcardMode(
    word: com.example.elementarylearningcompanion.dto.EnglishWord,
    showAnswer: Boolean,
    onShowAnswer: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = word.getWord(),
                style = MaterialTheme.typography.headlineLarge,
                fontWeight = FontWeight.Bold
            )

            word.getPhonetic()?.let { phonetic ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "[$phonetic]",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.primary
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            if (!showAnswer) {
                Button(onClick = onShowAnswer) {
                    Text("显示释义")
                }
            } else {
                Text(
                    text = word.getMeaningChinese(),
                    style = MaterialTheme.typography.titleLarge
                )

                word.getExampleSentence()?.let { example ->
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "例句：$example",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                word.getExampleTranslation()?.let { translation ->
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "翻译：$translation",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Composable
fun SpellingMode(
    word: com.example.elementarylearningcompanion.dto.EnglishWord,
    userInput: String,
    onInputChange: (String) -> Unit,
    showAnswer: Boolean,
    onSubmitSpelling: (String) -> Unit,
    recordResult: com.example.elementarylearningcompanion.dto.ApiResponse?
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "请拼写这个单词：",
                style = MaterialTheme.typography.titleMedium
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = word.getMeaningChinese(),
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(24.dp))

            OutlinedTextField(
                value = userInput,
                onValueChange = onInputChange,
                label = { Text("输入单词拼写") },
                modifier = Modifier.fillMaxWidth(),
                enabled = !showAnswer
            )

            Spacer(modifier = Modifier.height(16.dp))

            if (!showAnswer) {
                Button(
                    onClick = { onSubmitSpelling(userInput) },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = userInput.isNotBlank()
                ) {
                    Text("提交")
                }
            } else {
                recordResult?.let { result ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = if (result.isSuccess())
                                MaterialTheme.colorScheme.primaryContainer
                            else
                                MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = if (result.isSuccess()) "✓ 正确！" else "✗ 错误",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )
                            if (!result.isSuccess()) {
                                Text(
                                    text = "正确拼写：${word.getWord()}",
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ListeningMode(
    word: com.example.elementarylearningcompanion.dto.EnglishWord,
    showAnswer: Boolean,
    onShowAnswer: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "听力练习",
                style = MaterialTheme.typography.titleMedium
            )

            Spacer(modifier = Modifier.height(16.dp))

            Button(onClick = { /* Play audio */ }) {
                Text("🔊 播放发音")
            }

            Spacer(modifier = Modifier.height(16.dp))

            if (!showAnswer) {
                Button(onClick = onShowAnswer) {
                    Text("显示单词")
                }
            } else {
                Text(
                    text = word.getWord(),
                    style = MaterialTheme.typography.headlineLarge,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = word.getMeaningChinese(),
                    style = MaterialTheme.typography.titleMedium
                )
            }
        }
    }
}

@Composable
fun QuizMode(
    word: com.example.elementarylearningcompanion.dto.EnglishWord,
    showAnswer: Boolean,
    onShowAnswer: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = word.getWord(),
                style = MaterialTheme.typography.headlineLarge,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "选择正确的中文释义：",
                style = MaterialTheme.typography.titleMedium
            )

            Spacer(modifier = Modifier.height(16.dp))

            // TODO: Implement multiple choice options
            if (!showAnswer) {
                Button(onClick = onShowAnswer) {
                    Text("显示答案")
                }
            } else {
                Text(
                    text = word.getMeaningChinese(),
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}
