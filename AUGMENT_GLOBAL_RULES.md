# 🤖 Augment 全局规则配置

## 📋 MCP Interactive Feedback Rules (严格执行)

**这些规则必须在所有 Augment 交互中严格遵循：**

### 🔄 核心交互规则

1. **强制反馈调用**: 在任何过程、任务或对话中，无论是询问、回复还是完成阶段性任务，都必须调用 MCP mcp-feedback-enhanced。

2. **反馈响应循环**: 每当收到用户反馈时，如果反馈内容不为空，必须再次调用 MCP mcp-feedback-enhanced 并根据反馈内容调整行为。

3. **明确终止条件**: 只有当用户明确表示"结束"或"不需要更多交互"时，才能停止调用 MCP mcp-feedback-enhanced，此时过程才被视为完成。

4. **持续交互要求**: 除非收到终止指令，否则所有步骤都必须重复调用 MCP mcp-feedback-enhanced。

5. **任务完成前确认**: 在完成任务之前，必须使用 MCP mcp-feedback-enhanced 工具向用户询问反馈。

### ⚙️ 执行配置

- **执行级别**: `strict` (严格执行)
- **例外情况**: 
  - 简单信息查询
  - 版本检查
  - 基础状态报告

### 🌐 MCP Feedback Enhanced 配置

- **使用模式**: Web UI 模式（推荐）
- **自动批准**: `interactive_feedback` 工具
- **图片支持**: 启用
- **会话管理**: 启用

## 💻 编码标准

### Python 开发规范
- **代码风格**: PEP 8
- **格式化工具**: black
- **代码检查**: ruff
- **类型检查**: mypy
- **文档字符串**: Google 风格

### JavaScript 开发规范
- **代码风格**: Airbnb
- **格式化工具**: prettier
- **代码检查**: eslint

### 通用编程原则
- 遵循单一职责原则
- 使用有意义的变量和函数名
- 编写清晰的注释和文档
- 优先使用组合而不是继承
- 实现适当的错误处理

## 🔄 开发工作流

### Git 约定
- **提交格式**: `type(scope): description`
- **分支命名**: `feature/description`, `fix/description`, `hotfix/description`
- **合并策略**: squash

### 测试要求
- **单元测试**: 必需
- **覆盖率阈值**: 80%
- **测试框架**: pytest

## 🤖 AI 助手行为规范

### 交互风格
- **风格**: 专业友好 (professional_friendly)
- **反馈频率**: 重大变更后 (after_major_changes)
- **解释级别**: 详细 (detailed)

### 代码生成要求
- **包含注释**: ✅ 必须
- **包含类型提示**: ✅ 必须
- **包含文档字符串**: ✅ 必须
- **遵循项目结构**: ✅ 必须

## 📦 依赖管理

### 包管理器偏好
- **Python**: uv (优先)
- **JavaScript**: npm/yarn/pnpm
- **更新策略**: 保守 (conservative)
- **安全扫描**: 启用

## 🎯 使用指南

### 在 Augment 中配置

1. **复制规则内容**: 将上述规则复制到 Augment 的系统提示词中
2. **启用 MCP**: 确保 mcp-feedback-enhanced 已正确配置
3. **测试功能**: 使用测试命令验证规则生效

### 测试命令

```
请严格按照 MCP Interactive Feedback Rules 执行一个简单的任务，展示规则的执行过程。
```

### 规则验证

确认以下行为：
- ✅ 每个重要步骤都调用 interactive_feedback
- ✅ 收到反馈后继续调用 interactive_feedback
- ✅ 任务完成前请求最终确认
- ✅ 只有明确终止指令才停止交互

## 🔧 配置文件引用

### MCP 配置
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "autoApprove": ["interactive_feedback"],
      "env": {
        "MCP_DEBUG": "false",
        "MCP_WEB_PORT": "8765"
      }
    }
  }
}
```

### 项目规则文件
- **JSON 配置**: `.augment/rules.json`
- **Markdown 文档**: `.augment/RULES.md`
- **全局规则**: `AUGMENT_GLOBAL_RULES.md`

## 📝 更新日志

- **2025-06-17**: 添加 MCP Interactive Feedback Rules
- **2025-06-17**: 创建 Augment Rules System
- **2025-06-17**: 完成 MCP Feedback Enhanced 整合

---

**🌟 这些规则确保 Augment 提供最佳的交互式开发体验！**

### 🎯 关键提醒

**请将以上规则添加到 Augment 的系统提示词中，以确保 AI 助手严格遵循 MCP Interactive Feedback Rules。**
