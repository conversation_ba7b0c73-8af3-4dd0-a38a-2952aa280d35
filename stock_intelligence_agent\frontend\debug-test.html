<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
        }
        .status.online {
            background: #d4edda;
            color: #155724;
        }
        .status.offline {
            background: #f8d7da;
            color: #721c24;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .debug {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 调试测试页面</h1>
        
        <div id="status" class="status offline">
            🔴 检查服务状态中...
        </div>
        
        <div class="debug">
            <h4>调试信息</h4>
            <div id="debugInfo">页面加载中...</div>
        </div>
        
        <div>
            <h3>基础测试</h3>
            <button onclick="testAlert()">测试弹窗</button>
            <button onclick="testConsole()">测试控制台</button>
            <button onclick="testHealth()">健康检查</button>
            <button onclick="testStocks()">股票列表</button>
        </div>
        
        <div>
            <h3>股票详情测试</h3>
            <input type="text" id="stockCode" value="300750" placeholder="输入股票代码">
            <button onclick="testStockDetail()">查询股票详情</button>
        </div>
        
        <div id="result" class="result">
            <p>点击上方按钮开始测试...</p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        // 调试信息
        function updateDebugInfo(message) {
            const debugDiv = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.innerHTML += `<br>[${timestamp}] ${message}`;
            console.log(`[${timestamp}] ${message}`);
        }
        
        // 页面加载时检查
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo('页面DOM加载完成');
            updateDebugInfo('API地址: ' + API_BASE);
            checkServerStatus();
        });
        
        // 测试弹窗
        function testAlert() {
            alert('JavaScript正常工作！');
            updateDebugInfo('弹窗测试成功');
        }
        
        // 测试控制台
        function testConsole() {
            console.log('控制台测试消息');
            updateDebugInfo('控制台测试完成，请查看浏览器开发者工具');
        }
        
        // 检查服务器状态
        async function checkServerStatus() {
            const statusDiv = document.getElementById('status');
            updateDebugInfo('开始检查服务状态...');
            
            try {
                updateDebugInfo('发送请求到: ' + API_BASE + '/health');
                const response = await fetch(`${API_BASE}/health`);
                updateDebugInfo('收到响应，状态码: ' + response.status);
                
                const data = await response.json();
                updateDebugInfo('响应数据: ' + JSON.stringify(data));
                
                if (data.status === 'healthy') {
                    statusDiv.className = 'status online';
                    statusDiv.innerHTML = '🟢 服务在线';
                    updateDebugInfo('服务状态正常');
                } else {
                    throw new Error('Service unhealthy');
                }
            } catch (error) {
                updateDebugInfo('服务状态检查失败: ' + error.message);
                statusDiv.className = 'status offline';
                statusDiv.innerHTML = '🔴 服务离线 - ' + error.message;
            }
        }
        
        // 显示结果
        function showResult(title, data, error = null) {
            const resultDiv = document.getElementById('result');
            
            if (error) {
                resultDiv.innerHTML = `
                    <h4>❌ ${title} - 失败</h4>
                    <p style="color: red;">错误: ${error.message}</p>
                `;
                updateDebugInfo(`${title}失败: ${error.message}`);
            } else {
                resultDiv.innerHTML = `
                    <h4>✅ ${title} - 成功</h4>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                updateDebugInfo(`${title}成功`);
            }
        }
        
        // 测试健康检查
        async function testHealth() {
            updateDebugInfo('开始测试健康检查...');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                showResult('健康检查', data);
            } catch (error) {
                showResult('健康检查', null, error);
            }
        }
        
        // 测试股票列表
        async function testStocks() {
            updateDebugInfo('开始测试股票列表...');
            try {
                const url = `${API_BASE}/api/v1/stocks?limit=5`;
                updateDebugInfo('请求URL: ' + url);
                const response = await fetch(url);
                const data = await response.json();
                showResult('股票列表', data);
            } catch (error) {
                showResult('股票列表', null, error);
            }
        }
        
        // 测试股票详情
        async function testStockDetail() {
            const stockCode = document.getElementById('stockCode').value;
            if (!stockCode) {
                alert('请输入股票代码');
                return;
            }
            
            updateDebugInfo(`开始测试股票详情: ${stockCode}`);
            try {
                const url = `${API_BASE}/api/v1/stocks/${stockCode}`;
                updateDebugInfo('请求URL: ' + url);
                const response = await fetch(url);
                const data = await response.json();
                showResult(`股票详情 (${stockCode})`, data);
            } catch (error) {
                showResult(`股票详情 (${stockCode})`, null, error);
            }
        }
        
        // 页面错误处理
        window.addEventListener('error', function(event) {
            updateDebugInfo('JavaScript错误: ' + event.error.message);
            console.error('JavaScript错误:', event.error);
        });
        
        // 未处理的Promise错误
        window.addEventListener('unhandledrejection', function(event) {
            updateDebugInfo('未处理的Promise错误: ' + event.reason);
            console.error('未处理的Promise错误:', event.reason);
        });
    </script>
</body>
</html>
