@echo off
echo ==========================================
echo Elementary Learning App Project Test
echo 小学学习伴侣项目测试
echo ==========================================

echo.
echo 1. 检查项目结构...
echo ==========================================

echo 检查服务器目录...
if exist "server\src\main\java" (
    echo ✓ 服务器Java源码目录存在
) else (
    echo ✗ 服务器Java源码目录不存在
)

if exist "server\database_schema.sql" (
    echo ✓ 数据库结构文件存在
) else (
    echo ✗ 数据库结构文件不存在
)

if exist "server\sample_data.sql" (
    echo ✓ 示例数据文件存在
) else (
    echo ✗ 示例数据文件不存在
)

echo.
echo 检查客户端目录...
if exist "client\app\src\main\java" (
    echo ✓ 客户端Java源码目录存在
) else (
    echo ✗ 客户端Java源码目录不存在
)

if exist "client\app\src\main\AndroidManifest.xml" (
    echo ✓ Android清单文件存在
) else (
    echo ✗ Android清单文件不存在
)

if exist "client\gradlew.bat" (
    echo ✓ Gradle Wrapper存在
) else (
    echo ✗ Gradle Wrapper不存在
)

echo.
echo 2. 检查核心文件...
echo ==========================================

echo 检查服务器核心类...
set SERVER_CLASSES=0
for %%f in (server\src\main\java\com\example\elementarylearningcompanion\*.java) do (
    set /a SERVER_CLASSES+=1
)
for %%f in (server\src\main\java\com\example\elementarylearningcompanion\controller\*.java) do (
    set /a SERVER_CLASSES+=1
)
for %%f in (server\src\main\java\com\example\elementarylearningcompanion\service\*.java) do (
    set /a SERVER_CLASSES+=1
)
for %%f in (server\src\main\java\com\example\elementarylearningcompanion\model\*.java) do (
    set /a SERVER_CLASSES+=1
)
echo 服务器Java类文件数量: %SERVER_CLASSES%

echo.
echo 检查客户端核心文件...
set CLIENT_ACTIVITIES=0
for %%f in (client\app\src\main\java\com\example\elementarylearningcompanion\*Activity.kt) do (
    set /a CLIENT_ACTIVITIES+=1
)
echo 客户端Activity数量: %CLIENT_ACTIVITIES%

echo.
echo 3. 检查环境依赖...
echo ==========================================

echo 检查Java...
java -version >nul 2>&1
if errorlevel 1 (
    echo ✗ Java未安装或不在PATH中
) else (
    echo ✓ Java已安装
    java -version 2>&1 | findstr "version"
)

echo.
echo 检查Android SDK...
if exist "%ANDROID_HOME%" (
    echo ✓ ANDROID_HOME已设置: %ANDROID_HOME%
) else if exist "F:\Android" (
    echo ✓ Android SDK在默认位置: F:\Android
) else (
    echo ✗ Android SDK未找到
)

echo.
echo 4. 功能模块检查...
echo ==========================================

echo 检查数学模块...
if exist "server\src\main\java\com\example\elementarylearningcompanion\controller\MathController.java" (
    echo ✓ 数学控制器存在
) else (
    echo ✗ 数学控制器不存在
)

if exist "client\app\src\main\java\com\example\elementarylearningcompanion\MathActivity.kt" (
    echo ✓ 数学Activity存在
) else (
    echo ✗ 数学Activity不存在
)

echo.
echo 检查英语模块...
if exist "server\src\main\java\com\example\elementarylearningcompanion\controller\EnglishController.java" (
    echo ✓ 英语控制器存在
) else (
    echo ✗ 英语控制器不存在
)

if exist "client\app\src\main\java\com\example\elementarylearningcompanion\EnglishActivity.kt" (
    echo ✓ 英语Activity存在
) else (
    echo ✗ 英语Activity不存在
)

echo.
echo 检查错题本模块...
if exist "server\src\main\java\com\example\elementarylearningcompanion\controller\WrongQuestionController.java" (
    echo ✓ 错题本控制器存在
) else (
    echo ✗ 错题本控制器不存在
)

if exist "client\app\src\main\java\com\example\elementarylearningcompanion\WrongQuestionActivity.kt" (
    echo ✓ 错题本Activity存在
) else (
    echo ✗ 错题本Activity不存在
)

echo.
echo 5. 数据库文件检查...
echo ==========================================

echo 检查数据库结构...
findstr /c:"CREATE TABLE" server\database_schema.sql >nul 2>&1
if errorlevel 1 (
    echo ✗ 数据库结构文件无效
) else (
    for /f %%i in ('findstr /c:"CREATE TABLE" server\database_schema.sql ^| find /c /v ""') do echo ✓ 数据库表数量: %%i
)

echo.
echo 检查示例数据...
findstr /c:"INSERT INTO" server\sample_data.sql >nul 2>&1
if errorlevel 1 (
    echo ✗ 示例数据文件无效
) else (
    for /f %%i in ('findstr /c:"INSERT INTO" server\sample_data.sql ^| find /c /v ""') do echo ✓ 示例数据插入语句数量: %%i
)

echo.
echo ==========================================
echo 测试完成！
echo ==========================================

echo.
echo 项目状态总结:
echo - 这是一个完整的小学学习伴侣应用项目
echo - 包含Spring Boot后端和Android客户端
echo - 实现了语文、数学、英语学习模块
echo - 包含错题本、个人中心等功能
echo - 提供了完整的数据库设计和示例数据

echo.
echo 下一步建议:
echo 1. 确保MySQL数据库已安装并运行
echo 2. 导入数据库结构: mysql -u root -p elementary_learning ^< server\database_schema.sql
echo 3. 导入示例数据: mysql -u root -p elementary_learning ^< server\sample_data.sql
echo 4. 配置服务器数据库连接
echo 5. 启动服务器进行测试

pause
