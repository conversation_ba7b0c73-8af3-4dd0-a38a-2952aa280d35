"""
股票相关的 Pydantic 模型
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional, List
from pydantic import BaseModel, Field, validator
from uuid import UUID


class StockBase(BaseModel):
    """股票基础模型"""
    code: str = Field(..., description="股票代码", example="000001")
    name: str = Field(..., description="股票名称", example="平安银行")
    market: str = Field(..., description="市场", example="SZ")
    industry: Optional[str] = Field(None, description="所属行业", example="银行")
    sector: Optional[str] = Field(None, description="所属板块", example="金融")


class StockCreate(StockBase):
    """创建股票模型"""
    list_date: Optional[date] = Field(None, description="上市日期")
    total_share: Optional[Decimal] = Field(None, description="总股本(万股)")
    float_share: Optional[Decimal] = Field(None, description="流通股本(万股)")


class StockUpdate(BaseModel):
    """更新股票模型"""
    name: Optional[str] = Field(None, description="股票名称")
    industry: Optional[str] = Field(None, description="所属行业")
    sector: Optional[str] = Field(None, description="所属板块")
    total_share: Optional[Decimal] = Field(None, description="总股本(万股)")
    float_share: Optional[Decimal] = Field(None, description="流通股本(万股)")
    is_active: Optional[bool] = Field(None, description="是否活跃")


class StockResponse(StockBase):
    """股票响应模型"""
    id: UUID = Field(..., description="股票ID")
    list_date: Optional[date] = Field(None, description="上市日期")
    delist_date: Optional[date] = Field(None, description="退市日期")
    is_active: bool = Field(..., description="是否活跃")
    total_share: Optional[Decimal] = Field(None, description="总股本(万股)")
    float_share: Optional[Decimal] = Field(None, description="流通股本(万股)")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class StockListResponse(BaseModel):
    """股票列表响应模型"""
    stocks: List[StockResponse] = Field(..., description="股票列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")


class StockQuoteBase(BaseModel):
    """股票行情基础模型"""
    trade_date: date = Field(..., description="交易日期")
    open_price: Optional[Decimal] = Field(None, description="开盘价")
    high_price: Optional[Decimal] = Field(None, description="最高价")
    low_price: Optional[Decimal] = Field(None, description="最低价")
    close_price: Optional[Decimal] = Field(None, description="收盘价")
    pre_close: Optional[Decimal] = Field(None, description="昨收价")
    volume: Optional[Decimal] = Field(None, description="成交量(股)")
    amount: Optional[Decimal] = Field(None, description="成交额(元)")
    turnover_rate: Optional[Decimal] = Field(None, description="换手率(%)")
    change: Optional[Decimal] = Field(None, description="涨跌额")
    pct_change: Optional[Decimal] = Field(None, description="涨跌幅(%)")


class StockQuoteCreate(StockQuoteBase):
    """创建股票行情模型"""
    stock_id: UUID = Field(..., description="股票ID")


class StockQuoteResponse(StockQuoteBase):
    """股票行情响应模型"""
    id: UUID = Field(..., description="行情ID")
    stock_id: UUID = Field(..., description="股票ID")
    pe_ratio: Optional[Decimal] = Field(None, description="市盈率")
    pb_ratio: Optional[Decimal] = Field(None, description="市净率")
    ps_ratio: Optional[Decimal] = Field(None, description="市销率")
    market_cap: Optional[Decimal] = Field(None, description="总市值(元)")
    float_cap: Optional[Decimal] = Field(None, description="流通市值(元)")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class RealtimeQuoteBase(BaseModel):
    """实时行情基础模型"""
    current_price: Optional[Decimal] = Field(None, description="当前价")
    open_price: Optional[Decimal] = Field(None, description="开盘价")
    high_price: Optional[Decimal] = Field(None, description="最高价")
    low_price: Optional[Decimal] = Field(None, description="最低价")
    pre_close: Optional[Decimal] = Field(None, description="昨收价")
    volume: Optional[Decimal] = Field(None, description="成交量")
    amount: Optional[Decimal] = Field(None, description="成交额")
    change: Optional[Decimal] = Field(None, description="涨跌额")
    pct_change: Optional[Decimal] = Field(None, description="涨跌幅")


class RealtimeQuoteCreate(RealtimeQuoteBase):
    """创建实时行情模型"""
    stock_id: UUID = Field(..., description="股票ID")
    bid1_price: Optional[Decimal] = Field(None, description="买一价")
    bid1_volume: Optional[Decimal] = Field(None, description="买一量")
    ask1_price: Optional[Decimal] = Field(None, description="卖一价")
    ask1_volume: Optional[Decimal] = Field(None, description="卖一量")


class RealtimeQuoteResponse(RealtimeQuoteBase):
    """实时行情响应模型"""
    id: UUID = Field(..., description="行情ID")
    stock_id: UUID = Field(..., description="股票ID")
    bid1_price: Optional[Decimal] = Field(None, description="买一价")
    bid1_volume: Optional[Decimal] = Field(None, description="买一量")
    ask1_price: Optional[Decimal] = Field(None, description="卖一价")
    ask1_volume: Optional[Decimal] = Field(None, description="卖一量")
    timestamp: datetime = Field(..., description="数据时间")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class TechnicalIndicatorBase(BaseModel):
    """技术指标基础模型"""
    trade_date: date = Field(..., description="交易日期")
    ma5: Optional[Decimal] = Field(None, description="5日均线")
    ma10: Optional[Decimal] = Field(None, description="10日均线")
    ma20: Optional[Decimal] = Field(None, description="20日均线")
    ma60: Optional[Decimal] = Field(None, description="60日均线")
    macd_dif: Optional[Decimal] = Field(None, description="MACD DIF")
    macd_dea: Optional[Decimal] = Field(None, description="MACD DEA")
    macd_histogram: Optional[Decimal] = Field(None, description="MACD 柱状图")
    rsi6: Optional[Decimal] = Field(None, description="6日RSI")
    rsi12: Optional[Decimal] = Field(None, description="12日RSI")
    rsi24: Optional[Decimal] = Field(None, description="24日RSI")
    kdj_k: Optional[Decimal] = Field(None, description="KDJ K值")
    kdj_d: Optional[Decimal] = Field(None, description="KDJ D值")
    kdj_j: Optional[Decimal] = Field(None, description="KDJ J值")
    boll_upper: Optional[Decimal] = Field(None, description="布林带上轨")
    boll_mid: Optional[Decimal] = Field(None, description="布林带中轨")
    boll_lower: Optional[Decimal] = Field(None, description="布林带下轨")


class TechnicalIndicatorResponse(TechnicalIndicatorBase):
    """技术指标响应模型"""
    id: UUID = Field(..., description="指标ID")
    stock_id: UUID = Field(..., description="股票ID")
    volume_ma5: Optional[Decimal] = Field(None, description="5日成交量均线")
    volume_ma10: Optional[Decimal] = Field(None, description="10日成交量均线")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class FinancialDataBase(BaseModel):
    """财务数据基础模型"""
    report_date: date = Field(..., description="报告期")
    report_type: str = Field(..., description="报告类型(Q1/Q2/Q3/A)")
    revenue: Optional[Decimal] = Field(None, description="营业收入")
    net_profit: Optional[Decimal] = Field(None, description="净利润")
    gross_profit: Optional[Decimal] = Field(None, description="毛利润")
    operating_profit: Optional[Decimal] = Field(None, description="营业利润")
    revenue_growth: Optional[Decimal] = Field(None, description="营收增长率")
    profit_growth: Optional[Decimal] = Field(None, description="净利润增长率")
    roe: Optional[Decimal] = Field(None, description="净资产收益率")
    roa: Optional[Decimal] = Field(None, description="总资产收益率")
    gross_margin: Optional[Decimal] = Field(None, description="毛利率")
    net_margin: Optional[Decimal] = Field(None, description="净利率")


class FinancialDataResponse(FinancialDataBase):
    """财务数据响应模型"""
    id: UUID = Field(..., description="财务数据ID")
    stock_id: UUID = Field(..., description="股票ID")
    total_assets: Optional[Decimal] = Field(None, description="总资产")
    total_liabilities: Optional[Decimal] = Field(None, description="总负债")
    total_equity: Optional[Decimal] = Field(None, description="股东权益")
    debt_ratio: Optional[Decimal] = Field(None, description="资产负债率")
    current_ratio: Optional[Decimal] = Field(None, description="流动比率")
    inventory_turnover: Optional[Decimal] = Field(None, description="存货周转率")
    receivable_turnover: Optional[Decimal] = Field(None, description="应收账款周转率")
    total_asset_turnover: Optional[Decimal] = Field(None, description="总资产周转率")
    operating_cash_flow: Optional[Decimal] = Field(None, description="经营现金流")
    investing_cash_flow: Optional[Decimal] = Field(None, description="投资现金流")
    financing_cash_flow: Optional[Decimal] = Field(None, description="筹资现金流")
    eps: Optional[Decimal] = Field(None, description="每股收益")
    bps: Optional[Decimal] = Field(None, description="每股净资产")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class StockSearchSuggestion(BaseModel):
    """股票搜索建议模型"""
    code: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    market: str = Field(..., description="市场")
    industry: Optional[str] = Field(None, description="行业")


class StockSearchResponse(BaseModel):
    """股票搜索响应模型"""
    query: str = Field(..., description="搜索关键词")
    suggestions: List[StockSearchSuggestion] = Field(..., description="搜索建议")


class HotStock(BaseModel):
    """热门股票模型"""
    code: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    market: str = Field(..., description="市场")
    industry: Optional[str] = Field(None, description="行业")
    close_price: Optional[Decimal] = Field(None, description="收盘价")
    change: Optional[Decimal] = Field(None, description="涨跌额")
    pct_change: Optional[Decimal] = Field(None, description="涨跌幅")
    volume: Optional[Decimal] = Field(None, description="成交量")
    amount: Optional[Decimal] = Field(None, description="成交额")
    turnover_rate: Optional[Decimal] = Field(None, description="换手率")


class HotStocksResponse(BaseModel):
    """热门股票响应模型"""
    hot_stocks: List[HotStock] = Field(..., description="热门股票列表")
    sort_by: str = Field(..., description="排序字段")
    order: str = Field(..., description="排序方向")
    timestamp: datetime = Field(..., description="数据时间")


class StockStatistics(BaseModel):
    """股票统计模型"""
    total_stocks: int = Field(..., description="总股票数")
    market_distribution: dict = Field(..., description="市场分布")
    top_industries: dict = Field(..., description="热门行业")
    last_updated: datetime = Field(..., description="最后更新时间")


class DataRefreshTask(BaseModel):
    """数据刷新任务模型"""
    message: str = Field(..., description="任务消息")
    task_id: str = Field(..., description="任务ID")
    stock_code: str = Field(..., description="股票代码")


# 验证器
class StockCodeValidator:
    """股票代码验证器"""
    
    @staticmethod
    def validate_stock_code(code: str) -> str:
        """验证股票代码格式"""
        if not code or len(code) != 6 or not code.isdigit():
            raise ValueError("股票代码必须是6位数字")
        return code


# 为相关模型添加验证器
StockBase.validator('code', allow_reuse=True)(StockCodeValidator.validate_stock_code)
StockCreate.validator('code', allow_reuse=True)(StockCodeValidator.validate_stock_code)
