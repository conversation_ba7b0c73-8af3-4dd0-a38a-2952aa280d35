#!/usr/bin/env python3
"""
安装Selenium和ChromeDriver的脚本
"""

import subprocess
import sys
import os
import requests
import zipfile
import platform
from pathlib import Path

def install_selenium():
    """安装Selenium库"""
    try:
        print("🚀 开始安装Selenium...")
        
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "selenium", "-i", "https://pypi.tuna.tsinghua.edu.cn/simple/"
        ], capture_output=True, text=True, check=True)
        
        print("✅ Selenium安装成功!")
        print(result.stdout)
        
        # 测试导入
        try:
            from selenium import webdriver
            print("✅ Selenium导入测试成功!")
            return True
        except ImportError as e:
            print(f"❌ Selenium导入失败: {e}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Selenium安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def download_chromedriver():
    """下载ChromeDriver"""
    try:
        print("🚀 开始下载ChromeDriver...")
        
        # 检测系统类型
        system = platform.system().lower()
        if system == "windows":
            driver_name = "chromedriver.exe"
            download_url = "https://chromedriver.storage.googleapis.com/114.0.5735.90/chromedriver_win32.zip"
        elif system == "darwin":  # macOS
            driver_name = "chromedriver"
            download_url = "https://chromedriver.storage.googleapis.com/114.0.5735.90/chromedriver_mac64.zip"
        else:  # Linux
            driver_name = "chromedriver"
            download_url = "https://chromedriver.storage.googleapis.com/114.0.5735.90/chromedriver_linux64.zip"
        
        # 创建drivers目录
        drivers_dir = Path("drivers")
        drivers_dir.mkdir(exist_ok=True)
        
        driver_path = drivers_dir / driver_name
        
        # 如果已存在，跳过下载
        if driver_path.exists():
            print(f"✅ ChromeDriver已存在: {driver_path}")
            return str(driver_path)
        
        # 下载ChromeDriver
        print(f"📥 从 {download_url} 下载ChromeDriver...")
        response = requests.get(download_url, timeout=30)
        response.raise_for_status()
        
        # 保存zip文件
        zip_path = drivers_dir / "chromedriver.zip"
        with open(zip_path, 'wb') as f:
            f.write(response.content)
        
        # 解压
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(drivers_dir)
        
        # 删除zip文件
        zip_path.unlink()
        
        # 设置执行权限（Linux/macOS）
        if system != "windows":
            os.chmod(driver_path, 0o755)
        
        print(f"✅ ChromeDriver下载成功: {driver_path}")
        return str(driver_path)
        
    except Exception as e:
        print(f"❌ ChromeDriver下载失败: {e}")
        return None

def test_selenium_setup():
    """测试Selenium设置"""
    try:
        print("🧪 测试Selenium设置...")
        
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        # 设置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        
        # 尝试启动Chrome
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.get("https://www.baidu.com")
            title = driver.title
            driver.quit()
            
            print(f"✅ Selenium测试成功! 页面标题: {title}")
            return True
            
        except Exception as e:
            print(f"❌ Selenium测试失败: {e}")
            print("💡 提示: 请确保已安装Chrome浏览器")
            return False
            
    except ImportError as e:
        print(f"❌ Selenium导入失败: {e}")
        return False

def check_chrome_installed():
    """检查Chrome是否已安装"""
    try:
        system = platform.system().lower()
        
        if system == "windows":
            # Windows Chrome路径
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            ]
        elif system == "darwin":  # macOS
            chrome_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
            ]
        else:  # Linux
            chrome_paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/chromium-browser",
                "/usr/bin/chrome"
            ]
        
        for path in chrome_paths:
            if os.path.exists(path):
                print(f"✅ 找到Chrome浏览器: {path}")
                return True
        
        print("❌ 未找到Chrome浏览器")
        print("💡 请先安装Chrome浏览器: https://www.google.com/chrome/")
        return False
        
    except Exception as e:
        print(f"❌ 检查Chrome安装失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Selenium和ChromeDriver安装工具")
    print("=" * 50)
    
    # 1. 检查Chrome浏览器
    if not check_chrome_installed():
        print("\n❌ 请先安装Chrome浏览器后再运行此脚本")
        sys.exit(1)
    
    # 2. 安装Selenium
    if not install_selenium():
        print("\n❌ Selenium安装失败")
        sys.exit(1)
    
    # 3. 下载ChromeDriver
    driver_path = download_chromedriver()
    if not driver_path:
        print("\n❌ ChromeDriver下载失败")
        sys.exit(1)
    
    # 4. 测试设置
    if test_selenium_setup():
        print("\n🎉 Selenium和ChromeDriver安装完成！")
        print("现在可以使用同花顺研报爬虫功能了！")
    else:
        print("\n❌ Selenium设置测试失败")
        print("请检查Chrome浏览器是否正确安装")
        sys.exit(1)
