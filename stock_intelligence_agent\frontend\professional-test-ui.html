<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票智能体 - 专业测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .status-bar {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
            animation: pulse 2s infinite;
        }

        .status-dot.online {
            background: #28a745;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            min-height: 800px;
        }

        .sidebar {
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            padding: 20px;
        }

        .test-category {
            margin-bottom: 25px;
        }

        .category-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }

        .test-button {
            display: block;
            width: 100%;
            padding: 12px 15px;
            margin-bottom: 8px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            color: #495057;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 0.9em;
        }

        .test-button:hover {
            background: #3498db;
            color: white;
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .test-button.active {
            background: #3498db;
            color: white;
        }

        .content-area {
            padding: 30px;
            overflow-y: auto;
        }

        .test-panel {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .test-panel.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .panel-title {
            font-size: 1.8em;
            color: #2c3e50;
            font-weight: 300;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
        }

        .input-group label {
            min-width: 100px;
            font-weight: 500;
            color: #495057;
        }

        .input-group input,
        .input-group select {
            flex: 1;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 0.9em;
        }

        .result-container {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 500px;
            overflow-y: auto;
        }

        .result-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .result-title {
            font-weight: 600;
            color: #2c3e50;
        }

        .result-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .status-loading {
            background: #d1ecf1;
            color: #0c5460;
        }

        .json-viewer {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.85em;
            line-height: 1.4;
            overflow-x: auto;
        }

        .json-key {
            color: #3498db;
        }

        .json-string {
            color: #2ecc71;
        }

        .json-number {
            color: #e74c3c;
        }

        .json-boolean {
            color: #f39c12;
        }

        .feedback-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .feedback-title {
            font-weight: 600;
            color: #856404;
            margin-bottom: 15px;
        }

        .feedback-textarea {
            width: 100%;
            min-height: 100px;
            padding: 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            resize: vertical;
            font-family: inherit;
        }

        .quick-tests {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }

        .quick-test-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .quick-test-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .quick-test-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .quick-test-desc {
            font-size: 0.85em;
            color: #6c757d;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 10px;
            padding: 30px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .modal-title {
            font-size: 1.5em;
            color: #2c3e50;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5em;
            cursor: pointer;
            color: #6c757d;
        }

        .close-btn:hover {
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🚀 股票智能体专业测试工具</h1>
            <p>全面测试股票数据、新闻分析、市场洞察等核心功能</p>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">检查服务状态中...</span>
            </div>
            <div>
                <span>API地址: </span>
                <strong id="apiUrl">http://localhost:8000</strong>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="test-category">
                    <div class="category-title">📊 股票功能</div>
                    <button class="test-button" onclick="showPanel('stocks', event)">股票列表测试</button>
                    <button class="test-button" onclick="showPanel('stock-detail', event)">股票详情测试</button>
                    <button class="test-button" onclick="showPanel('hot-stocks', event)">热门股票测试</button>
                </div>

                <div class="test-category">
                    <div class="category-title">📈 分析功能</div>
                    <button class="test-button" onclick="showPanel('technical', event)">技术分析测试</button>
                    <button class="test-button" onclick="showPanel('fundamental', event)">研报评级</button>
                    <button class="test-button" onclick="showPanel('risk', event)">风险评估测试</button>
                </div>

                <div class="test-category">
                    <div class="category-title">🏪 市场功能</div>
                    <button class="test-button" onclick="showPanel('market', event)">市场概览测试</button>
                    <button class="test-button" onclick="showPanel('sectors', event)">板块分析测试</button>
                    <button class="test-button" onclick="showPanel('sentiment', event)">市场情绪测试</button>
                </div>

                <div class="test-category">
                    <div class="category-title">📰 新闻功能</div>
                    <button class="test-button" onclick="showPanel('news', event)">新闻列表测试</button>
                    <button class="test-button" onclick="showPanel('hot-topics', event)">热点话题测试</button>
                    <button class="test-button" onclick="showPanel('news-filter', event)">新闻筛选测试</button>
                </div>

                <div class="test-category">
                    <div class="category-title">🧪 综合测试</div>
                    <button class="test-button" onclick="showPanel('performance', event)">性能测试</button>
                    <button class="test-button" onclick="showPanel('batch', event)">批量测试</button>
                    <button class="test-button" onclick="showPanel('feedback', event)">反馈收集</button>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 默认面板 -->
                <div id="welcome" class="test-panel active">
                    <div class="panel-header">
                        <h2 class="panel-title">欢迎使用股票智能体测试工具</h2>
                    </div>
                    
                    <div class="quick-tests">
                        <div class="quick-test-card" onclick="quickTest('health')">
                            <div class="quick-test-icon">🔍</div>
                            <div class="quick-test-title">健康检查</div>
                            <div class="quick-test-desc">检查后端服务状态</div>
                        </div>
                        
                        <div class="quick-test-card" onclick="quickTest('stocks')">
                            <div class="quick-test-icon">📊</div>
                            <div class="quick-test-title">股票数据</div>
                            <div class="quick-test-desc">测试股票列表和详情</div>
                        </div>
                        
                        <div class="quick-test-card" onclick="quickTest('news')">
                            <div class="quick-test-icon">📰</div>
                            <div class="quick-test-title">新闻分析</div>
                            <div class="quick-test-desc">测试新闻和热点话题</div>
                        </div>
                        
                        <div class="quick-test-card" onclick="quickTest('sectors')">
                            <div class="quick-test-icon">🏪</div>
                            <div class="quick-test-title">板块分析</div>
                            <div class="quick-test-desc">测试概念板块功能</div>
                        </div>
                    </div>

                    <div class="result-container">
                        <div class="result-header">
                            <span class="result-title">快速测试结果</span>
                        </div>
                        <div id="quickTestResult">
                            <p>点击上方快速测试卡片开始测试...</p>
                        </div>
                    </div>
                </div>

                <!-- 股票列表测试面板 -->
                <div id="stocks" class="test-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">📊 股票列表测试</h2>
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="testStockList()">获取股票列表</button>
                            <button class="btn btn-success" onclick="testStockListWithParams()">自定义参数测试</button>
                        </div>
                    </div>

                    <div class="input-group">
                        <label>返回数量:</label>
                        <input type="number" id="stockLimit" value="10" min="1" max="300">
                        
                        <label>市场:</label>
                        <select id="stockMarket">
                            <option value="all">全部市场</option>
                            <option value="SH">上海市场</option>
                            <option value="SZ">深圳市场</option>
                        </select>
                        
                        <label>排序:</label>
                        <select id="stockSort">
                            <option value="pct_change">按涨跌幅</option>
                            <option value="amount">按成交额</option>
                            <option value="volume">按成交量</option>
                        </select>
                    </div>

                    <div class="result-container">
                        <div class="result-header">
                            <span class="result-title">测试结果</span>
                            <span id="stocksStatus" class="result-status">等待测试</span>
                        </div>
                        <div id="stocksResult">等待测试...</div>
                    </div>

                    <div class="feedback-section">
                        <div class="feedback-title">💬 测试反馈</div>
                        <textarea class="feedback-textarea" id="stocksFeedback" placeholder="请输入您对股票列表功能的反馈意见..."></textarea>
                        <button class="btn btn-primary" style="margin-top: 10px;" onclick="saveFeedback('stocks')">保存反馈</button>
                    </div>
                </div>

                <!-- 股票详情测试面板 -->
                <div id="stock-detail" class="test-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">🔍 股票详情测试</h2>
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="testStockDetail()">查询股票详情</button>
                            <button class="btn btn-warning" onclick="testMultipleStocks()">批量查询</button>
                        </div>
                    </div>

                    <div class="input-group">
                        <label>股票代码:</label>
                        <input type="text" id="stockCode" value="300750" placeholder="输入6位股票代码">
                        <button class="btn btn-primary" onclick="testStockDetail()">查询</button>
                    </div>

                    <div class="input-group">
                        <label>批量代码:</label>
                        <input type="text" id="batchCodes" value="300750,002230,000001" placeholder="多个代码用逗号分隔">
                    </div>

                    <div class="result-container">
                        <div class="result-header">
                            <span class="result-title">股票详情结果</span>
                            <span id="stockDetailStatus" class="result-status">等待测试</span>
                        </div>
                        <div id="stockDetailResult">输入股票代码开始测试...</div>
                    </div>

                    <div class="feedback-section">
                        <div class="feedback-title">💬 概念板块信息反馈</div>
                        <textarea class="feedback-textarea" id="stock-detailFeedback" placeholder="请重点反馈概念板块信息的准确性和完整性..."></textarea>
                        <button class="btn btn-primary" style="margin-top: 10px;" onclick="saveFeedback('stock-detail')">保存反馈</button>
                    </div>
                </div>

                <!-- 热门股票测试面板 -->
                <div id="hot-stocks" class="test-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">🔥 热门股票测试</h2>
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="testHotStocks('pct_change')">涨幅榜</button>
                            <button class="btn btn-success" onclick="testHotStocks('amount')">成交额榜</button>
                        </div>
                    </div>

                    <div class="input-group">
                        <label>排序类型:</label>
                        <select id="hotStockType">
                            <option value="pct_change">涨跌幅排序</option>
                            <option value="amount">成交额排序</option>
                        </select>

                        <label>数量:</label>
                        <input type="number" id="hotStockLimit" value="20" min="1" max="300">

                        <button class="btn btn-primary" onclick="testHotStocksCustom()">自定义查询</button>
                    </div>

                    <div class="result-container">
                        <div class="result-header">
                            <span class="result-title">热门股票结果</span>
                            <span id="hotStocksStatus" class="result-status">等待测试</span>
                        </div>
                        <div id="hotStocksResult">点击上方按钮开始测试...</div>
                    </div>

                    <div class="feedback-section">
                        <div class="feedback-title">💬 排行榜数据反馈</div>
                        <textarea class="feedback-textarea" id="hot-stocksFeedback" placeholder="请反馈涨幅榜和成交额榜的数据准确性..."></textarea>
                        <button class="btn btn-primary" style="margin-top: 10px;" onclick="saveFeedback('hot-stocks')">保存反馈</button>
                    </div>
                </div>

                <!-- 新闻测试面板 -->
                <div id="news" class="test-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">📰 新闻列表测试</h2>
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="testNews()">获取新闻</button>
                            <button class="btn btn-success" onclick="testNewsWithFilter()">筛选测试</button>
                        </div>
                    </div>

                    <div class="input-group">
                        <label>新闻分类:</label>
                        <select id="newsCategory">
                            <option value="all">全部新闻</option>
                            <option value="market_dynamics">市场动态</option>
                            <option value="policy">政策监管</option>
                            <option value="company">公司公告</option>
                            <option value="market">市场行情</option>
                            <option value="funds">资金流向</option>
                        </select>

                        <label>数量:</label>
                        <input type="number" id="newsLimit" value="10" min="1" max="50">
                    </div>

                    <div class="input-group">
                        <label>搜索关键词:</label>
                        <input type="text" id="newsSearch" placeholder="输入关键词搜索">
                        <button class="btn btn-primary" onclick="testNewsSearch()">搜索</button>
                    </div>

                    <div class="result-container">
                        <div class="result-header">
                            <span class="result-title">新闻列表结果</span>
                            <span id="newsStatus" class="result-status">等待测试</span>
                        </div>
                        <div id="newsResult">选择分类开始测试...</div>
                    </div>

                    <div class="feedback-section">
                        <div class="feedback-title">💬 新闻质量反馈</div>
                        <textarea class="feedback-textarea" id="newsFeedback" placeholder="请反馈新闻的A股相关性、来源质量等..."></textarea>
                        <button class="btn btn-primary" style="margin-top: 10px;" onclick="saveFeedback('news')">保存反馈</button>
                    </div>
                </div>

                <!-- 热点话题测试面板 -->
                <div id="hot-topics" class="test-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">🔥 热点话题测试</h2>
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="testHotTopics()">获取热点话题</button>
                            <button class="btn btn-success" onclick="analyzeTopics()">话题分析</button>
                        </div>
                    </div>

                    <div class="input-group">
                        <label>话题数量:</label>
                        <input type="number" id="topicsLimit" value="20" min="1" max="50">
                        <button class="btn btn-primary" onclick="testHotTopics()">刷新</button>
                    </div>

                    <div class="result-container">
                        <div class="result-header">
                            <span class="result-title">热点话题结果</span>
                            <span id="hotTopicsStatus" class="result-status">等待测试</span>
                        </div>
                        <div id="hotTopicsResult">点击获取热点话题...</div>
                    </div>

                    <div class="feedback-section">
                        <div class="feedback-title">💬 热点话题反馈</div>
                        <textarea class="feedback-textarea" id="hot-topicsFeedback" placeholder="请重点反馈概念题材的准确性和热度排序..."></textarea>
                        <button class="btn btn-primary" style="margin-top: 10px;" onclick="saveFeedback('hot-topics')">保存反馈</button>
                    </div>
                </div>

                <!-- 技术分析测试面板 -->
                <div id="technical" class="test-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">📈 技术分析测试</h2>
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="testTechnicalAnalysis()">技术分析</button>
                        </div>
                    </div>

                    <div class="input-group">
                        <label>股票代码:</label>
                        <input type="text" id="technicalStockCode" value="300750" placeholder="输入股票代码">
                        <button class="btn btn-primary" onclick="testTechnicalAnalysis()">分析</button>
                    </div>

                    <div class="result-container">
                        <div class="result-header">
                            <span class="result-title">技术分析结果</span>
                            <span id="technicalStatus" class="result-status">等待测试</span>
                        </div>
                        <div id="technicalResult">输入股票代码开始技术分析...</div>
                    </div>

                    <div class="feedback-section">
                        <div class="feedback-title">💬 技术分析反馈</div>
                        <textarea class="feedback-textarea" id="technicalFeedback" placeholder="请反馈技术指标的准确性和分析质量..."></textarea>
                        <button class="btn btn-primary" style="margin-top: 10px;" onclick="saveFeedback('technical')">保存反馈</button>
                    </div>
                </div>

                <!-- 研报评级测试面板 -->
                <div id="fundamental" class="test-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">📊 研报评级测试</h2>
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="testResearchRating()">研报评级</button>
                        </div>
                    </div>

                    <div class="input-group">
                        <label>股票代码:</label>
                        <input type="text" id="researchStockCode" value="300750" placeholder="输入股票代码">
                        <button class="btn btn-primary" onclick="testResearchRating()">查询评级</button>
                    </div>

                    <div class="result-container">
                        <div class="result-header">
                            <span class="result-title">研报评级结果</span>
                            <span id="researchStatus" class="result-status">等待测试</span>
                        </div>
                        <div id="researchResult">输入股票代码开始查询研报评级...</div>
                    </div>

                    <div class="feedback-section">
                        <div class="feedback-title">💬 研报评级反馈</div>
                        <textarea class="feedback-textarea" id="researchFeedback" placeholder="请反馈研报评级的准确性和参考价值..."></textarea>
                        <button class="btn btn-primary" style="margin-top: 10px;" onclick="saveFeedback('research')">保存反馈</button>
                    </div>
                </div>

                <!-- 风险评估测试面板 -->
                <div id="risk" class="test-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">⚠️ 风险评估测试</h2>
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="testRiskAssessment()">风险评估</button>
                        </div>
                    </div>

                    <div class="input-group">
                        <label>股票代码:</label>
                        <input type="text" id="riskStockCode" value="300750" placeholder="输入股票代码">
                        <button class="btn btn-primary" onclick="testRiskAssessment()">评估</button>
                    </div>

                    <div class="result-container">
                        <div class="result-header">
                            <span class="result-title">风险评估结果</span>
                            <span id="riskStatus" class="result-status">等待测试</span>
                        </div>
                        <div id="riskResult">输入股票代码开始风险评估...</div>
                    </div>

                    <div class="feedback-section">
                        <div class="feedback-title">💬 风险评估反馈</div>
                        <textarea class="feedback-textarea" id="riskFeedback" placeholder="请反馈风险指标的准确性和评估合理性..."></textarea>
                        <button class="btn btn-primary" style="margin-top: 10px;" onclick="saveFeedback('risk')">保存反馈</button>
                    </div>
                </div>

                <!-- 市场概览测试面板 -->
                <div id="market" class="test-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">🏪 市场概览测试</h2>
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="testMarketOverview()">市场概览</button>
                        </div>
                    </div>

                    <div class="result-container">
                        <div class="result-header">
                            <span class="result-title">市场概览结果</span>
                            <span id="marketStatus" class="result-status">等待测试</span>
                        </div>
                        <div id="marketResult">点击按钮获取市场概览...</div>
                    </div>

                    <div class="feedback-section">
                        <div class="feedback-title">💬 市场概览反馈</div>
                        <textarea class="feedback-textarea" id="marketFeedback" placeholder="请反馈市场数据的完整性和准确性..."></textarea>
                        <button class="btn btn-primary" style="margin-top: 10px;" onclick="saveFeedback('market')">保存反馈</button>
                    </div>
                </div>

                <!-- 市场情绪测试面板 -->
                <div id="sentiment" class="test-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">😊 市场情绪测试</h2>
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="testMarketSentiment()">市场情绪</button>
                        </div>
                    </div>

                    <div class="result-container">
                        <div class="result-header">
                            <span class="result-title">市场情绪结果</span>
                            <span id="sentimentStatus" class="result-status">等待测试</span>
                        </div>
                        <div id="sentimentResult">点击按钮获取市场情绪分析...</div>
                    </div>

                    <div class="feedback-section">
                        <div class="feedback-title">💬 市场情绪反馈</div>
                        <textarea class="feedback-textarea" id="sentimentFeedback" placeholder="请反馈市场情绪指标的准确性..."></textarea>
                        <button class="btn btn-primary" style="margin-top: 10px;" onclick="saveFeedback('sentiment')">保存反馈</button>
                    </div>
                </div>

                <!-- 板块分析测试面板 -->
                <div id="sectors" class="test-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">🏪 板块分析测试</h2>
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="testSectors('concept')">概念板块</button>
                            <button class="btn btn-success" onclick="testSectors('industry')">行业板块</button>
                            <button class="btn btn-warning" onclick="testSectorFunds()">资金流向</button>
                        </div>
                    </div>

                    <div class="input-group">
                        <label>板块类型:</label>
                        <select id="sectorType">
                            <option value="concept">概念板块</option>
                            <option value="industry">行业板块</option>
                        </select>

                        <label>数量:</label>
                        <input type="number" id="sectorLimit" value="20" min="1" max="50">

                        <button class="btn btn-primary" onclick="testSectorsCustom()">自定义查询</button>
                    </div>

                    <div class="result-container">
                        <div class="result-header">
                            <span class="result-title">板块分析结果</span>
                            <span id="sectorsStatus" class="result-status">等待测试</span>
                        </div>
                        <div id="sectorsResult">选择板块类型开始测试...</div>
                    </div>

                    <div class="feedback-section">
                        <div class="feedback-title">💬 板块数据反馈</div>
                        <textarea class="feedback-textarea" id="sectorsFeedback" placeholder="请反馈概念板块的准确性和排序合理性..."></textarea>
                        <button class="btn btn-primary" style="margin-top: 10px;" onclick="saveFeedback('sectors')">保存反馈</button>
                    </div>
                </div>

                <!-- 综合反馈面板 -->
                <div id="feedback" class="test-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">💬 综合反馈收集</h2>
                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="exportFeedbacks()">导出反馈</button>
                            <button class="btn btn-warning" onclick="clearAllFeedbacks()">清空反馈</button>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h3>📊 测试统计</h3>
                            <div id="testStats" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                                <p>总测试次数: <span id="totalTests">0</span></p>
                                <p>成功测试: <span id="successTests">0</span></p>
                                <p>失败测试: <span id="failedTests">0</span></p>
                                <p>平均响应时间: <span id="avgResponseTime">0ms</span></p>
                            </div>

                            <h3>💬 反馈统计</h3>
                            <div id="feedbackStats" style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                                <p>反馈条数: <span id="totalFeedbacks">0</span></p>
                                <p>最新反馈: <span id="latestFeedback">无</span></p>
                            </div>
                        </div>

                        <div>
                            <h3>🎯 整体评价</h3>
                            <div style="margin-bottom: 15px;">
                                <label>功能完整性评分 (1-10):</label>
                                <input type="range" id="completenessScore" min="1" max="10" value="5" style="width: 100%;">
                                <span id="completenessValue">5</span>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label>数据准确性评分 (1-10):</label>
                                <input type="range" id="accuracyScore" min="1" max="10" value="5" style="width: 100%;">
                                <span id="accuracyValue">5</span>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label>用户体验评分 (1-10):</label>
                                <input type="range" id="uxScore" min="1" max="10" value="5" style="width: 100%;">
                                <span id="uxValue">5</span>
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label>整体满意度 (1-10):</label>
                                <input type="range" id="overallScore" min="1" max="10" value="5" style="width: 100%;">
                                <span id="overallValue">5</span>
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 25px;">
                        <h3>📝 综合意见和建议</h3>
                        <textarea id="overallFeedback" class="feedback-textarea" style="min-height: 150px;"
                                  placeholder="请输入您对股票智能体的整体评价、改进建议、功能需求等..."></textarea>
                        <div style="margin-top: 15px;">
                            <button class="btn btn-primary" onclick="saveOverallFeedback()">提交综合反馈</button>
                            <button class="btn btn-success" onclick="generateReport()">生成测试报告</button>
                        </div>
                    </div>

                    <div id="finalReport" style="margin-top: 25px; display: none;">
                        <h3>📋 测试报告</h3>
                        <div id="reportContent" style="background: #f8f9fa; padding: 20px; border-radius: 8px;"></div>
                    </div>
                </div>

                <!-- 性能测试面板 -->
                <div id="performance" class="test-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">⚡ 性能测试</h2>
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="runPerformanceTest()">开始性能测试</button>
                            <button class="btn btn-warning" onclick="stopPerformanceTest()">停止测试</button>
                        </div>
                    </div>

                    <div class="input-group">
                        <label>并发数:</label>
                        <input type="number" id="concurrency" value="5" min="1" max="20">

                        <label>测试次数:</label>
                        <input type="number" id="testCount" value="10" min="1" max="100">

                        <label>测试接口:</label>
                        <select id="testEndpoint">
                            <option value="/health">健康检查</option>
                            <option value="/api/v1/stocks?limit=10">股票列表</option>
                            <option value="/api/v1/news?limit=5">新闻列表</option>
                            <option value="/api/v1/sectors/hot?limit=10">热门板块</option>
                        </select>
                    </div>

                    <div class="result-container">
                        <div class="result-header">
                            <span class="result-title">性能测试结果</span>
                            <span id="performanceStatus" class="result-status">等待测试</span>
                        </div>
                        <div id="performanceResult">配置参数后开始性能测试...</div>
                    </div>

                    <div class="feedback-section">
                        <div class="feedback-title">💬 性能反馈</div>
                        <textarea class="feedback-textarea" id="performanceFeedback" placeholder="请反馈系统响应速度、稳定性等性能表现..."></textarea>
                        <button class="btn btn-primary" style="margin-top: 10px;" onclick="saveFeedback('performance')">保存反馈</button>
                    </div>
                </div>

                <!-- 批量测试面板 -->
                <div id="batch" class="test-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">🔄 批量测试</h2>
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="runBatchTest()">开始批量测试</button>
                            <button class="btn btn-success" onclick="runFullTest()">全功能测试</button>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <h3>选择测试项目:</h3>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                            <label><input type="checkbox" id="test_stocks" checked> 股票功能</label>
                            <label><input type="checkbox" id="test_news" checked> 新闻功能</label>
                            <label><input type="checkbox" id="test_sectors" checked> 板块功能</label>
                            <label><input type="checkbox" id="test_analysis" checked> 分析功能</label>
                            <label><input type="checkbox" id="test_market" checked> 市场功能</label>
                            <label><input type="checkbox" id="test_performance" checked> 性能测试</label>
                        </div>
                    </div>

                    <div class="result-container">
                        <div class="result-header">
                            <span class="result-title">批量测试进度</span>
                            <span id="batchStatus" class="result-status">等待测试</span>
                        </div>
                        <div id="batchResult">选择测试项目后开始批量测试...</div>
                    </div>

                    <div class="feedback-section">
                        <div class="feedback-title">💬 批量测试反馈</div>
                        <textarea class="feedback-textarea" id="batchFeedback" placeholder="请反馈批量测试的整体表现和发现的问题..."></textarea>
                        <button class="btn btn-primary" style="margin-top: 10px;" onclick="saveFeedback('batch')">保存反馈</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        const API_BASE = 'http://localhost:8000';
        let currentPanel = 'welcome';
        let testResults = {};
        let feedbacks = {};

        // 初始化（已合并到页面底部）

        // 检查服务器状态
        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    document.getElementById('statusDot').classList.add('online');
                    document.getElementById('statusText').textContent = '服务在线';
                } else {
                    throw new Error('Service unhealthy');
                }
            } catch (error) {
                document.getElementById('statusDot').classList.remove('online');
                document.getElementById('statusText').textContent = '服务离线';
            }
        }

        // 显示面板
        function showPanel(panelId, event) {
            // 隐藏所有面板
            document.querySelectorAll('.test-panel').forEach(panel => {
                panel.classList.remove('active');
            });

            // 移除所有按钮的active状态
            document.querySelectorAll('.test-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // 显示目标面板
            const targetPanel = document.getElementById(panelId);
            if (targetPanel) {
                targetPanel.classList.add('active');
                currentPanel = panelId;
            }

            // 设置按钮active状态
            if (event && event.target) {
                event.target.classList.add('active');
            }
        }

        // 快速测试
        async function quickTest(testType) {
            const resultDiv = document.getElementById('quickTestResult');
            resultDiv.innerHTML = '<div class="status-loading">测试中...</div>';
            
            try {
                let url, title;
                
                switch(testType) {
                    case 'health':
                        url = '/health';
                        title = '健康检查';
                        break;
                    case 'stocks':
                        url = '/api/v1/stocks?limit=5';
                        title = '股票数据';
                        break;
                    case 'news':
                        url = '/api/v1/news?limit=3';
                        title = '新闻数据';
                        break;
                    case 'sectors':
                        url = '/api/v1/sectors/hot?limit=5';
                        title = '板块数据';
                        break;
                }
                
                const response = await fetch(`${API_BASE}${url}`);
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="status-success">✅ ${title}测试成功</div>
                    <div class="json-viewer">${JSON.stringify(data, null, 2)}</div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="status-error">❌ 测试失败: ${error.message}</div>
                `;
            }
        }

        // 测试股票列表
        async function testStockList() {
            await testAPI('/api/v1/stocks?limit=10', 'stocksResult', 'stocksStatus', '股票列表');
        }

        // 自定义参数测试股票列表
        async function testStockListWithParams() {
            const limit = document.getElementById('stockLimit').value;
            const market = document.getElementById('stockMarket').value;
            const sort = document.getElementById('stockSort').value;
            
            const params = new URLSearchParams({
                limit: limit,
                market: market,
                sort_by: sort
            });
            
            await testAPI(`/api/v1/stocks?${params}`, 'stocksResult', 'stocksStatus', '自定义股票列表');
        }

        // 通用API测试函数
        async function testAPI(endpoint, resultId, statusId, testName) {
            const resultDiv = document.getElementById(resultId);
            const statusSpan = document.getElementById(statusId);
            
            statusSpan.textContent = '测试中...';
            statusSpan.className = 'result-status status-loading';
            
            try {
                const startTime = Date.now();
                const response = await fetch(`${API_BASE}${endpoint}`);
                const endTime = Date.now();
                const data = await response.json();
                
                statusSpan.textContent = `成功 (${endTime - startTime}ms)`;
                statusSpan.className = 'result-status status-success';
                
                resultDiv.innerHTML = `
                    <div style="margin-bottom: 15px;">
                        <strong>📊 ${testName}测试结果</strong>
                        <span style="float: right; color: #28a745;">响应时间: ${endTime - startTime}ms</span>
                    </div>
                    <div class="json-viewer">${formatJSON(data)}</div>
                `;
                
                // 保存测试结果
                testResults[testName] = {
                    success: true,
                    responseTime: endTime - startTime,
                    data: data
                };
                
            } catch (error) {
                statusSpan.textContent = '失败';
                statusSpan.className = 'result-status status-error';
                
                resultDiv.innerHTML = `
                    <div class="status-error">❌ ${testName}测试失败</div>
                    <div style="margin-top: 10px; color: #721c24;">
                        错误信息: ${error.message}
                    </div>
                `;
                
                testResults[testName] = {
                    success: false,
                    error: error.message
                };
            }
        }

        // 格式化JSON显示
        function formatJSON(obj) {
            return JSON.stringify(obj, null, 2)
                .replace(/(".*?"):/g, '<span class="json-key">$1</span>:')
                .replace(/: (".*?")/g, ': <span class="json-string">$1</span>')
                .replace(/: (\d+\.?\d*)/g, ': <span class="json-number">$1</span>')
                .replace(/: (true|false)/g, ': <span class="json-boolean">$1</span>');
        }

        // 测试股票详情
        async function testStockDetail() {
            const stockCode = document.getElementById('stockCode').value;
            if (!stockCode) {
                alert('请输入股票代码');
                return;
            }
            await testAPI(`/api/v1/stocks/${stockCode}`, 'stockDetailResult', 'stockDetailStatus', '股票详情');
        }

        // 批量测试股票
        async function testMultipleStocks() {
            const codes = document.getElementById('batchCodes').value.split(',');
            const resultDiv = document.getElementById('stockDetailResult');
            const statusSpan = document.getElementById('stockDetailStatus');

            statusSpan.textContent = '批量测试中...';
            statusSpan.className = 'result-status status-loading';

            let results = [];
            for (let code of codes) {
                code = code.trim();
                if (code) {
                    try {
                        const response = await fetch(`${API_BASE}/api/v1/stocks/${code}`);
                        const data = await response.json();
                        results.push({code, success: true, data});
                    } catch (error) {
                        results.push({code, success: false, error: error.message});
                    }
                }
            }

            statusSpan.textContent = '批量测试完成';
            statusSpan.className = 'result-status status-success';

            resultDiv.innerHTML = `
                <div><strong>📊 批量股票详情测试结果</strong></div>
                <div class="json-viewer">${formatJSON(results)}</div>
            `;
        }

        // 测试热门股票
        async function testHotStocks(sortType) {
            const limit = sortType === 'pct_change' ? 100 : 300;
            await testAPI(`/api/v1/stocks/hot?sort_type=${sortType}&limit=${limit}`, 'hotStocksResult', 'hotStocksStatus', `热门股票-${sortType}`);
        }

        // 自定义热门股票测试
        async function testHotStocksCustom() {
            const sortType = document.getElementById('hotStockType').value;
            const limit = document.getElementById('hotStockLimit').value;
            await testAPI(`/api/v1/stocks/hot?sort_type=${sortType}&limit=${limit}`, 'hotStocksResult', 'hotStocksStatus', '自定义热门股票');
        }

        // 测试新闻
        async function testNews() {
            await testAPI('/api/v1/news?limit=10', 'newsResult', 'newsStatus', '新闻列表');
        }

        // 筛选测试新闻
        async function testNewsWithFilter() {
            const category = document.getElementById('newsCategory').value;
            const limit = document.getElementById('newsLimit').value;
            const params = new URLSearchParams({limit, category});
            await testAPI(`/api/v1/news?${params}`, 'newsResult', 'newsStatus', '筛选新闻');
        }

        // 搜索新闻
        async function testNewsSearch() {
            const search = document.getElementById('newsSearch').value;
            const category = document.getElementById('newsCategory').value;
            const limit = document.getElementById('newsLimit').value;
            const params = new URLSearchParams({limit, category, search});
            await testAPI(`/api/v1/news?${params}`, 'newsResult', 'newsStatus', '搜索新闻');
        }

        // 测试热点话题
        async function testHotTopics() {
            const limit = document.getElementById('topicsLimit').value;
            await testAPI(`/api/v1/news/hot-topics?limit=${limit}`, 'hotTopicsResult', 'hotTopicsStatus', '热点话题');
        }

        // 分析话题
        async function analyzeTopics() {
            const resultDiv = document.getElementById('hotTopicsResult');
            const statusSpan = document.getElementById('hotTopicsStatus');

            statusSpan.textContent = '分析中...';
            statusSpan.className = 'result-status status-loading';

            try {
                const response = await fetch(`${API_BASE}/api/v1/news/hot-topics?limit=20`);
                const topics = await response.json();

                // 分析概念占比
                const conceptCount = topics.filter(t => t.is_concept).length;
                const conceptRatio = (conceptCount / topics.length * 100).toFixed(1);

                statusSpan.textContent = '分析完成';
                statusSpan.className = 'result-status status-success';

                resultDiv.innerHTML = `
                    <div><strong>📊 热点话题分析结果</strong></div>
                    <div style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                        <p><strong>概念话题占比:</strong> ${conceptRatio}% (${conceptCount}/${topics.length})</p>
                        <p><strong>热度最高:</strong> ${topics[0]?.topic} (热度: ${topics[0]?.heat_score})</p>
                        <p><strong>趋势向上:</strong> ${topics.filter(t => t.trend === 'up').length} 个</p>
                    </div>
                    <div class="json-viewer">${formatJSON(topics)}</div>
                `;

            } catch (error) {
                statusSpan.textContent = '分析失败';
                statusSpan.className = 'result-status status-error';
                resultDiv.innerHTML = `<div class="status-error">分析失败: ${error.message}</div>`;
            }
        }

        // 测试板块
        async function testSectors(sectorType) {
            await testAPI(`/api/v1/sectors/hot?sector_type=${sectorType}&limit=20`, 'sectorsResult', 'sectorsStatus', `${sectorType}板块`);
        }

        // 自定义板块测试
        async function testSectorsCustom() {
            const sectorType = document.getElementById('sectorType').value;
            const limit = document.getElementById('sectorLimit').value;
            await testAPI(`/api/v1/sectors/hot?sector_type=${sectorType}&limit=${limit}`, 'sectorsResult', 'sectorsStatus', '自定义板块');
        }

        // 测试板块资金流向
        async function testSectorFunds() {
            await testAPI('/api/v1/sectors/funds-flow', 'sectorsResult', 'sectorsStatus', '板块资金流向');
        }

        // 测试技术分析
        async function testTechnicalAnalysis() {
            const stockCode = document.getElementById('technicalStockCode')?.value || '300750';
            await testAPI(`/api/v1/analysis/technical/${stockCode}`, 'technicalResult', 'technicalStatus', '技术分析');
        }

        // 测试研报评级
        async function testResearchRating() {
            const stockCode = document.getElementById('researchStockCode')?.value || '300750';
            await testAPI(`/api/v1/analysis/research-rating/${stockCode}`, 'researchResult', 'researchStatus', '研报评级');
        }

        // 测试风险评估
        async function testRiskAssessment() {
            const stockCode = document.getElementById('riskStockCode')?.value || '300750';
            await testAPI(`/api/v1/analysis/risk/${stockCode}`, 'riskResult', 'riskStatus', '风险评估');
        }

        // 测试市场概览
        async function testMarketOverview() {
            await testAPI('/api/v1/market/overview', 'marketResult', 'marketStatus', '市场概览');
        }

        // 测试市场情绪
        async function testMarketSentiment() {
            await testAPI('/api/v1/analysis/market/sentiment', 'sentimentResult', 'sentimentStatus', '市场情绪');
        }

        // 保存反馈
        function saveFeedback(category) {
            const feedbackText = document.getElementById(`${category}Feedback`).value;
            if (feedbackText.trim()) {
                feedbacks[category] = {
                    text: feedbackText,
                    timestamp: new Date().toISOString(),
                    testResults: testResults[category] || null
                };

                alert('反馈已保存！感谢您的宝贵意见。');
                console.log('反馈收集:', feedbacks);

                // 清空反馈框
                document.getElementById(`${category}Feedback`).value = '';
            }
        }

        // 导出所有反馈
        function exportFeedbacks() {
            const allFeedbacks = {
                timestamp: new Date().toISOString(),
                testResults: testResults,
                feedbacks: feedbacks,
                summary: {
                    totalTests: Object.keys(testResults).length,
                    successfulTests: Object.values(testResults).filter(r => r.success).length,
                    totalFeedbacks: Object.keys(feedbacks).length
                }
            };

            const blob = new Blob([JSON.stringify(allFeedbacks, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `股票智能体测试反馈_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 更新统计信息
        function updateStats() {
            const totalTests = Object.keys(testResults).length;
            const successTests = Object.values(testResults).filter(r => r.success).length;
            const failedTests = totalTests - successTests;
            const avgTime = totalTests > 0 ?
                Object.values(testResults).filter(r => r.responseTime).reduce((sum, r) => sum + r.responseTime, 0) / totalTests : 0;

            document.getElementById('totalTests').textContent = totalTests;
            document.getElementById('successTests').textContent = successTests;
            document.getElementById('failedTests').textContent = failedTests;
            document.getElementById('avgResponseTime').textContent = Math.round(avgTime) + 'ms';

            const totalFeedbacks = Object.keys(feedbacks).length;
            const latestFeedback = totalFeedbacks > 0 ?
                Object.keys(feedbacks)[Object.keys(feedbacks).length - 1] : '无';

            document.getElementById('totalFeedbacks').textContent = totalFeedbacks;
            document.getElementById('latestFeedback').textContent = latestFeedback;
        }

        // 评分滑块事件（已合并到页面底部）

        // 保存综合反馈（重复定义已删除）

        // 生成测试报告（重复定义已删除）

        // 性能测试（重复定义已删除）

        // 批量测试（重复定义已删除）

        // 清空所有反馈（保留此版本）
        function clearAllFeedbacks() {
            if (confirm('确定要清空所有反馈数据吗？')) {
                feedbacks = {};
                testResults = {};
                updateStats();
                alert('所有反馈数据已清空');
            }
        }

        // 保存综合反馈（保留此版本）
        function saveOverallFeedback() {
            const overallFeedback = document.getElementById('overallFeedback')?.value;
            const scores = {
                completeness: document.getElementById('completenessScore')?.value,
                accuracy: document.getElementById('accuracyScore')?.value,
                ux: document.getElementById('uxScore')?.value,
                overall: document.getElementById('overallScore')?.value
            };

            feedbacks['overall'] = {
                text: overallFeedback,
                scores: scores,
                timestamp: new Date().toISOString()
            };

            alert('综合反馈已保存！');
            updateStats();
        }

        // 生成测试报告
        function generateReport() {
            const totalTests = Object.keys(testResults).length;
            const successfulTests = Object.values(testResults).filter(r => r.success).length;
            const avgResponseTime = totalTests > 0 ?
                Object.values(testResults).filter(r => r.responseTime).reduce((sum, r) => sum + r.responseTime, 0) / totalTests : 0;

            const report = {
                testSummary: {
                    totalTests: totalTests,
                    successRate: totalTests > 0 ? (successfulTests / totalTests * 100).toFixed(1) : 0,
                    avgResponseTime: avgResponseTime
                },
                scores: feedbacks.overall?.scores || {},
                recommendations: []
            };

            // 生成建议
            if (report.testSummary.successRate < 80) {
                report.recommendations.push('建议检查网络连接和服务稳定性');
            }
            if (report.testSummary.avgResponseTime > 1000) {
                report.recommendations.push('建议优化API响应速度');
            }
            if (Object.keys(feedbacks).length < 3) {
                report.recommendations.push('建议增加更多功能测试和反馈');
            }

            const reportDiv = document.getElementById('finalReport');
            const contentDiv = document.getElementById('reportContent');

            if (reportDiv && contentDiv) {
                reportDiv.style.display = 'block';
                contentDiv.innerHTML = `
                    <h4>📊 测试概要</h4>
                    <p>总测试数: ${report.testSummary.totalTests}</p>
                    <p>成功率: ${report.testSummary.successRate}%</p>
                    <p>平均响应时间: ${Math.round(report.testSummary.avgResponseTime)}ms</p>

                    <h4>📈 评分情况</h4>
                    <p>功能完整性: ${report.scores.completeness || 'N/A'}/10</p>
                    <p>数据准确性: ${report.scores.accuracy || 'N/A'}/10</p>
                    <p>用户体验: ${report.scores.ux || 'N/A'}/10</p>
                    <p>整体满意度: ${report.scores.overall || 'N/A'}/10</p>

                    <h4>💡 改进建议</h4>
                    ${report.recommendations.map(r => `<p>• ${r}</p>`).join('')}
                `;
            }
        }

        // 性能测试
        let performanceTestRunning = false;

        async function runPerformanceTest() {
            if (performanceTestRunning) return;

            performanceTestRunning = true;
            const concurrency = parseInt(document.getElementById('concurrency').value);
            const testCount = parseInt(document.getElementById('testCount').value);
            const endpoint = document.getElementById('testEndpoint').value;

            const resultDiv = document.getElementById('performanceResult');
            const statusSpan = document.getElementById('performanceStatus');

            statusSpan.textContent = '性能测试中...';
            statusSpan.className = 'result-status status-loading';

            const results = [];
            const startTime = Date.now();

            try {
                for (let i = 0; i < testCount; i += concurrency) {
                    const batch = [];
                    for (let j = 0; j < concurrency && (i + j) < testCount; j++) {
                        batch.push(fetch(`${API_BASE}${endpoint}`).then(r => r.json()));
                    }

                    const batchResults = await Promise.allSettled(batch);
                    results.push(...batchResults);

                    // 更新进度
                    const progress = Math.min(i + concurrency, testCount);
                    resultDiv.innerHTML = `<p>进度: ${progress}/${testCount} (${(progress/testCount*100).toFixed(1)}%)</p>`;
                }

                const endTime = Date.now();
                const totalTime = endTime - startTime;
                const successCount = results.filter(r => r.status === 'fulfilled').length;
                const avgTime = totalTime / testCount;
                const qps = (testCount / (totalTime / 1000)).toFixed(2);

                statusSpan.textContent = '性能测试完成';
                statusSpan.className = 'result-status status-success';

                resultDiv.innerHTML = `
                    <div><strong>⚡ 性能测试结果</strong></div>
                    <div style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                        <p><strong>总请求数:</strong> ${testCount}</p>
                        <p><strong>成功请求:</strong> ${successCount} (${(successCount/testCount*100).toFixed(1)}%)</p>
                        <p><strong>总耗时:</strong> ${totalTime}ms</p>
                        <p><strong>平均响应时间:</strong> ${avgTime.toFixed(2)}ms</p>
                        <p><strong>QPS:</strong> ${qps}</p>
                        <p><strong>并发数:</strong> ${concurrency}</p>
                    </div>
                `;

            } catch (error) {
                statusSpan.textContent = '性能测试失败';
                statusSpan.className = 'result-status status-error';
                resultDiv.innerHTML = `<div class="status-error">性能测试失败: ${error.message}</div>`;
            }

            performanceTestRunning = false;
        }

        function stopPerformanceTest() {
            performanceTestRunning = false;
            if (document.getElementById('performanceStatus')) {
                document.getElementById('performanceStatus').textContent = '测试已停止';
                document.getElementById('performanceStatus').className = 'result-status status-error';
            }
        }

        // 批量测试
        async function runBatchTest() {
            const tests = [
                {name: 'stocks', enabled: document.getElementById('test_stocks')?.checked, url: '/api/v1/stocks?limit=5'},
                {name: 'news', enabled: document.getElementById('test_news')?.checked, url: '/api/v1/news?limit=3'},
                {name: 'sectors', enabled: document.getElementById('test_sectors')?.checked, url: '/api/v1/sectors/hot?limit=5'},
                {name: 'hot-topics', enabled: document.getElementById('test_analysis')?.checked, url: '/api/v1/news/hot-topics?limit=5'},
                {name: 'market', enabled: document.getElementById('test_market')?.checked, url: '/api/v1/market/overview'}
            ];

            const enabledTests = tests.filter(t => t.enabled);
            const resultDiv = document.getElementById('batchResult');
            const statusSpan = document.getElementById('batchStatus');

            statusSpan.textContent = '批量测试中...';
            statusSpan.className = 'result-status status-loading';

            let results = [];

            for (let i = 0; i < enabledTests.length; i++) {
                const test = enabledTests[i];
                resultDiv.innerHTML = `<p>正在测试: ${test.name} (${i+1}/${enabledTests.length})</p>`;

                try {
                    const startTime = Date.now();
                    const response = await fetch(`${API_BASE}${test.url}`);
                    const data = await response.json();
                    const endTime = Date.now();

                    results.push({
                        name: test.name,
                        success: true,
                        responseTime: endTime - startTime,
                        dataSize: JSON.stringify(data).length
                    });
                } catch (error) {
                    results.push({
                        name: test.name,
                        success: false,
                        error: error.message
                    });
                }
            }

            statusSpan.textContent = '批量测试完成';
            statusSpan.className = 'result-status status-success';

            const successCount = results.filter(r => r.success).length;
            const avgTime = results.filter(r => r.responseTime).reduce((sum, r) => sum + r.responseTime, 0) / results.length;

            resultDiv.innerHTML = `
                <div><strong>🔄 批量测试结果</strong></div>
                <div style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                    <p><strong>测试项目:</strong> ${enabledTests.length}</p>
                    <p><strong>成功项目:</strong> ${successCount}</p>
                    <p><strong>成功率:</strong> ${(successCount/enabledTests.length*100).toFixed(1)}%</p>
                    <p><strong>平均响应时间:</strong> ${avgTime.toFixed(2)}ms</p>
                </div>
                <div class="json-viewer">${formatJSON(results)}</div>
            `;
        }

        // 全功能测试
        async function runFullTest() {
            // 启用所有测试项目
            ['test_stocks', 'test_news', 'test_sectors', 'test_analysis', 'test_market', 'test_performance'].forEach(id => {
                const element = document.getElementById(id);
                if (element) element.checked = true;
            });

            await runBatchTest();
        }

        // 评分滑块事件监听（重复定义已删除）

        // 页面加载完成后初始化（合并所有初始化代码）
        document.addEventListener('DOMContentLoaded', function() {
            // 检查服务状态
            checkServerStatus();
            setInterval(checkServerStatus, 30000); // 每30秒检查一次服务状态

            // 初始化评分滑块
            ['completeness', 'accuracy', 'ux', 'overall'].forEach(type => {
                const slider = document.getElementById(`${type}Score`);
                const value = document.getElementById(`${type}Value`);
                if (slider && value) {
                    slider.addEventListener('input', function() {
                        value.textContent = this.value;
                    });
                }
            });

            // 定期更新统计
            setInterval(updateStats, 2000);
        });
    </script>
</body>
</html>
