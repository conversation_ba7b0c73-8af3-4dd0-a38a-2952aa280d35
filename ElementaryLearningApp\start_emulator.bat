@echo off
cd /d "%~dp0"

echo Setting up Android environment...
set ANDROID_AVD_HOME=F:\Android\avd
set ANDROID_SDK_ROOT=F:\Android
set ANDROID_HOME=F:\Android

echo Starting Android Emulator for ElementaryLearningApp...
echo Project Directory: %CD%

echo.
echo Available AVDs:
F:\Android\emulator\emulator.exe -list-avds

echo.
echo Starting Pixel_8 emulator...
start "ElementaryLearningApp Emulator" F:\Android\emulator\emulator.exe -avd Pixel_8

echo.
echo Waiting for emulator to start...
timeout /t 20 /nobreak

echo.
echo Checking connected devices:
F:\Android\platform-tools\adb.exe devices

echo.
echo You can now run your app with:
echo cd client
echo .\gradlew installDebug

pause