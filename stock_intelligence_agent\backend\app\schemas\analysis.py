"""
分析相关的 Pydantic 模型
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field
from uuid import UUID


class TechnicalAnalysisRequest(BaseModel):
    """技术分析请求模型"""
    stock_code: str = Field(..., description="股票代码", example="000001")
    period: str = Field("daily", description="周期", example="daily")
    indicators: Optional[List[str]] = Field(None, description="指标列表", example=["ma5", "ma20", "macd"])
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")


class TechnicalIndicators(BaseModel):
    """技术指标模型"""
    ma5: Optional[float] = Field(None, description="5日均线")
    ma10: Optional[float] = Field(None, description="10日均线")
    ma20: Optional[float] = Field(None, description="20日均线")
    ma60: Optional[float] = Field(None, description="60日均线")
    macd_dif: Optional[float] = Field(None, description="MACD DIF")
    macd_dea: Optional[float] = Field(None, description="MACD DEA")
    macd_histogram: Optional[float] = Field(None, description="MACD 柱状图")
    rsi6: Optional[float] = Field(None, description="6日RSI")
    rsi12: Optional[float] = Field(None, description="12日RSI")
    rsi24: Optional[float] = Field(None, description="24日RSI")
    kdj_k: Optional[float] = Field(None, description="KDJ K值")
    kdj_d: Optional[float] = Field(None, description="KDJ D值")
    kdj_j: Optional[float] = Field(None, description="KDJ J值")
    boll_upper: Optional[float] = Field(None, description="布林带上轨")
    boll_mid: Optional[float] = Field(None, description="布林带中轨")
    boll_lower: Optional[float] = Field(None, description="布林带下轨")
    volume_ma5: Optional[float] = Field(None, description="5日成交量均线")
    volume_ma10: Optional[float] = Field(None, description="10日成交量均线")


class TechnicalAnalysisResponse(BaseModel):
    """技术分析响应模型"""
    stock_id: str = Field(..., description="股票ID")
    calculation_date: datetime = Field(..., description="计算日期")
    data_points: int = Field(..., description="数据点数量")
    indicators: TechnicalIndicators = Field(..., description="技术指标")


class TrendSignal(BaseModel):
    """趋势信号模型"""
    signal_type: str = Field(..., description="信号类型", example="buy")
    signal_strength: str = Field(..., description="信号强度", example="strong")
    description: str = Field(..., description="信号描述", example="MACD金叉，买入信号")
    timestamp: datetime = Field(..., description="信号时间")


class TrendAnalysisResponse(BaseModel):
    """趋势分析响应模型"""
    trend_direction: str = Field(..., description="趋势方向", example="upward")
    trend_strength: str = Field(..., description="趋势强度", example="strong")
    support_level: Optional[float] = Field(None, description="支撑位")
    resistance_level: Optional[float] = Field(None, description="阻力位")
    signals: List[str] = Field(..., description="信号列表")
    confidence: Optional[float] = Field(None, description="置信度")


class ProfitabilityAnalysis(BaseModel):
    """盈利能力分析模型"""
    roe: Optional[float] = Field(None, description="净资产收益率")
    roe_rating: Optional[str] = Field(None, description="ROE评级")
    roa: Optional[float] = Field(None, description="总资产收益率")
    roa_rating: Optional[str] = Field(None, description="ROA评级")
    gross_margin: Optional[float] = Field(None, description="毛利率")
    gross_margin_rating: Optional[str] = Field(None, description="毛利率评级")
    net_margin: Optional[float] = Field(None, description="净利率")
    net_margin_rating: Optional[str] = Field(None, description="净利率评级")


class GrowthAnalysis(BaseModel):
    """成长能力分析模型"""
    revenue_growth: Optional[float] = Field(None, description="营收增长率")
    revenue_growth_rating: Optional[str] = Field(None, description="营收增长率评级")
    profit_growth: Optional[float] = Field(None, description="利润增长率")
    profit_growth_rating: Optional[str] = Field(None, description="利润增长率评级")


class SolvencyAnalysis(BaseModel):
    """偿债能力分析模型"""
    debt_ratio: Optional[float] = Field(None, description="资产负债率")
    debt_ratio_rating: Optional[str] = Field(None, description="资产负债率评级")
    current_ratio: Optional[float] = Field(None, description="流动比率")
    current_ratio_rating: Optional[str] = Field(None, description="流动比率评级")


class EfficiencyAnalysis(BaseModel):
    """营运能力分析模型"""
    asset_turnover: Optional[float] = Field(None, description="资产周转率")
    asset_turnover_rating: Optional[str] = Field(None, description="资产周转率评级")
    inventory_turnover: Optional[float] = Field(None, description="存货周转率")
    receivable_turnover: Optional[float] = Field(None, description="应收账款周转率")


class ValuationAnalysis(BaseModel):
    """估值分析模型"""
    eps: Optional[float] = Field(None, description="每股收益")
    bps: Optional[float] = Field(None, description="每股净资产")
    pe_ratio: Optional[float] = Field(None, description="市盈率")
    pb_ratio: Optional[float] = Field(None, description="市净率")
    peg_ratio: Optional[float] = Field(None, description="PEG比率")


class FundamentalAnalysisResponse(BaseModel):
    """基本面分析响应模型"""
    report_date: date = Field(..., description="报告期")
    profitability: ProfitabilityAnalysis = Field(..., description="盈利能力")
    growth: GrowthAnalysis = Field(..., description="成长能力")
    solvency: SolvencyAnalysis = Field(..., description="偿债能力")
    efficiency: EfficiencyAnalysis = Field(..., description="营运能力")
    valuation: ValuationAnalysis = Field(..., description="估值分析")
    overall_score: int = Field(..., description="综合评分", ge=0, le=100)
    rating: str = Field(..., description="评级", example="A")
    recommendations: Optional[List[str]] = Field(None, description="投资建议")


class RiskMetrics(BaseModel):
    """风险指标模型"""
    volatility: float = Field(..., description="年化波动率")
    max_drawdown: float = Field(..., description="最大回撤")
    var_95: float = Field(..., description="95% VaR")
    var_99: float = Field(..., description="99% VaR")
    sharpe_ratio: float = Field(..., description="夏普比率")
    beta: Optional[float] = Field(None, description="贝塔系数")
    tracking_error: Optional[float] = Field(None, description="跟踪误差")


class RiskAssessmentResponse(BaseModel):
    """风险评估响应模型"""
    risk_level: str = Field(..., description="风险等级", example="medium")
    risk_score: Optional[int] = Field(None, description="风险评分", ge=0, le=100)
    metrics: RiskMetrics = Field(..., description="风险指标")
    risk_factors: List[str] = Field(..., description="风险因素")
    recommendations: List[str] = Field(..., description="风险建议")
    assessment_date: datetime = Field(..., description="评估日期")


class PortfolioAnalysisRequest(BaseModel):
    """投资组合分析请求模型"""
    stock_codes: List[str] = Field(..., description="股票代码列表", min_items=1)
    weights: Optional[List[float]] = Field(None, description="权重列表")
    benchmark: Optional[str] = Field(None, description="基准指数")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")


class PortfolioMetrics(BaseModel):
    """投资组合指标模型"""
    total_return: float = Field(..., description="总收益率")
    annualized_return: float = Field(..., description="年化收益率")
    volatility: float = Field(..., description="波动率")
    sharpe_ratio: float = Field(..., description="夏普比率")
    max_drawdown: float = Field(..., description="最大回撤")
    calmar_ratio: float = Field(..., description="卡玛比率")
    sortino_ratio: float = Field(..., description="索提诺比率")
    information_ratio: Optional[float] = Field(None, description="信息比率")
    tracking_error: Optional[float] = Field(None, description="跟踪误差")


class PortfolioHolding(BaseModel):
    """投资组合持仓模型"""
    stock_code: str = Field(..., description="股票代码")
    stock_name: str = Field(..., description="股票名称")
    weight: float = Field(..., description="权重")
    market_value: float = Field(..., description="市值")
    return_contribution: float = Field(..., description="收益贡献")
    risk_contribution: float = Field(..., description="风险贡献")


class PortfolioAnalysisResponse(BaseModel):
    """投资组合分析响应模型"""
    portfolio_metrics: PortfolioMetrics = Field(..., description="组合指标")
    holdings: List[PortfolioHolding] = Field(..., description="持仓明细")
    sector_allocation: Dict[str, float] = Field(..., description="行业配置")
    risk_analysis: RiskAssessmentResponse = Field(..., description="风险分析")
    performance_attribution: Dict[str, Any] = Field(..., description="业绩归因")
    rebalancing_suggestions: List[str] = Field(..., description="再平衡建议")


class MarketSentimentIndicator(BaseModel):
    """市场情绪指标模型"""
    indicator_name: str = Field(..., description="指标名称")
    current_value: float = Field(..., description="当前值")
    historical_percentile: float = Field(..., description="历史分位数")
    signal: str = Field(..., description="信号", example="bullish")
    description: str = Field(..., description="指标描述")


class MarketSentimentResponse(BaseModel):
    """市场情绪响应模型"""
    overall_sentiment: str = Field(..., description="整体情绪", example="neutral")
    sentiment_score: int = Field(..., description="情绪评分", ge=0, le=100)
    indicators: List[MarketSentimentIndicator] = Field(..., description="情绪指标")
    market_phase: str = Field(..., description="市场阶段", example="consolidation")
    key_drivers: List[str] = Field(..., description="关键驱动因素")
    outlook: str = Field(..., description="市场展望")
    update_time: datetime = Field(..., description="更新时间")


class AnalysisTask(BaseModel):
    """分析任务模型"""
    task_id: str = Field(..., description="任务ID")
    task_type: str = Field(..., description="任务类型")
    status: str = Field(..., description="任务状态")
    progress: int = Field(..., description="进度百分比", ge=0, le=100)
    result: Optional[Dict[str, Any]] = Field(None, description="分析结果")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_at: datetime = Field(..., description="创建时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")


class AnalysisTaskResponse(BaseModel):
    """分析任务响应模型"""
    task: AnalysisTask = Field(..., description="任务信息")
    estimated_completion: Optional[datetime] = Field(None, description="预计完成时间")


class BatchAnalysisRequest(BaseModel):
    """批量分析请求模型"""
    stock_codes: List[str] = Field(..., description="股票代码列表", min_items=1, max_items=100)
    analysis_types: List[str] = Field(..., description="分析类型列表", example=["technical", "fundamental"])
    priority: str = Field("normal", description="优先级", example="high")
    callback_url: Optional[str] = Field(None, description="回调URL")


class BatchAnalysisResponse(BaseModel):
    """批量分析响应模型"""
    batch_id: str = Field(..., description="批次ID")
    total_tasks: int = Field(..., description="总任务数")
    submitted_tasks: int = Field(..., description="已提交任务数")
    estimated_completion: datetime = Field(..., description="预计完成时间")
    status_url: str = Field(..., description="状态查询URL")


class ComparisonRequest(BaseModel):
    """对比分析请求模型"""
    stock_codes: List[str] = Field(..., description="股票代码列表", min_items=2, max_items=10)
    comparison_type: str = Field(..., description="对比类型", example="fundamental")
    metrics: Optional[List[str]] = Field(None, description="对比指标")
    period: Optional[str] = Field("1y", description="对比周期")


class ComparisonMetric(BaseModel):
    """对比指标模型"""
    metric_name: str = Field(..., description="指标名称")
    values: Dict[str, float] = Field(..., description="各股票指标值")
    ranking: Dict[str, int] = Field(..., description="排名")
    best_performer: str = Field(..., description="最佳表现者")
    worst_performer: str = Field(..., description="最差表现者")


class ComparisonResponse(BaseModel):
    """对比分析响应模型"""
    comparison_type: str = Field(..., description="对比类型")
    stock_codes: List[str] = Field(..., description="股票代码列表")
    metrics: List[ComparisonMetric] = Field(..., description="对比指标")
    summary: Dict[str, Any] = Field(..., description="对比摘要")
    recommendations: List[str] = Field(..., description="投资建议")
    analysis_date: datetime = Field(..., description="分析日期")
