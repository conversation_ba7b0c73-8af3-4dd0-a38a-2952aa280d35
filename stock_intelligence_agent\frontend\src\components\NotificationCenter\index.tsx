/**
 * 通知中心组件
 */

import React, { useState, useEffect } from 'react';
import {
  Drawer,
  List,
  Typography,
  Button,
  Space,
  Badge,
  Empty,
  Tabs,
  Tag,
  Avatar,
  Divider,
} from 'antd';
import {
  BellOutlined,
  StockOutlined,
  FileTextOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  CheckOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

import { useAppSelector } from '@/store';
import wsService from '@/services/websocket';
import { AlertMessage, NewsDataMessage, WebSocketMessage } from '@/types';

const { Title, Text } = Typography;

interface Notification {
  id: string;
  type: 'alert' | 'news' | 'system';
  title: string;
  content: string;
  timestamp: string;
  read: boolean;
  data?: any;
}

interface NotificationCenterProps {
  visible: boolean;
  onClose: () => void;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({ visible, onClose }) => {
  // 状态
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [activeTab, setActiveTab] = useState('all');

  // 初始化通知监听
  useEffect(() => {
    // 监听预警消息
    const unsubscribeAlert = wsService.onAlert((message: AlertMessage) => {
      const notification: Notification = {
        id: `alert_${Date.now()}`,
        type: 'alert',
        title: `预警触发: ${message.data.alert_name}`,
        content: message.data.message,
        timestamp: message.data.trigger_time,
        read: false,
        data: message.data,
      };
      
      setNotifications(prev => [notification, ...prev]);
    });

    // 监听新闻消息
    const unsubscribeNews = wsService.onNewsData((message: NewsDataMessage) => {
      if (message.data.importance >= 4) { // 只推送重要新闻
        const notification: Notification = {
          id: `news_${message.data.id}`,
          type: 'news',
          title: `重要新闻: ${message.data.title}`,
          content: message.data.summary || '',
          timestamp: message.data.published_at,
          read: false,
          data: message.data,
        };
        
        setNotifications(prev => [notification, ...prev]);
      }
    });

    // 监听系统消息
    const unsubscribeMessage = wsService.onMessage((message: WebSocketMessage) => {
      if (message.type === 'system_notification') {
        const notification: Notification = {
          id: `system_${Date.now()}`,
          type: 'system',
          title: '系统通知',
          content: message.data?.message || '',
          timestamp: message.timestamp,
          read: false,
          data: message.data,
        };
        
        setNotifications(prev => [notification, ...prev]);
      }
    });

    return () => {
      unsubscribeAlert();
      unsubscribeNews();
      unsubscribeMessage();
    };
  }, []);

  // 获取图标
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'alert':
        return <WarningOutlined style={{ color: '#ff4d4f' }} />;
      case 'news':
        return <FileTextOutlined style={{ color: '#1890ff' }} />;
      case 'system':
        return <InfoCircleOutlined style={{ color: '#52c41a' }} />;
      default:
        return <BellOutlined />;
    }
  };

  // 获取类型标签
  const getTypeTag = (type: string) => {
    switch (type) {
      case 'alert':
        return <Tag color="red">预警</Tag>;
      case 'news':
        return <Tag color="blue">新闻</Tag>;
      case 'system':
        return <Tag color="green">系统</Tag>;
      default:
        return <Tag>通知</Tag>;
    }
  };

  // 标记为已读
  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  // 删除通知
  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  // 全部标记为已读
  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  // 清空所有通知
  const clearAll = () => {
    setNotifications([]);
  };

  // 过滤通知
  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 'all') return true;
    if (activeTab === 'unread') return !notification.read;
    return notification.type === activeTab;
  });

  // 未读数量
  const unreadCount = notifications.filter(n => !n.read).length;

  // 标签页配置
  const tabItems = [
    {
      key: 'all',
      label: `全部 (${notifications.length})`,
    },
    {
      key: 'unread',
      label: `未读 (${unreadCount})`,
    },
    {
      key: 'alert',
      label: '预警',
    },
    {
      key: 'news',
      label: '新闻',
    },
    {
      key: 'system',
      label: '系统',
    },
  ];

  return (
    <Drawer
      title={
        <div className="flex-between">
          <Space>
            <BellOutlined />
            <span>通知中心</span>
            {unreadCount > 0 && (
              <Badge count={unreadCount} size="small" />
            )}
          </Space>
          <Space>
            <Button
              type="text"
              size="small"
              onClick={markAllAsRead}
              disabled={unreadCount === 0}
            >
              全部已读
            </Button>
            <Button
              type="text"
              size="small"
              onClick={clearAll}
              disabled={notifications.length === 0}
            >
              清空
            </Button>
          </Space>
        </div>
      }
      placement="right"
      onClose={onClose}
      open={visible}
      width={400}
      bodyStyle={{ padding: 0 }}
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        style={{ padding: '0 16px' }}
      />

      <div style={{ height: 'calc(100vh - 120px)', overflow: 'auto' }}>
        {filteredNotifications.length > 0 ? (
          <List
            dataSource={filteredNotifications}
            renderItem={(notification) => (
              <List.Item
                style={{
                  padding: '12px 16px',
                  backgroundColor: notification.read ? 'transparent' : '#f6ffed',
                  borderLeft: notification.read ? 'none' : '3px solid #52c41a',
                }}
                actions={[
                  <Button
                    type="text"
                    size="small"
                    icon={<CheckOutlined />}
                    onClick={() => markAsRead(notification.id)}
                    disabled={notification.read}
                    title="标记为已读"
                  />,
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => deleteNotification(notification.id)}
                    title="删除"
                  />,
                ]}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar
                      size="small"
                      icon={getNotificationIcon(notification.type)}
                      style={{ backgroundColor: 'transparent' }}
                    />
                  }
                  title={
                    <div>
                      <Space>
                        {getTypeTag(notification.type)}
                        <Text
                          strong={!notification.read}
                          style={{
                            fontSize: 14,
                            opacity: notification.read ? 0.7 : 1,
                          }}
                        >
                          {notification.title}
                        </Text>
                      </Space>
                    </div>
                  }
                  description={
                    <div>
                      <Text
                        type="secondary"
                        style={{
                          fontSize: 12,
                          display: 'block',
                          marginBottom: 4,
                          opacity: notification.read ? 0.7 : 1,
                        }}
                      >
                        {notification.content}
                      </Text>
                      <Text
                        type="secondary"
                        style={{ fontSize: 11 }}
                      >
                        {formatDistanceToNow(new Date(notification.timestamp), {
                          addSuffix: true,
                          locale: zhCN,
                        })}
                      </Text>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        ) : (
          <div style={{ padding: 40 }}>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                activeTab === 'unread' ? '暂无未读通知' : '暂无通知'
              }
            />
          </div>
        )}
      </div>
    </Drawer>
  );
};

export default NotificationCenter;
