#!/usr/bin/env python3
"""
简单的研报API测试服务器
用于测试多数据源研报功能
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import uvicorn
import json

app = FastAPI(title="研报测试API", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模拟研报数据
MOCK_RESEARCH_DATA = {
    "300750": {
        "stock_code": "300750",
        "research_reports": [
            {
                "institution": "华泰证券",
                "analyst": "申建国",
                "rating": "买入",
                "target_price": 341.24,
                "publish_date": "2025-05-20",
                "report_title": "港股上市，看好全球市场估值修复",
                "key_points": ["维持预计公司25-27年归母净利润为666亿元、802亿元和926亿元", "目标价为341.24元"],
                "data_source": "同花顺"
            },
            {
                "institution": "平安证券",
                "analyst": "张之尧",
                "rating": "强烈推荐",
                "target_price": 0,
                "publish_date": "2025-05-09",
                "report_title": "一季度业绩表现出色，全球市场拓展顺利",
                "key_points": ["预计2025/2026/2027年公司归母净利润分别为662.56/797.29/923.57亿元", "维持强烈推荐评级"],
                "data_source": "同花顺"
            },
            {
                "institution": "群益证券(香港)有限公司",
                "analyst": "沈嘉婕",
                "rating": "增持",
                "target_price": 0,
                "publish_date": "2025-05-21",
                "report_title": "港股上市加速全球布局 积极推进重卡换电生态",
                "key_points": ["港股上市加速全球化布局", "重卡换电生态建设"],
                "data_source": "东方财富"
            },
            {
                "institution": "华创证券有限责任公司",
                "analyst": "黄麟/何家金",
                "rating": "强推",
                "target_price": 369.25,
                "publish_date": "2025-04-23",
                "report_title": "业绩符合预期 预计2025年将发力换电",
                "key_points": ["预计公司2025-2027归母净利润分别为650.31/785.43/930.79亿元", "目标价为369.25元"],
                "data_source": "新浪财经"
            },
            {
                "institution": "雪球用户观点",
                "analyst": "专业投资者",
                "rating": "看好",
                "target_price": 0,
                "publish_date": "2025-06-18",
                "report_title": "新能源汽车渗透率提升，宁德时代受益明显",
                "key_points": ["新能源汽车销量持续增长", "技术领先优势明显"],
                "data_source": "雪球"
            }
        ],
        "rating_summary": {
            "total_reports": 5,
            "rating_distribution": {
                "买入": 2,
                "强烈推荐": 1,
                "增持": 1,
                "强推": 1,
                "看好": 1
            },
            "avg_target_price": 355.25,
            "consensus_rating": "买入"
        },
        "analysis_time": datetime.now().isoformat(),
        "data_source": "同花顺等4个数据源",
        "source_details": {
            "successful_sources": ["同花顺", "东方财富", "新浪财经", "雪球"],
            "failed_sources": [],
            "total_sources_tried": 4
        }
    }
}

# 模拟数据源状态
MOCK_DATA_SOURCES = {
    "ths": {
        "name": "同花顺",
        "priority": 1,
        "reliability": 0.95,
        "failure_count": 0,
        "last_success": datetime.now().isoformat(),
        "update_frequency": "实时"
    },
    "eastmoney": {
        "name": "东方财富",
        "priority": 2,
        "reliability": 0.90,
        "failure_count": 0,
        "last_success": datetime.now().isoformat(),
        "update_frequency": "日更"
    },
    "sina": {
        "name": "新浪财经",
        "priority": 4,
        "reliability": 0.80,
        "failure_count": 1,
        "last_success": datetime.now().isoformat(),
        "update_frequency": "日更"
    },
    "xueqiu": {
        "name": "雪球",
        "priority": 3,
        "reliability": 0.85,
        "failure_count": 2,
        "last_success": datetime.now().isoformat(),
        "update_frequency": "实时"
    }
}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/api/research/reports/{stock_code}")
async def get_research_reports(stock_code: str, max_sources: int = 3):
    """获取股票研报数据"""
    if stock_code not in MOCK_RESEARCH_DATA:
        raise HTTPException(status_code=404, detail="股票代码未找到")
    
    data = MOCK_RESEARCH_DATA[stock_code].copy()
    
    # 根据max_sources限制返回的研报数量
    if max_sources < len(data["research_reports"]):
        data["research_reports"] = data["research_reports"][:max_sources]
        data["rating_summary"]["total_reports"] = len(data["research_reports"])
    
    return {
        "success": True,
        "message": "研报数据获取成功",
        "data": data
    }

@app.get("/api/research/sources/status")
async def get_data_sources_status():
    """获取数据源状态"""
    total_sources = len(MOCK_DATA_SOURCES)
    healthy_sources = sum(1 for s in MOCK_DATA_SOURCES.values() if s['failure_count'] < 3)
    health_score = (healthy_sources / total_sources) * 100 if total_sources > 0 else 0
    
    return {
        "success": True,
        "data": {
            "sources": MOCK_DATA_SOURCES,
            "summary": {
                "total_sources": total_sources,
                "healthy_sources": healthy_sources,
                "health_score": round(health_score, 2),
                "last_updated": datetime.now().isoformat()
            }
        }
    }

@app.post("/api/research/sources/refresh")
async def refresh_data_sources():
    """刷新数据源状态"""
    # 重置失败计数
    for source_id in MOCK_DATA_SOURCES:
        MOCK_DATA_SOURCES[source_id]['failure_count'] = 0
        MOCK_DATA_SOURCES[source_id]['last_success'] = datetime.now().isoformat()
    
    return {
        "success": True,
        "message": "数据源状态已刷新",
        "data": {
            "refresh_time": datetime.now().isoformat(),
            "sources_refreshed": list(MOCK_DATA_SOURCES.keys())
        }
    }

@app.get("/api/research/ratings/distribution/{stock_code}")
async def get_rating_distribution(stock_code: str, days: int = 90):
    """获取股票评级分布统计"""
    if stock_code not in MOCK_RESEARCH_DATA:
        raise HTTPException(status_code=404, detail="股票代码未找到")
    
    data = MOCK_RESEARCH_DATA[stock_code]
    reports = data["research_reports"]
    
    # 统计评级分布
    rating_stats = {}
    institution_stats = {}
    
    for report in reports:
        rating = report.get("rating", "未知")
        institution = report.get("institution", "未知")
        
        rating_stats[rating] = rating_stats.get(rating, 0) + 1
        institution_stats[institution] = institution_stats.get(institution, 0) + 1
    
    # 计算一致性评级
    total_reports = len(reports)
    positive_ratings = rating_stats.get("买入", 0) + rating_stats.get("增持", 0) + rating_stats.get("强烈推荐", 0) + rating_stats.get("强推", 0)
    consensus_score = (positive_ratings / total_reports) * 100 if total_reports > 0 else 0
    
    return {
        "success": True,
        "data": {
            "stock_code": stock_code,
            "period_days": days,
            "total_reports": total_reports,
            "rating_distribution": rating_stats,
            "institution_distribution": dict(sorted(institution_stats.items(), key=lambda x: x[1], reverse=True)),
            "consensus_analysis": {
                "positive_ratio": round(consensus_score, 2),
                "consensus_level": "强烈看好" if consensus_score >= 80 else "看好" if consensus_score >= 60 else "中性" if consensus_score >= 40 else "谨慎",
                "most_common_rating": max(rating_stats.items(), key=lambda x: x[1])[0] if rating_stats else "无数据"
            },
            "analysis_time": datetime.now().isoformat()
        }
    }

@app.get("/api/research/trending")
async def get_trending_research():
    """获取热门研报趋势"""
    trending_data = []
    
    for stock_code, data in MOCK_RESEARCH_DATA.items():
        recent_reports = data["research_reports"][:3]
        rating_summary = data.get("rating_summary", {})
        
        trending_data.append({
            "stock_code": stock_code,
            "recent_reports_count": len(recent_reports),
            "consensus_rating": rating_summary.get("consensus_rating", "中性"),
            "avg_target_price": rating_summary.get("avg_target_price", 0),
            "latest_report": recent_reports[0] if recent_reports else None,
            "data_sources": data.get("source_details", {}).get("successful_sources", [])
        })
    
    return {
        "success": True,
        "data": {
            "trending_stocks": trending_data,
            "update_time": datetime.now().isoformat(),
            "total_stocks": len(trending_data)
        }
    }

if __name__ == "__main__":
    print("🚀 启动研报测试API服务器...")
    print("📊 支持的功能:")
    print("  - 多数据源研报查询")
    print("  - 数据源状态监控")
    print("  - 评级分布统计")
    print("  - 热门研报趋势")
    print(f"🌐 服务地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    
    uvicorn.run(app, host="0.0.0.0", port=8001)
