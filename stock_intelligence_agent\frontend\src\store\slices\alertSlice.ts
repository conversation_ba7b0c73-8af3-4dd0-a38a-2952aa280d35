/**
 * 预警相关状态管理
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../index';
import { apiService } from '@/services/api';

// 类型定义
export interface AlertItem {
  id: string;
  stock_code: string;
  stock_name: string;
  alert_type: 'price' | 'change' | 'volume' | 'technical';
  condition: 'above' | 'below' | 'equal';
  target_value: number;
  current_value?: number;
  is_active: boolean;
  is_triggered: boolean;
  triggered_time?: string;
  created_time: string;
  message?: string;
}

export interface AlertState {
  // 预警列表
  alerts: AlertItem[];
  alertsLoading: boolean;
  alertsError: string | null;
  
  // 操作状态
  createLoading: boolean;
  updateLoading: boolean;
  deleteLoading: boolean;
  
  // 筛选条件
  filters: {
    status: 'all' | 'active' | 'triggered' | 'inactive';
    type: string;
    search: string;
  };
}

// 初始状态
const initialState: AlertState = {
  alerts: [],
  alertsLoading: false,
  alertsError: null,
  
  createLoading: false,
  updateLoading: false,
  deleteLoading: false,
  
  filters: {
    status: 'all',
    type: 'all',
    search: '',
  },
};

// 异步 actions
export const fetchAlertsAsync = createAsyncThunk(
  'alert/fetchAlerts',
  async () => {
    const response = await apiService.getAlerts();
    return response.data;
  }
);

export const createAlertAsync = createAsyncThunk(
  'alert/createAlert',
  async (alertData: Omit<AlertItem, 'id' | 'created_time' | 'is_triggered' | 'triggered_time'>) => {
    const response = await alertApi.createAlert(alertData);
    return response.data;
  }
);

export const updateAlertAsync = createAsyncThunk(
  'alert/updateAlert',
  async (params: { id: string; data: Partial<AlertItem> }) => {
    const response = await alertApi.updateAlert(params.id, params.data);
    return response.data;
  }
);

export const deleteAlertAsync = createAsyncThunk(
  'alert/deleteAlert',
  async (id: string) => {
    await alertApi.deleteAlert(id);
    return id;
  }
);

// Slice
const alertSlice = createSlice({
  name: 'alert',
  initialState,
  reducers: {
    // 设置筛选条件
    setFilters: (state, action: PayloadAction<Partial<AlertState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    // 清除错误
    clearErrors: (state) => {
      state.alertsError = null;
    },
    
    // 触发预警（WebSocket推送）
    triggerAlert: (state, action: PayloadAction<{ id: string; current_value: number; triggered_time: string }>) => {
      const { id, current_value, triggered_time } = action.payload;
      const alert = state.alerts.find(item => item.id === id);
      if (alert) {
        alert.is_triggered = true;
        alert.current_value = current_value;
        alert.triggered_time = triggered_time;
      }
    },
  },
  extraReducers: (builder) => {
    // 获取预警列表
    builder
      .addCase(fetchAlertsAsync.pending, (state) => {
        state.alertsLoading = true;
        state.alertsError = null;
      })
      .addCase(fetchAlertsAsync.fulfilled, (state, action) => {
        state.alertsLoading = false;
        state.alerts = action.payload;
      })
      .addCase(fetchAlertsAsync.rejected, (state, action) => {
        state.alertsLoading = false;
        state.alertsError = action.error.message || '获取预警失败';
      });
    
    // 创建预警
    builder
      .addCase(createAlertAsync.pending, (state) => {
        state.createLoading = true;
      })
      .addCase(createAlertAsync.fulfilled, (state, action) => {
        state.createLoading = false;
        state.alerts.unshift(action.payload);
      })
      .addCase(createAlertAsync.rejected, (state, action) => {
        state.createLoading = false;
        state.alertsError = action.error.message || '创建预警失败';
      });
    
    // 更新预警
    builder
      .addCase(updateAlertAsync.pending, (state) => {
        state.updateLoading = true;
      })
      .addCase(updateAlertAsync.fulfilled, (state, action) => {
        state.updateLoading = false;
        const index = state.alerts.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.alerts[index] = action.payload;
        }
      })
      .addCase(updateAlertAsync.rejected, (state, action) => {
        state.updateLoading = false;
        state.alertsError = action.error.message || '更新预警失败';
      });
    
    // 删除预警
    builder
      .addCase(deleteAlertAsync.pending, (state) => {
        state.deleteLoading = true;
      })
      .addCase(deleteAlertAsync.fulfilled, (state, action) => {
        state.deleteLoading = false;
        state.alerts = state.alerts.filter(item => item.id !== action.payload);
      })
      .addCase(deleteAlertAsync.rejected, (state, action) => {
        state.deleteLoading = false;
        state.alertsError = action.error.message || '删除预警失败';
      });
  },
});

// 导出 actions
export const { setFilters, clearErrors, triggerAlert } = alertSlice.actions;

// 选择器
export const selectAlerts = (state: RootState) => state.alert.alerts;
export const selectAlertsLoading = (state: RootState) => state.alert.alertsLoading;
export const selectAlertsError = (state: RootState) => state.alert.alertsError;

export const selectCreateLoading = (state: RootState) => state.alert.createLoading;
export const selectUpdateLoading = (state: RootState) => state.alert.updateLoading;
export const selectDeleteLoading = (state: RootState) => state.alert.deleteLoading;

export const selectAlertFilters = (state: RootState) => state.alert.filters;

// 派生选择器
export const selectFilteredAlerts = (state: RootState) => {
  const { alerts, filters } = state.alert;
  const { status, type, search } = filters;
  
  return alerts.filter(alert => {
    // 状态过滤
    if (status !== 'all') {
      if (status === 'active' && !alert.is_active) return false;
      if (status === 'triggered' && !alert.is_triggered) return false;
      if (status === 'inactive' && alert.is_active) return false;
    }
    
    // 类型过滤
    if (type !== 'all' && alert.alert_type !== type) return false;
    
    // 搜索过滤
    if (search) {
      const searchLower = search.toLowerCase();
      if (
        !alert.stock_code.toLowerCase().includes(searchLower) &&
        !alert.stock_name.toLowerCase().includes(searchLower)
      ) {
        return false;
      }
    }
    
    return true;
  });
};

export const selectActiveAlertsCount = (state: RootState) => {
  return state.alert.alerts.filter(alert => alert.is_active && !alert.is_triggered).length;
};

export const selectTriggeredAlertsCount = (state: RootState) => {
  return state.alert.alerts.filter(alert => alert.is_triggered).length;
};

export default alertSlice.reducer;
