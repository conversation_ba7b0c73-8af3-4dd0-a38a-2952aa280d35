/**
 * 登录页面
 */

import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  Space,
  Divider,
  Checkbox,
  Alert,
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  StockOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '@/store';
import { loginAsync, clearError } from '@/store/slices/authSlice';
import { LoginRequest } from '@/types';

const { Title, Text } = Typography;

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  // 状态
  const { isLoading, error } = useAppSelector(state => state.auth);
  const [form] = Form.useForm();

  // 处理登录
  const handleLogin = async (values: LoginRequest) => {
    try {
      await dispatch(loginAsync(values)).unwrap();
      navigate('/', { replace: true });
    } catch (error) {
      // 错误已在 slice 中处理
    }
  };

  // 清除错误
  const handleClearError = () => {
    dispatch(clearError());
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
      }}
    >
      <Card
        style={{
          width: '100%',
          maxWidth: 400,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: 12,
        }}
        bodyStyle={{ padding: '40px 32px' }}
      >
        {/* 头部 */}
        <div className="text-center mb-4">
          <Space direction="vertical" size="small">
            <StockOutlined style={{ fontSize: 48, color: '#1890ff' }} />
            <Title level={2} style={{ margin: 0, color: '#262626' }}>
              股票资讯智能体
            </Title>
            <Text type="secondary">
              专业的股票分析和资讯平台
            </Text>
          </Space>
        </div>

        <Divider />

        {/* 错误提示 */}
        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            closable
            onClose={handleClearError}
            style={{ marginBottom: 24 }}
          />
        )}

        {/* 登录表单 */}
        <Form
          form={form}
          name="login"
          onFinish={handleLogin}
          autoComplete="off"
          size="large"
          initialValues={{
            remember_me: true,
          }}
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名或邮箱' },
              { min: 3, message: '用户名至少3个字符' },
            ]}
          >
            <Input
              prefix={<UserOutlined style={{ color: '#bfbfbf' }} />}
              placeholder="用户名或邮箱"
              autoComplete="username"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined style={{ color: '#bfbfbf' }} />}
              placeholder="密码"
              autoComplete="current-password"
              iconRender={(visible) =>
                visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
              }
            />
          </Form.Item>

          <Form.Item>
            <div className="flex-between">
              <Form.Item name="remember_me" valuePropName="checked" noStyle>
                <Checkbox>记住我</Checkbox>
              </Form.Item>
              <Link to="/forgot-password" style={{ color: '#1890ff' }}>
                忘记密码？
              </Link>
            </div>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={isLoading}
              block
              style={{
                height: 48,
                fontSize: 16,
                fontWeight: 500,
              }}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <Divider>
          <Text type="secondary">还没有账号？</Text>
        </Divider>

        {/* 注册链接 */}
        <div className="text-center">
          <Link to="/register">
            <Button type="link" size="large">
              立即注册
            </Button>
          </Link>
        </div>

        {/* 演示账号 */}
        <div className="text-center mt-3">
          <Text type="secondary" style={{ fontSize: 12 }}>
            演示账号: demo / 123456
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default LoginPage;
