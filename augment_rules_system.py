#!/usr/bin/env python3
"""
Augment Rules System - 类似 Cursor RULES 的功能
为 Augment 提供项目级别的规则和约定管理

作者: Augment Agent
日期: 2025-06-17
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime


class AugmentRulesSystem:
    """Augment 规则系统"""

    def __init__(self, project_root: Optional[str] = None):
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.rules_file = self.project_root / ".augment" / "rules.json"
        self.rules_md_file = self.project_root / ".augment" / "RULES.md"
        self.global_rules_file = Path.home() / ".augment" / "global_rules.json"

        # 确保目录存在
        self.rules_file.parent.mkdir(parents=True, exist_ok=True)
        self.global_rules_file.parent.mkdir(parents=True, exist_ok=True)

    def create_default_rules(self) -> Dict[str, Any]:
        """创建默认规则配置"""
        return {
            "version": "1.0",
            "created": datetime.now().isoformat(),
            "project_info": {
                "name": self.project_root.name,
                "description": "项目规则和约定",
                "type": "general"
            },
            "coding_standards": {
                "language_preferences": {
                    "python": {
                        "style_guide": "PEP 8",
                        "formatter": "black",
                        "linter": "ruff",
                        "type_checker": "mypy",
                        "docstring_style": "Google"
                    },
                    "javascript": {
                        "style_guide": "Airbnb",
                        "formatter": "prettier",
                        "linter": "eslint"
                    }
                },
                "general_principles": [
                    "遵循单一职责原则",
                    "使用有意义的变量和函数名",
                    "编写清晰的注释和文档",
                    "优先使用组合而不是继承",
                    "实现适当的错误处理"
                ]
            },
            "development_workflow": {
                "git_conventions": {
                    "commit_format": "type(scope): description",
                    "branch_naming": "feature/description, fix/description, hotfix/description",
                    "merge_strategy": "squash"
                },
                "testing_requirements": {
                    "unit_tests": "required",
                    "coverage_threshold": 80,
                    "test_framework": "pytest"
                },
                "code_review": {
                    "required": True,
                    "min_reviewers": 1,
                    "auto_merge": False
                }
            },
            "ai_assistant_rules": {
                "interaction_style": "professional_friendly",
                "feedback_frequency": "after_major_changes",
                "explanation_level": "detailed",
                "code_generation": {
                    "include_comments": True,
                    "include_type_hints": True,
                    "include_docstrings": True,
                    "follow_project_structure": True
                },
                "mcp_feedback_enhanced": {
                    "auto_approve": ["interactive_feedback"],
                    "use_web_ui": True,
                    "enable_image_support": True,
                    "session_management": True
                }
            },
            "project_structure": {
                "source_directories": ["src", "lib"],
                "test_directories": ["tests", "test"],
                "documentation_directories": ["docs", "documentation"],
                "configuration_files": [".augment", "pyproject.toml", "package.json"]
            },
            "dependencies": {
                "package_manager": "uv",  # Python 项目优先使用 uv
                "update_strategy": "conservative",
                "security_scanning": True
            },
            "custom_rules": []
        }

    def load_rules(self) -> Dict[str, Any]:
        """加载规则配置"""
        if self.rules_file.exists():
            try:
                with open(self.rules_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载规则文件失败: {e}")
                return self.create_default_rules()
        else:
            return self.create_default_rules()

    def save_rules(self, rules: Dict[str, Any]) -> bool:
        """保存规则配置"""
        try:
            rules["updated"] = datetime.now().isoformat()

            with open(self.rules_file, 'w', encoding='utf-8') as f:
                json.dump(rules, f, indent=2, ensure_ascii=False)

            # 同时生成 Markdown 版本
            self.generate_rules_markdown(rules)
            return True

        except Exception as e:
            print(f"保存规则文件失败: {e}")
            return False

    def generate_rules_markdown(self, rules: Dict[str, Any]):
        """生成 Markdown 格式的规则文档"""
        md_content = f"""# {rules['project_info']['name']} - 项目规则

> 自动生成于: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📋 项目信息

- **项目名称**: {rules['project_info']['name']}
- **项目描述**: {rules['project_info']['description']}
- **项目类型**: {rules['project_info']['type']}

## 💻 编码标准

### 语言偏好

"""

        # 添加语言偏好
        for lang, prefs in rules['coding_standards']['language_preferences'].items():
            md_content += f"#### {lang.title()}\n"
            for key, value in prefs.items():
                md_content += f"- **{key.replace('_', ' ').title()}**: {value}\n"
            md_content += "\n"

        # 添加通用原则
        md_content += "### 通用原则\n\n"
        for principle in rules['coding_standards']['general_principles']:
            md_content += f"- {principle}\n"

        # 添加开发工作流
        md_content += f"""

## 🔄 开发工作流

### Git 约定
- **提交格式**: {rules['development_workflow']['git_conventions']['commit_format']}
- **分支命名**: {rules['development_workflow']['git_conventions']['branch_naming']}
- **合并策略**: {rules['development_workflow']['git_conventions']['merge_strategy']}

### 测试要求
- **单元测试**: {rules['development_workflow']['testing_requirements']['unit_tests']}
- **覆盖率阈值**: {rules['development_workflow']['testing_requirements']['coverage_threshold']}%
- **测试框架**: {rules['development_workflow']['testing_requirements']['test_framework']}

## 🤖 AI 助手规则

### 交互风格
- **风格**: {rules['ai_assistant_rules']['interaction_style']}
- **反馈频率**: {rules['ai_assistant_rules']['feedback_frequency']}
- **解释级别**: {rules['ai_assistant_rules']['explanation_level']}

### 代码生成要求
- **包含注释**: {rules['ai_assistant_rules']['code_generation']['include_comments']}
- **包含类型提示**: {rules['ai_assistant_rules']['code_generation']['include_type_hints']}
- **包含文档字符串**: {rules['ai_assistant_rules']['code_generation']['include_docstrings']}
- **遵循项目结构**: {rules['ai_assistant_rules']['code_generation']['follow_project_structure']}

### MCP Interactive Feedback Rules (严格执行)
"""

        # 添加 MCP Interactive Feedback Rules
        if 'mcp_interactive_feedback_rules' in rules['ai_assistant_rules']:
            mcp_rules = rules['ai_assistant_rules']['mcp_interactive_feedback_rules']
            for i, rule in enumerate(mcp_rules['rules'], 1):
                md_content += f"{i}. {rule}\n"

            md_content += f"""
- **执行级别**: {mcp_rules['enforcement_level']}
- **例外情况**: {', '.join(mcp_rules['exceptions'])}

### MCP Feedback Enhanced 配置
- **自动批准**: {rules['ai_assistant_rules']['mcp_feedback_enhanced']['auto_approve']}
- **使用 Web UI**: {rules['ai_assistant_rules']['mcp_feedback_enhanced']['use_web_ui']}
- **启用图片支持**: {rules['ai_assistant_rules']['mcp_feedback_enhanced']['enable_image_support']}

## 📁 项目结构

- **源码目录**: {', '.join(rules['project_structure']['source_directories'])}
- **测试目录**: {', '.join(rules['project_structure']['test_directories'])}
- **文档目录**: {', '.join(rules['project_structure']['documentation_directories'])}

## 📦 依赖管理

- **包管理器**: {rules['dependencies']['package_manager']}
- **更新策略**: {rules['dependencies']['update_strategy']}
- **安全扫描**: {rules['dependencies']['security_scanning']}

---

*此文档由 Augment Rules System 自动生成*
"""

        # 保存 Markdown 文件
        with open(self.rules_md_file, 'w', encoding='utf-8') as f:
            f.write(md_content)

    def add_custom_rule(self, category: str, rule: str) -> bool:
        """添加自定义规则"""
        rules = self.load_rules()

        if 'custom_rules' not in rules:
            rules['custom_rules'] = []

        custom_rule = {
            "category": category,
            "rule": rule,
            "added": datetime.now().isoformat()
        }

        rules['custom_rules'].append(custom_rule)
        return self.save_rules(rules)

    def get_ai_prompt_rules(self) -> str:
        """获取适用于 AI 助手的提示词规则"""
        rules = self.load_rules()

        prompt = f"""# {rules['project_info']['name']} 项目规则

## 编码标准
"""

        # 添加语言偏好
        for lang, prefs in rules['coding_standards']['language_preferences'].items():
            prompt += f"### {lang.title()}\n"
            for key, value in prefs.items():
                prompt += f"- {key.replace('_', ' ')}: {value}\n"

        # 添加通用原则
        prompt += "\n## 通用原则\n"
        for principle in rules['coding_standards']['general_principles']:
            prompt += f"- {principle}\n"

        # 添加 AI 助手规则
        ai_rules = rules['ai_assistant_rules']
        prompt += f"""
## AI 助手行为规则
- 交互风格: {ai_rules['interaction_style']}
- 反馈频率: {ai_rules['feedback_frequency']}
- 解释级别: {ai_rules['explanation_level']}

### 代码生成要求
- 包含注释: {ai_rules['code_generation']['include_comments']}
- 包含类型提示: {ai_rules['code_generation']['include_type_hints']}
- 包含文档字符串: {ai_rules['code_generation']['include_docstrings']}
- 遵循项目结构: {ai_rules['code_generation']['follow_project_structure']}

### MCP Interactive Feedback Rules (严格执行)
"""

        # 添加 MCP Interactive Feedback Rules
        if 'mcp_interactive_feedback_rules' in ai_rules:
            mcp_rules = ai_rules['mcp_interactive_feedback_rules']
            for i, rule in enumerate(mcp_rules['rules'], 1):
                prompt += f"{i}. {rule}\n"

            prompt += f"""
执行级别: {mcp_rules['enforcement_level']}
例外情况: {', '.join(mcp_rules['exceptions'])}
"""

        prompt += """
### MCP Feedback Enhanced 使用
- 使用 Web UI 模式
- 启用图片支持功能
- 在重要节点请求用户反馈
"""

        # 添加自定义规则
        if rules.get('custom_rules'):
            prompt += "\n## 自定义规则\n"
            for rule in rules['custom_rules']:
                prompt += f"- [{rule['category']}] {rule['rule']}\n"

        return prompt

    def initialize_project(self) -> bool:
        """初始化项目规则"""
        print(f"🚀 初始化 Augment Rules System for {self.project_root.name}")

        rules = self.create_default_rules()

        if self.save_rules(rules):
            print(f"✅ 规则文件已创建: {self.rules_file}")
            print(f"✅ Markdown 文档已创建: {self.rules_md_file}")
            return True
        else:
            print("❌ 初始化失败")
            return False

    def show_current_rules(self):
        """显示当前规则"""
        rules = self.load_rules()

        print(f"\n📋 {rules['project_info']['name']} 项目规则")
        print("=" * 50)

        print("\n🤖 AI 助手提示词:")
        print(self.get_ai_prompt_rules())


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="Augment Rules System")
    parser.add_argument("--init", action="store_true", help="初始化项目规则")
    parser.add_argument("--show", action="store_true", help="显示当前规则")
    parser.add_argument("--prompt", action="store_true", help="生成 AI 提示词")
    parser.add_argument("--project", type=str, help="项目目录路径")

    args = parser.parse_args()

    rules_system = AugmentRulesSystem(args.project)

    if args.init:
        rules_system.initialize_project()
    elif args.show:
        rules_system.show_current_rules()
    elif args.prompt:
        print(rules_system.get_ai_prompt_rules())
    else:
        print("使用 --help 查看可用选项")


if __name__ == "__main__":
    main()
