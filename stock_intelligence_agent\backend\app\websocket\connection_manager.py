"""
WebSocket 连接管理器
管理所有 WebSocket 连接和消息推送
"""

import json
import logging
import asyncio
from datetime import datetime
from typing import Dict, List, Set, Optional, Any
from fastapi import WebSocket, WebSocketDisconnect
from uuid import uuid4, UUID
import redis.asyncio as redis

from app.core.config import settings

logger = logging.getLogger(__name__)


class ConnectionManager:
    """WebSocket 连接管理器"""
    
    def __init__(self):
        # 活跃连接字典 {connection_id: WebSocket}
        self.active_connections: Dict[str, WebSocket] = {}
        
        # 用户连接映射 {user_id: Set[connection_id]}
        self.user_connections: Dict[str, Set[str]] = {}
        
        # 股票订阅映射 {stock_code: Set[connection_id]}
        self.stock_subscriptions: Dict[str, Set[str]] = {}
        
        # 新闻订阅映射 {category: Set[connection_id]}
        self.news_subscriptions: Dict[str, Set[str]] = {}
        
        # 连接元数据 {connection_id: metadata}
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        
        # Redis 客户端
        self.redis: Optional[redis.Redis] = None
        
        # 心跳任务
        self.heartbeat_task: Optional[asyncio.Task] = None
        
        logger.info("WebSocket 连接管理器已初始化")
    
    async def set_redis(self, redis_client: redis.Redis):
        """设置 Redis 客户端"""
        self.redis = redis_client
        logger.info("Redis 客户端已设置")
    
    async def connect(
        self,
        websocket: WebSocket,
        user_id: Optional[str] = None,
        client_info: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        接受新的 WebSocket 连接
        
        Args:
            websocket: WebSocket 连接
            user_id: 用户ID
            client_info: 客户端信息
            
        Returns:
            连接ID
        """
        try:
            await websocket.accept()
            
            # 生成连接ID
            connection_id = str(uuid4())
            
            # 存储连接
            self.active_connections[connection_id] = websocket
            
            # 存储连接元数据
            self.connection_metadata[connection_id] = {
                "user_id": user_id,
                "connected_at": datetime.utcnow(),
                "last_heartbeat": datetime.utcnow(),
                "client_info": client_info or {},
                "subscriptions": {
                    "stocks": set(),
                    "news": set(),
                    "alerts": set()
                }
            }
            
            # 用户连接映射
            if user_id:
                if user_id not in self.user_connections:
                    self.user_connections[user_id] = set()
                self.user_connections[user_id].add(connection_id)
            
            # 启动心跳检测
            if not self.heartbeat_task:
                self.heartbeat_task = asyncio.create_task(self._heartbeat_checker())
            
            logger.info(f"新连接已建立: {connection_id}, 用户: {user_id}")
            
            # 发送连接确认消息
            await self.send_personal_message(connection_id, {
                "type": "connection_established",
                "connection_id": connection_id,
                "timestamp": datetime.utcnow().isoformat(),
                "server_time": datetime.utcnow().isoformat()
            })
            
            return connection_id
            
        except Exception as e:
            logger.error(f"建立 WebSocket 连接失败: {e}")
            raise
    
    async def disconnect(self, connection_id: str):
        """
        断开 WebSocket 连接
        
        Args:
            connection_id: 连接ID
        """
        try:
            if connection_id in self.active_connections:
                # 获取连接元数据
                metadata = self.connection_metadata.get(connection_id, {})
                user_id = metadata.get("user_id")
                
                # 清理股票订阅
                subscriptions = metadata.get("subscriptions", {})
                for stock_code in subscriptions.get("stocks", set()):
                    await self._unsubscribe_stock(connection_id, stock_code)
                
                # 清理新闻订阅
                for category in subscriptions.get("news", set()):
                    await self._unsubscribe_news(connection_id, category)
                
                # 清理用户连接映射
                if user_id and user_id in self.user_connections:
                    self.user_connections[user_id].discard(connection_id)
                    if not self.user_connections[user_id]:
                        del self.user_connections[user_id]
                
                # 删除连接
                del self.active_connections[connection_id]
                del self.connection_metadata[connection_id]
                
                logger.info(f"连接已断开: {connection_id}, 用户: {user_id}")
                
        except Exception as e:
            logger.error(f"断开连接失败: {e}")
    
    async def send_personal_message(self, connection_id: str, message: Dict[str, Any]):
        """
        发送个人消息
        
        Args:
            connection_id: 连接ID
            message: 消息内容
        """
        try:
            if connection_id in self.active_connections:
                websocket = self.active_connections[connection_id]
                await websocket.send_text(json.dumps(message, default=str))
                
        except WebSocketDisconnect:
            logger.warning(f"连接已断开，无法发送消息: {connection_id}")
            await self.disconnect(connection_id)
        except Exception as e:
            logger.error(f"发送个人消息失败: {e}")
    
    async def send_user_message(self, user_id: str, message: Dict[str, Any]):
        """
        发送用户消息（所有连接）
        
        Args:
            user_id: 用户ID
            message: 消息内容
        """
        try:
            if user_id in self.user_connections:
                connection_ids = list(self.user_connections[user_id])
                for connection_id in connection_ids:
                    await self.send_personal_message(connection_id, message)
                    
        except Exception as e:
            logger.error(f"发送用户消息失败: {e}")
    
    async def broadcast_message(self, message: Dict[str, Any]):
        """
        广播消息给所有连接
        
        Args:
            message: 消息内容
        """
        try:
            connection_ids = list(self.active_connections.keys())
            for connection_id in connection_ids:
                await self.send_personal_message(connection_id, message)
                
        except Exception as e:
            logger.error(f"广播消息失败: {e}")
    
    async def subscribe_stock(self, connection_id: str, stock_code: str):
        """
        订阅股票实时数据
        
        Args:
            connection_id: 连接ID
            stock_code: 股票代码
        """
        try:
            if connection_id not in self.active_connections:
                return
            
            # 添加到股票订阅
            if stock_code not in self.stock_subscriptions:
                self.stock_subscriptions[stock_code] = set()
            self.stock_subscriptions[stock_code].add(connection_id)
            
            # 更新连接元数据
            if connection_id in self.connection_metadata:
                self.connection_metadata[connection_id]["subscriptions"]["stocks"].add(stock_code)
            
            logger.info(f"连接 {connection_id} 订阅股票 {stock_code}")
            
            # 发送订阅确认
            await self.send_personal_message(connection_id, {
                "type": "subscription_confirmed",
                "data_type": "stock",
                "stock_code": stock_code,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            # 发送最新数据
            await self._send_latest_stock_data(connection_id, stock_code)
            
        except Exception as e:
            logger.error(f"订阅股票失败: {e}")
    
    async def unsubscribe_stock(self, connection_id: str, stock_code: str):
        """
        取消订阅股票
        
        Args:
            connection_id: 连接ID
            stock_code: 股票代码
        """
        await self._unsubscribe_stock(connection_id, stock_code)
        
        # 发送取消订阅确认
        await self.send_personal_message(connection_id, {
            "type": "unsubscription_confirmed",
            "data_type": "stock",
            "stock_code": stock_code,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    async def _unsubscribe_stock(self, connection_id: str, stock_code: str):
        """内部取消订阅股票方法"""
        try:
            if stock_code in self.stock_subscriptions:
                self.stock_subscriptions[stock_code].discard(connection_id)
                if not self.stock_subscriptions[stock_code]:
                    del self.stock_subscriptions[stock_code]
            
            # 更新连接元数据
            if connection_id in self.connection_metadata:
                self.connection_metadata[connection_id]["subscriptions"]["stocks"].discard(stock_code)
            
            logger.info(f"连接 {connection_id} 取消订阅股票 {stock_code}")
            
        except Exception as e:
            logger.error(f"取消订阅股票失败: {e}")
    
    async def subscribe_news(self, connection_id: str, category: str):
        """
        订阅新闻推送
        
        Args:
            connection_id: 连接ID
            category: 新闻分类
        """
        try:
            if connection_id not in self.active_connections:
                return
            
            # 添加到新闻订阅
            if category not in self.news_subscriptions:
                self.news_subscriptions[category] = set()
            self.news_subscriptions[category].add(connection_id)
            
            # 更新连接元数据
            if connection_id in self.connection_metadata:
                self.connection_metadata[connection_id]["subscriptions"]["news"].add(category)
            
            logger.info(f"连接 {connection_id} 订阅新闻分类 {category}")
            
            # 发送订阅确认
            await self.send_personal_message(connection_id, {
                "type": "subscription_confirmed",
                "data_type": "news",
                "category": category,
                "timestamp": datetime.utcnow().isoformat()
            })
            
        except Exception as e:
            logger.error(f"订阅新闻失败: {e}")
    
    async def unsubscribe_news(self, connection_id: str, category: str):
        """
        取消订阅新闻
        
        Args:
            connection_id: 连接ID
            category: 新闻分类
        """
        await self._unsubscribe_news(connection_id, category)
        
        # 发送取消订阅确认
        await self.send_personal_message(connection_id, {
            "type": "unsubscription_confirmed",
            "data_type": "news",
            "category": category,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    async def _unsubscribe_news(self, connection_id: str, category: str):
        """内部取消订阅新闻方法"""
        try:
            if category in self.news_subscriptions:
                self.news_subscriptions[category].discard(connection_id)
                if not self.news_subscriptions[category]:
                    del self.news_subscriptions[category]
            
            # 更新连接元数据
            if connection_id in self.connection_metadata:
                self.connection_metadata[connection_id]["subscriptions"]["news"].discard(category)
            
            logger.info(f"连接 {connection_id} 取消订阅新闻分类 {category}")
            
        except Exception as e:
            logger.error(f"取消订阅新闻失败: {e}")
    
    async def push_stock_data(self, stock_code: str, data: Dict[str, Any]):
        """
        推送股票实时数据
        
        Args:
            stock_code: 股票代码
            data: 股票数据
        """
        try:
            if stock_code in self.stock_subscriptions:
                message = {
                    "type": "stock_data",
                    "stock_code": stock_code,
                    "data": data,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                connection_ids = list(self.stock_subscriptions[stock_code])
                for connection_id in connection_ids:
                    await self.send_personal_message(connection_id, message)
                    
        except Exception as e:
            logger.error(f"推送股票数据失败: {e}")
    
    async def push_news_data(self, category: str, news_data: Dict[str, Any]):
        """
        推送新闻数据
        
        Args:
            category: 新闻分类
            news_data: 新闻数据
        """
        try:
            if category in self.news_subscriptions:
                message = {
                    "type": "news_data",
                    "category": category,
                    "data": news_data,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                connection_ids = list(self.news_subscriptions[category])
                for connection_id in connection_ids:
                    await self.send_personal_message(connection_id, message)
                    
        except Exception as e:
            logger.error(f"推送新闻数据失败: {e}")
    
    async def push_alert(self, user_id: str, alert_data: Dict[str, Any]):
        """
        推送用户预警
        
        Args:
            user_id: 用户ID
            alert_data: 预警数据
        """
        try:
            message = {
                "type": "alert",
                "data": alert_data,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            await self.send_user_message(user_id, message)
            
        except Exception as e:
            logger.error(f"推送预警失败: {e}")
    
    async def _send_latest_stock_data(self, connection_id: str, stock_code: str):
        """发送最新股票数据"""
        try:
            if self.redis:
                # 从 Redis 获取最新数据
                cache_key = f"realtime_quote:{stock_code}"
                cached_data = await self.redis.hgetall(cache_key)
                
                if cached_data:
                    data = {
                        "current_price": cached_data.get("current_price"),
                        "change": cached_data.get("change"),
                        "pct_change": cached_data.get("pct_change"),
                        "volume": cached_data.get("volume"),
                        "amount": cached_data.get("amount"),
                        "timestamp": cached_data.get("timestamp")
                    }
                    
                    await self.push_stock_data(stock_code, data)
                    
        except Exception as e:
            logger.error(f"发送最新股票数据失败: {e}")
    
    async def _heartbeat_checker(self):
        """心跳检测任务"""
        while True:
            try:
                await asyncio.sleep(settings.WEBSOCKET_HEARTBEAT_INTERVAL)
                
                current_time = datetime.utcnow()
                expired_connections = []
                
                for connection_id, metadata in self.connection_metadata.items():
                    last_heartbeat = metadata.get("last_heartbeat")
                    if last_heartbeat:
                        time_diff = (current_time - last_heartbeat).total_seconds()
                        if time_diff > settings.WEBSOCKET_HEARTBEAT_INTERVAL * 2:
                            expired_connections.append(connection_id)
                
                # 清理过期连接
                for connection_id in expired_connections:
                    logger.warning(f"连接心跳超时，断开连接: {connection_id}")
                    await self.disconnect(connection_id)
                
            except Exception as e:
                logger.error(f"心跳检测失败: {e}")
    
    async def update_heartbeat(self, connection_id: str):
        """更新连接心跳时间"""
        if connection_id in self.connection_metadata:
            self.connection_metadata[connection_id]["last_heartbeat"] = datetime.utcnow()
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return {
            "total_connections": len(self.active_connections),
            "user_connections": len(self.user_connections),
            "stock_subscriptions": len(self.stock_subscriptions),
            "news_subscriptions": len(self.news_subscriptions),
            "active_stocks": list(self.stock_subscriptions.keys()),
            "active_news_categories": list(self.news_subscriptions.keys())
        }


# 全局连接管理器实例
connection_manager = ConnectionManager()
