"""
新闻资讯相关的 Pydantic 模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, HttpUrl
from uuid import UUID


class NewsBase(BaseModel):
    """新闻基础模型"""
    title: str = Field(..., description="新闻标题", max_length=200)
    content: str = Field(..., description="新闻内容")
    summary: Optional[str] = Field(None, description="新闻摘要", max_length=500)
    source: str = Field(..., description="新闻来源", max_length=100)
    author: Optional[str] = Field(None, description="作者", max_length=100)
    url: Optional[HttpUrl] = Field(None, description="原文链接")
    published_at: datetime = Field(..., description="发布时间")


class NewsCreate(NewsBase):
    """创建新闻模型"""
    category_id: Optional[UUID] = Field(None, description="分类ID")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    related_stocks: Optional[List[str]] = Field(None, description="相关股票代码")
    importance: int = Field(1, description="重要性等级", ge=1, le=5)


class NewsUpdate(BaseModel):
    """更新新闻模型"""
    title: Optional[str] = Field(None, description="新闻标题", max_length=200)
    content: Optional[str] = Field(None, description="新闻内容")
    summary: Optional[str] = Field(None, description="新闻摘要", max_length=500)
    category_id: Optional[UUID] = Field(None, description="分类ID")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    related_stocks: Optional[List[str]] = Field(None, description="相关股票代码")
    importance: Optional[int] = Field(None, description="重要性等级", ge=1, le=5)
    is_active: Optional[bool] = Field(None, description="是否活跃")


class NewsResponse(NewsBase):
    """新闻响应模型"""
    id: UUID = Field(..., description="新闻ID")
    category_id: Optional[UUID] = Field(None, description="分类ID")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    related_stocks: List[str] = Field(default_factory=list, description="相关股票代码")
    importance: int = Field(..., description="重要性等级")
    view_count: int = Field(0, description="浏览次数")
    like_count: int = Field(0, description="点赞次数")
    comment_count: int = Field(0, description="评论次数")
    sentiment_score: Optional[float] = Field(None, description="情感分数")
    sentiment_label: Optional[str] = Field(None, description="情感标签")
    is_active: bool = Field(True, description="是否活跃")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class NewsListResponse(BaseModel):
    """新闻列表响应模型"""
    news: List[NewsResponse] = Field(..., description="新闻列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过数量")
    limit: int = Field(..., description="限制数量")
    has_more: bool = Field(..., description="是否有更多")


class NewsCategoryBase(BaseModel):
    """新闻分类基础模型"""
    name: str = Field(..., description="分类名称", max_length=50)
    description: Optional[str] = Field(None, description="分类描述", max_length=200)
    parent_id: Optional[UUID] = Field(None, description="父分类ID")


class NewsCategoryCreate(NewsCategoryBase):
    """创建新闻分类模型"""
    sort_order: int = Field(0, description="排序顺序")


class NewsCategoryResponse(NewsCategoryBase):
    """新闻分类响应模型"""
    id: UUID = Field(..., description="分类ID")
    sort_order: int = Field(..., description="排序顺序")
    news_count: int = Field(0, description="新闻数量")
    is_active: bool = Field(True, description="是否活跃")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class NewsKeywordBase(BaseModel):
    """新闻关键词基础模型"""
    keyword: str = Field(..., description="关键词", max_length=50)
    category: str = Field(..., description="关键词类别", max_length=20)
    weight: float = Field(1.0, description="权重", ge=0.0, le=10.0)


class NewsKeywordResponse(NewsKeywordBase):
    """新闻关键词响应模型"""
    id: UUID = Field(..., description="关键词ID")
    frequency: int = Field(0, description="出现频次")
    last_seen: Optional[datetime] = Field(None, description="最后出现时间")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class NewsSearchRequest(BaseModel):
    """新闻搜索请求模型"""
    query: Optional[str] = Field(None, description="搜索关键词")
    category_id: Optional[UUID] = Field(None, description="分类ID")
    source: Optional[str] = Field(None, description="新闻来源")
    stock_code: Optional[str] = Field(None, description="相关股票代码")
    start_date: Optional[datetime] = Field(None, description="开始时间")
    end_date: Optional[datetime] = Field(None, description="结束时间")
    importance: Optional[int] = Field(None, description="重要性等级", ge=1, le=5)
    sentiment: Optional[str] = Field(None, description="情感倾向")
    tags: Optional[List[str]] = Field(None, description="标签筛选")
    sort_by: str = Field("published_at", description="排序字段")
    sort_order: str = Field("desc", description="排序方向")
    skip: int = Field(0, description="跳过数量", ge=0)
    limit: int = Field(20, description="限制数量", ge=1, le=100)


class NewsAnalyticsRequest(BaseModel):
    """新闻分析请求模型"""
    stock_code: Optional[str] = Field(None, description="股票代码")
    start_date: Optional[datetime] = Field(None, description="开始时间")
    end_date: Optional[datetime] = Field(None, description="结束时间")
    analysis_type: str = Field("sentiment", description="分析类型")


class SentimentAnalysis(BaseModel):
    """情感分析模型"""
    positive_count: int = Field(..., description="正面新闻数量")
    negative_count: int = Field(..., description="负面新闻数量")
    neutral_count: int = Field(..., description="中性新闻数量")
    average_sentiment: float = Field(..., description="平均情感分数")
    sentiment_trend: List[Dict[str, Any]] = Field(..., description="情感趋势")
    key_positive_news: List[NewsResponse] = Field(..., description="关键正面新闻")
    key_negative_news: List[NewsResponse] = Field(..., description="关键负面新闻")


class TopicAnalysis(BaseModel):
    """话题分析模型"""
    hot_topics: List[Dict[str, Any]] = Field(..., description="热门话题")
    topic_trends: List[Dict[str, Any]] = Field(..., description="话题趋势")
    keyword_cloud: List[Dict[str, Any]] = Field(..., description="关键词云")
    topic_sentiment: Dict[str, float] = Field(..., description="话题情感")


class NewsAnalyticsResponse(BaseModel):
    """新闻分析响应模型"""
    analysis_type: str = Field(..., description="分析类型")
    time_range: Dict[str, datetime] = Field(..., description="时间范围")
    total_news: int = Field(..., description="新闻总数")
    sentiment_analysis: Optional[SentimentAnalysis] = Field(None, description="情感分析")
    topic_analysis: Optional[TopicAnalysis] = Field(None, description="话题分析")
    analysis_date: datetime = Field(..., description="分析日期")


class NewsRecommendationRequest(BaseModel):
    """新闻推荐请求模型"""
    user_id: Optional[UUID] = Field(None, description="用户ID")
    stock_codes: Optional[List[str]] = Field(None, description="关注股票")
    interests: Optional[List[str]] = Field(None, description="兴趣标签")
    limit: int = Field(10, description="推荐数量", ge=1, le=50)


class NewsRecommendationResponse(BaseModel):
    """新闻推荐响应模型"""
    recommended_news: List[NewsResponse] = Field(..., description="推荐新闻")
    recommendation_reason: Dict[str, str] = Field(..., description="推荐理由")
    personalization_score: float = Field(..., description="个性化分数")


class NewsAlert(BaseModel):
    """新闻预警模型"""
    alert_type: str = Field(..., description="预警类型")
    title: str = Field(..., description="预警标题")
    message: str = Field(..., description="预警消息")
    importance: int = Field(..., description="重要性等级", ge=1, le=5)
    related_news: List[UUID] = Field(..., description="相关新闻ID")
    related_stocks: List[str] = Field(..., description="相关股票")
    created_at: datetime = Field(..., description="创建时间")


class NewsAlertResponse(BaseModel):
    """新闻预警响应模型"""
    alerts: List[NewsAlert] = Field(..., description="预警列表")
    total_alerts: int = Field(..., description="预警总数")
    unread_alerts: int = Field(..., description="未读预警数")


class NewsStatistics(BaseModel):
    """新闻统计模型"""
    total_news: int = Field(..., description="新闻总数")
    today_news: int = Field(..., description="今日新闻")
    weekly_news: int = Field(..., description="本周新闻")
    monthly_news: int = Field(..., description="本月新闻")
    source_distribution: Dict[str, int] = Field(..., description="来源分布")
    category_distribution: Dict[str, int] = Field(..., description="分类分布")
    sentiment_distribution: Dict[str, int] = Field(..., description="情感分布")
    hot_keywords: List[Dict[str, Any]] = Field(..., description="热门关键词")
    trending_topics: List[Dict[str, Any]] = Field(..., description="趋势话题")
    last_updated: datetime = Field(..., description="最后更新时间")


class NewsCollectionTask(BaseModel):
    """新闻采集任务模型"""
    task_id: str = Field(..., description="任务ID")
    source: str = Field(..., description="采集源")
    status: str = Field(..., description="任务状态")
    collected_count: int = Field(0, description="已采集数量")
    processed_count: int = Field(0, description="已处理数量")
    error_count: int = Field(0, description="错误数量")
    start_time: datetime = Field(..., description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    error_message: Optional[str] = Field(None, description="错误信息")


class NewsCollectionResponse(BaseModel):
    """新闻采集响应模型"""
    task: NewsCollectionTask = Field(..., description="采集任务")
    progress: int = Field(..., description="进度百分比", ge=0, le=100)
    estimated_completion: Optional[datetime] = Field(None, description="预计完成时间")


class NewsExportRequest(BaseModel):
    """新闻导出请求模型"""
    format: str = Field("json", description="导出格式", regex="^(json|csv|excel)$")
    filters: NewsSearchRequest = Field(..., description="筛选条件")
    fields: Optional[List[str]] = Field(None, description="导出字段")


class NewsExportResponse(BaseModel):
    """新闻导出响应模型"""
    export_id: str = Field(..., description="导出ID")
    status: str = Field(..., description="导出状态")
    file_url: Optional[str] = Field(None, description="文件下载链接")
    file_size: Optional[int] = Field(None, description="文件大小")
    record_count: int = Field(..., description="记录数量")
    created_at: datetime = Field(..., description="创建时间")
    expires_at: datetime = Field(..., description="过期时间")


class NewsSubscription(BaseModel):
    """新闻订阅模型"""
    subscription_id: UUID = Field(..., description="订阅ID")
    user_id: UUID = Field(..., description="用户ID")
    keywords: List[str] = Field(..., description="关键词列表")
    stock_codes: List[str] = Field(..., description="股票代码列表")
    categories: List[UUID] = Field(..., description="分类列表")
    notification_method: str = Field(..., description="通知方式")
    is_active: bool = Field(True, description="是否活跃")
    created_at: datetime = Field(..., description="创建时间")


class NewsSubscriptionResponse(BaseModel):
    """新闻订阅响应模型"""
    subscription: NewsSubscription = Field(..., description="订阅信息")
    matched_news_count: int = Field(..., description="匹配新闻数量")
    last_notification: Optional[datetime] = Field(None, description="最后通知时间")
