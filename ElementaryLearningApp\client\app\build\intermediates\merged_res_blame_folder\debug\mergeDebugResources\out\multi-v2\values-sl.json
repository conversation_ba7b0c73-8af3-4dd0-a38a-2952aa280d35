{"logs": [{"outputFile": "com.example.elementarylearningcompanion.app-mergeDebugResources-58:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\586aaef9cc7b931892e6d835ad6bc99e\\transformed\\material3-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,313,431,560,670,766,879,1019,1145,1288,1373,1472,1565,1662,1779,1901,2005,2142,2276,2407,2591,2718,2841,2966,3088,3182,3280,3400,3524,3624,3733,3839,3982,4129,4238,4340,4424,4519,4615,4703,4789,4892,4974,5057,5152,5252,5343,5440,5528,5632,5729,5831,5973,6055,6161", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "179,308,426,555,665,761,874,1014,1140,1283,1368,1467,1560,1657,1774,1896,2000,2137,2271,2402,2586,2713,2836,2961,3083,3177,3275,3395,3519,3619,3728,3834,3977,4124,4233,4335,4419,4514,4610,4698,4784,4887,4969,5052,5147,5247,5338,5435,5523,5627,5724,5826,5968,6050,6156,6255"}, "to": {"startLines": "62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5633,5762,5891,6009,6138,6248,6344,6457,6597,6723,6866,6951,7050,7143,7240,7357,7479,7583,7720,7854,7985,8169,8296,8419,8544,8666,8760,8858,8978,9102,9202,9311,9417,9560,9707,9816,9918,10002,10097,10193,10281,10367,10470,10552,10635,10730,10830,10921,11018,11106,11210,11307,11409,11551,11633,11739", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "5757,5886,6004,6133,6243,6339,6452,6592,6718,6861,6946,7045,7138,7235,7352,7474,7578,7715,7849,7980,8164,8291,8414,8539,8661,8755,8853,8973,9097,9197,9306,9412,9555,9702,9811,9913,9997,10092,10188,10276,10362,10465,10547,10630,10725,10825,10916,11013,11101,11205,11302,11404,11546,11628,11734,11833"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a4df73b4c2d29b2ee6d6405fd9c7e463\\transformed\\ui-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,281,377,475,560,637,724,816,898,971,1043,1125,1211,1283,1361,1431", "endColumns": "94,80,95,97,84,76,86,91,81,72,71,81,85,71,77,69,120", "endOffsets": "195,276,372,470,555,632,719,811,893,966,1038,1120,1206,1278,1356,1426,1547"}, "to": {"startLines": "50,51,52,53,54,59,60,176,177,178,179,181,182,186,189,190,191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4651,4746,4827,4923,5021,5406,5483,16457,16549,16631,16704,16853,16935,17268,17519,17597,17667", "endColumns": "94,80,95,97,84,76,86,91,81,72,71,81,85,71,77,69,120", "endOffsets": "4741,4822,4918,5016,5101,5478,5565,16544,16626,16699,16771,16930,17016,17335,17592,17662,17783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fd01bc6fe90ff89257de9b3fddbd915b\\transformed\\foundation-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,90", "endOffsets": "140,231"}, "to": {"startLines": "192,193", "startColumns": "4,4", "startOffsets": "17788,17878", "endColumns": "89,90", "endOffsets": "17873,17964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\95602d9e58c05302d7b199e835d43608\\transformed\\appcompat-1.6.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "417,529,631,739,826,929,1048,1129,1207,1299,1393,1488,1582,1677,1771,1867,1967,2059,2151,2235,2343,2451,2551,2664,2772,2877,3057,17021", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "524,626,734,821,924,1043,1124,1202,1294,1388,1483,1577,1672,1766,1862,1962,2054,2146,2230,2338,2446,2546,2659,2767,2872,3052,3152,17100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a90387b92ad49178bbd95d8e99c39d57\\transformed\\core-1.13.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "40,41,42,43,44,45,46,188", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3612,3709,3811,3909,4013,4116,4218,17418", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3704,3806,3904,4008,4111,4213,4330,17514"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0a92ec79585cffeb394139b023740469\\transformed\\material-1.12.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,367,456,545,633,731,822,928,1054,1138,1202,1268,1362,1438,1501,1613,1673,1738,1792,1862,1922,1978,2090,2147,2209,2265,2338,2472,2557,2634,2723,2804,2889,3032,3116,3199,3333,3422,3499,3555,3610,3676,3749,3826,3897,3976,4050,4126,4201,4274,4379,4467,4540,4630,4721,4793,4867,4958,5010,5092,5159,5243,5330,5392,5456,5519,5588,5691,5799,5897,6001,6061,6120,6197,6284,6360", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "362,451,540,628,726,817,923,1049,1133,1197,1263,1357,1433,1496,1608,1668,1733,1787,1857,1917,1973,2085,2142,2204,2260,2333,2467,2552,2629,2718,2799,2884,3027,3111,3194,3328,3417,3494,3550,3605,3671,3744,3821,3892,3971,4045,4121,4196,4269,4374,4462,4535,4625,4716,4788,4862,4953,5005,5087,5154,5238,5325,5387,5451,5514,5583,5686,5794,5892,5996,6056,6115,6192,6279,6355,6433"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,55,56,57,58,61,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,180,184,185,187", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3157,3246,3335,3423,3521,4335,4441,4567,5106,5170,5236,5330,5570,11838,11950,12010,12075,12129,12199,12259,12315,12427,12484,12546,12602,12675,12809,12894,12971,13060,13141,13226,13369,13453,13536,13670,13759,13836,13892,13947,14013,14086,14163,14234,14313,14387,14463,14538,14611,14716,14804,14877,14967,15058,15130,15204,15295,15347,15429,15496,15580,15667,15729,15793,15856,15925,16028,16136,16234,16338,16398,16776,17105,17192,17340", "endLines": "7,35,36,37,38,39,47,48,49,55,56,57,58,61,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,180,184,185,187", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "412,3241,3330,3418,3516,3607,4436,4562,4646,5165,5231,5325,5401,5628,11945,12005,12070,12124,12194,12254,12310,12422,12479,12541,12597,12670,12804,12889,12966,13055,13136,13221,13364,13448,13531,13665,13754,13831,13887,13942,14008,14081,14158,14229,14308,14382,14458,14533,14606,14711,14799,14872,14962,15053,15125,15199,15290,15342,15424,15491,15575,15662,15724,15788,15851,15920,16023,16131,16229,16333,16393,16452,16848,17187,17263,17413"}}]}]}