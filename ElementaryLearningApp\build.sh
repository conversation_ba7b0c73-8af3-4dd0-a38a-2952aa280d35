#!/bin/bash

# Elementary Learning App Build Script
# 小学学习伴侣构建脚本

echo "=========================================="
echo "Elementary Learning App Build Script"
echo "小学学习伴侣构建脚本"
echo "=========================================="

# Set variables
PROJECT_ROOT=$(pwd)
SERVER_DIR="$PROJECT_ROOT/server"
CLIENT_DIR="$PROJECT_ROOT/client"
BUILD_DIR="$PROJECT_ROOT/build"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Create build directory
echo "Creating build directory..."
mkdir -p "$BUILD_DIR"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "Checking prerequisites..."

if ! command_exists java; then
    echo "Error: Java is not installed or not in PATH"
    exit 1
fi

if ! command_exists java; then
    echo "Error: Java is not available"
    exit 1
fi

echo "Prerequisites check passed!"

# Build Spring Boot Server
echo "=========================================="
echo "Building Spring Boot Server..."
echo "=========================================="

cd "$SERVER_DIR"

# Clean and build
echo "Cleaning previous builds..."
./gradlew clean

echo "Building server JAR..."
./gradlew bootJar -x test

if [ $? -eq 0 ]; then
    echo "Server build successful!"
    
    # Copy JAR to build directory
    JAR_FILE=$(find build/libs -name "*.jar" | head -1)
    if [ -n "$JAR_FILE" ]; then
        cp "$JAR_FILE" "$BUILD_DIR/elementary-learning-server-${TIMESTAMP}.jar"
        echo "Server JAR copied to build directory"
    fi
else
    echo "Server build failed!"
    exit 1
fi

# Build Android Client
echo "=========================================="
echo "Building Android Client..."
echo "=========================================="

cd "$CLIENT_DIR"

# Check if Android SDK is available
if [ -z "$ANDROID_HOME" ]; then
    echo "Warning: ANDROID_HOME is not set. Trying to detect Android SDK..."
    
    # Common Android SDK locations
    POSSIBLE_ANDROID_HOMES=(
        "$HOME/Android/Sdk"
        "$HOME/Library/Android/sdk"
        "/opt/android-sdk"
        "/usr/local/android-sdk"
    )
    
    for path in "${POSSIBLE_ANDROID_HOMES[@]}"; do
        if [ -d "$path" ]; then
            export ANDROID_HOME="$path"
            echo "Found Android SDK at: $ANDROID_HOME"
            break
        fi
    done
    
    if [ -z "$ANDROID_HOME" ]; then
        echo "Error: Android SDK not found. Please set ANDROID_HOME environment variable."
        exit 1
    fi
fi

# Add Android tools to PATH
export PATH="$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools:$PATH"

echo "Cleaning previous builds..."
if [ -f "./gradlew" ]; then
    ./gradlew clean
else
    gradle clean
fi

echo "Building debug APK..."
if [ -f "./gradlew" ]; then
    ./gradlew assembleDebug
else
    gradle assembleDebug
fi

if [ $? -eq 0 ]; then
    echo "Android build successful!"
    
    # Copy APK to build directory
    APK_FILE=$(find app/build/outputs/apk/debug -name "*.apk" | head -1)
    if [ -n "$APK_FILE" ]; then
        cp "$APK_FILE" "$BUILD_DIR/elementary-learning-app-${TIMESTAMP}.apk"
        echo "APK copied to build directory"
    fi
else
    echo "Android build failed!"
    exit 1
fi

# Create deployment package
echo "=========================================="
echo "Creating deployment package..."
echo "=========================================="

cd "$BUILD_DIR"

# Create deployment directory
DEPLOY_DIR="elementary-learning-${TIMESTAMP}"
mkdir -p "$DEPLOY_DIR"

# Copy files
if [ -f "elementary-learning-server-${TIMESTAMP}.jar" ]; then
    cp "elementary-learning-server-${TIMESTAMP}.jar" "$DEPLOY_DIR/"
fi

if [ -f "elementary-learning-app-${TIMESTAMP}.apk" ]; then
    cp "elementary-learning-app-${TIMESTAMP}.apk" "$DEPLOY_DIR/"
fi

# Copy documentation and scripts
cp "$PROJECT_ROOT/README.md" "$DEPLOY_DIR/" 2>/dev/null || echo "README.md not found"
cp "$PROJECT_ROOT/server/database_schema.sql" "$DEPLOY_DIR/" 2>/dev/null
cp "$PROJECT_ROOT/server/sample_data.sql" "$DEPLOY_DIR/" 2>/dev/null

# Create deployment instructions
cat > "$DEPLOY_DIR/DEPLOYMENT.md" << EOF
# Elementary Learning App Deployment Guide
# 小学学习伴侣部署指南

## 服务器部署 (Server Deployment)

### 前置条件 (Prerequisites)
- Java 17 或更高版本
- MySQL 8.0 或更高版本

### 部署步骤 (Deployment Steps)

1. 创建数据库 (Create Database)
   \`\`\`sql
   CREATE DATABASE elementary_learning CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   \`\`\`

2. 导入数据库结构 (Import Database Schema)
   \`\`\`bash
   mysql -u root -p elementary_learning < database_schema.sql
   \`\`\`

3. 导入示例数据 (Import Sample Data)
   \`\`\`bash
   mysql -u root -p elementary_learning < sample_data.sql
   \`\`\`

4. 启动服务器 (Start Server)
   \`\`\`bash
   java -jar elementary-learning-server-${TIMESTAMP}.jar
   \`\`\`

服务器将在 http://localhost:8080 启动

## Android应用安装 (Android App Installation)

1. 在Android设备上启用"未知来源"安装
2. 将APK文件传输到设备
3. 安装 elementary-learning-app-${TIMESTAMP}.apk

## 配置说明 (Configuration)

### 服务器配置 (Server Configuration)
- 数据库连接：修改 application.properties 中的数据库配置
- 端口设置：默认8080，可通过 server.port 修改

### 客户端配置 (Client Configuration)
- 服务器地址：在 RetrofitClient.java 中修改 BASE_URL

## 测试账号 (Test Accounts)
- 手机号：***********，密码：password
- 手机号：***********，密码：password

构建时间：${TIMESTAMP}
EOF

# Create archive
echo "Creating deployment archive..."
tar -czf "elementary-learning-${TIMESTAMP}.tar.gz" "$DEPLOY_DIR"

echo "=========================================="
echo "Build completed successfully!"
echo "=========================================="
echo "Build artifacts:"
echo "- Server JAR: $BUILD_DIR/elementary-learning-server-${TIMESTAMP}.jar"
echo "- Android APK: $BUILD_DIR/elementary-learning-app-${TIMESTAMP}.apk"
echo "- Deployment package: $BUILD_DIR/elementary-learning-${TIMESTAMP}.tar.gz"
echo ""
echo "Deployment directory: $BUILD_DIR/$DEPLOY_DIR"
echo "=========================================="

cd "$PROJECT_ROOT"
