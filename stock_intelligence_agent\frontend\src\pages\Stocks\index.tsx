/**
 * 股票页面
 */

import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Row,
  Col,
  Card,
  Table,
  Input,
  Select,
  Button,
  Space,
  Tag,
  Typography,
  Tabs,
  Statistic,
  Progress,
  Tooltip,
  message,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  StarOutlined,
  StarFilled,
  TrendingUpOutlined,
  TrendingDownOutlined,
  ReloadOutlined,
  DownloadOutlined,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '@/store';
import {
  fetchStocksAsync,
  fetchHotSectorsAsync,
  selectStocks,
  selectStocksLoading,
  selectHotSectors,
  selectHotSectorsLoading,
} from '@/store/slices/stockSlice';
import StockPriceDisplay from '@/components/StockPriceDisplay';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

interface StockData {
  code: string;
  name: string;
  market: string;
  industry: string;
  close_price: number;
  change: number;
  pct_change: number;
  volume: number;
  amount: number;
  turnover_rate: number;
  pe_ratio: number;
  pb_ratio: number;
  is_watched?: boolean;
}

const StocksPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [searchParams, setSearchParams] = useSearchParams();
  
  // 状态
  const stocks = useAppSelector(selectStocks);
  const stocksLoading = useAppSelector(selectStocksLoading);
  const hotSectors = useAppSelector(selectHotSectors);
  const hotSectorsLoading = useAppSelector(selectHotSectorsLoading);
  
  // 本地状态
  const [searchText, setSearchText] = useState('');
  const [selectedMarket, setSelectedMarket] = useState<string>('all');
  const [selectedIndustry, setSelectedIndustry] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('pct_change');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [activeTab, setActiveTab] = useState('stocks');

  // 初始化数据
  useEffect(() => {
    const params = {
      limit: 100,
      sort_by: sortBy,
      order: sortOrder,
      market: selectedMarket !== 'all' ? selectedMarket : undefined,
      industry: selectedIndustry !== 'all' ? selectedIndustry : undefined,
      search: searchText || undefined,
    };
    
    dispatch(fetchStocksAsync(params));
    dispatch(fetchHotSectorsAsync({ limit: 20 }));
  }, [dispatch, sortBy, sortOrder, selectedMarket, selectedIndustry, searchText]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
  };

  // 处理筛选
  const handleFilterChange = (type: string, value: string) => {
    if (type === 'market') {
      setSelectedMarket(value);
    } else if (type === 'industry') {
      setSelectedIndustry(value);
    }
  };

  // 处理排序
  const handleSortChange = (field: string) => {
    if (field === sortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  // 处理自选股
  const handleWatchlistToggle = (stock: StockData) => {
    // TODO: 实现自选股添加/移除逻辑
    message.success(stock.is_watched ? '已移除自选股' : '已添加到自选股');
  };

  // 刷新数据
  const handleRefresh = () => {
    const params = {
      limit: 100,
      sort_by: sortBy,
      order: sortOrder,
      market: selectedMarket !== 'all' ? selectedMarket : undefined,
      industry: selectedIndustry !== 'all' ? selectedIndustry : undefined,
      search: searchText || undefined,
    };
    
    dispatch(fetchStocksAsync(params));
    dispatch(fetchHotSectorsAsync({ limit: 20 }));
  };

  // 表格列配置
  const stockColumns = [
    {
      title: '股票',
      key: 'stock',
      fixed: 'left' as const,
      width: 200,
      render: (record: StockData) => (
        <div className="flex-between">
          <div>
            <div className="flex-center">
              <Text strong className="stock-code">
                {record.code}
              </Text>
              <Button
                type="text"
                size="small"
                icon={record.is_watched ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleWatchlistToggle(record);
                }}
                style={{ marginLeft: 4 }}
              />
            </div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.name}
            </Text>
            <br />
            <Tag size="small" color="blue">
              {record.market}
            </Tag>
          </div>
        </div>
      ),
    },
    {
      title: '行业',
      dataIndex: 'industry',
      key: 'industry',
      width: 120,
      render: (industry: string) => (
        <Text style={{ fontSize: 12 }}>{industry || '-'}</Text>
      ),
    },
    {
      title: '价格',
      key: 'price',
      width: 120,
      sorter: true,
      sortOrder: sortBy === 'close_price' ? sortOrder : null,
      render: (record: StockData) => (
        <StockPriceDisplay
          price={record.close_price}
          change={record.change}
          pctChange={record.pct_change}
          size="small"
        />
      ),
    },
    {
      title: '成交量',
      dataIndex: 'volume',
      key: 'volume',
      width: 100,
      sorter: true,
      sortOrder: sortBy === 'volume' ? sortOrder : null,
      render: (volume: number) => (
        <Text style={{ fontSize: 12 }}>
          {volume ? (volume / 10000).toFixed(1) + '万' : '-'}
        </Text>
      ),
    },
    {
      title: '成交额',
      dataIndex: 'amount',
      key: 'amount',
      width: 100,
      sorter: true,
      sortOrder: sortBy === 'amount' ? sortOrder : null,
      render: (amount: number) => (
        <Text style={{ fontSize: 12 }}>
          {amount ? (amount / 100000000).toFixed(2) + '亿' : '-'}
        </Text>
      ),
    },
    {
      title: '换手率',
      dataIndex: 'turnover_rate',
      key: 'turnover_rate',
      width: 80,
      sorter: true,
      sortOrder: sortBy === 'turnover_rate' ? sortOrder : null,
      render: (rate: number) => (
        <Text style={{ fontSize: 12 }}>
          {rate ? rate.toFixed(2) + '%' : '-'}
        </Text>
      ),
    },
    {
      title: 'PE',
      dataIndex: 'pe_ratio',
      key: 'pe_ratio',
      width: 80,
      render: (pe: number) => (
        <Text style={{ fontSize: 12 }}>
          {pe ? pe.toFixed(1) : '-'}
        </Text>
      ),
    },
    {
      title: 'PB',
      dataIndex: 'pb_ratio',
      key: 'pb_ratio',
      width: 80,
      render: (pb: number) => (
        <Text style={{ fontSize: 12 }}>
          {pb ? pb.toFixed(2) : '-'}
        </Text>
      ),
    },
  ];

  return (
    <div>
      {/* 页面标题 */}
      <div className="flex-between" style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          股票市场
        </Title>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={stocksLoading}
          >
            刷新
          </Button>
          <Button icon={<DownloadOutlined />}>
            导出
          </Button>
        </Space>
      </div>

      {/* 筛选工具栏 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索股票代码或名称"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              value={selectedMarket}
              onChange={(value) => handleFilterChange('market', value)}
              style={{ width: '100%' }}
            >
              <Option value="all">全部市场</Option>
              <Option value="SH">上海</Option>
              <Option value="SZ">深圳</Option>
              <Option value="BJ">北京</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              value={selectedIndustry}
              onChange={(value) => handleFilterChange('industry', value)}
              style={{ width: '100%' }}
            >
              <Option value="all">全部行业</Option>
              <Option value="银行">银行</Option>
              <Option value="房地产">房地产</Option>
              <Option value="医药生物">医药生物</Option>
              <Option value="电子">电子</Option>
              <Option value="计算机">计算机</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* 主要内容 */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="股票列表" key="stocks">
          <Card>
            <Table
              columns={stockColumns}
              dataSource={stocks}
              loading={stocksLoading}
              rowKey="code"
              scroll={{ x: 1000 }}
              pagination={{
                total: stocks.length,
                pageSize: 50,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
              onRow={(record) => ({
                onClick: () => navigate(`/stocks/${record.code}`),
                style: { cursor: 'pointer' },
              })}
              onChange={(pagination, filters, sorter: any) => {
                if (sorter.field) {
                  handleSortChange(sorter.field);
                }
              }}
            />
          </Card>
        </TabPane>
        
        <TabPane tab="热门板块" key="sectors">
          <Card loading={hotSectorsLoading}>
            <Row gutter={[16, 16]}>
              {hotSectors.map((sector, index) => (
                <Col xs={24} sm={12} md={8} lg={6} key={index}>
                  <Card
                    size="small"
                    hoverable
                    onClick={() => navigate(`/sectors/${sector.sector_name}`)}
                    style={{ cursor: 'pointer' }}
                  >
                    <div className="text-center">
                      <Text strong>{sector.sector_name}</Text>
                      <div style={{ margin: '8px 0' }}>
                        <Text
                          className={
                            sector.avg_pct_change >= 0
                              ? 'stock-change-positive'
                              : 'stock-change-negative'
                          }
                          style={{ fontSize: 18, fontWeight: 'bold' }}
                        >
                          {sector.avg_pct_change >= 0 ? '+' : ''}
                          {sector.avg_pct_change.toFixed(2)}%
                        </Text>
                      </div>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {sector.stock_count} 只股票
                      </Text>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default StocksPage;
