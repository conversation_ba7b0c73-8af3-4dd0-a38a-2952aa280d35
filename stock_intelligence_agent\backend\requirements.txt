# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
sqlalchemy==2.0.23
alembic==1.13.1
asyncpg==0.29.0
psycopg2-binary==2.9.9

# Cache and Message Queue
redis==5.0.1
celery==5.3.4
kombu==5.3.4

# Data Processing
pandas==2.1.4
numpy==1.25.2
akshare==1.11.80
tushare==1.2.89

# Technical Analysis
TA-Lib==0.4.28
talib==0.4.28

# HTTP Clients
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# Web Scraping
scrapy==2.11.0
selenium==4.15.2
beautifulsoup4==4.12.2
lxml==4.9.3

# Natural Language Processing
jieba==0.42.1
transformers==4.36.2
torch==2.1.2

# Time Series Database
influxdb-client==1.39.0

# Search Engine
elasticsearch==8.11.1

# WebSocket
websockets==12.0
python-socketio==5.10.0

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Monitoring & Logging
prometheus-client==0.19.0
structlog==23.2.0
loguru==0.7.2

# Configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
factory-boy==3.3.0

# Development Tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Utilities
python-dateutil==2.8.2
pytz==2023.3
schedule==1.2.0
click==8.1.7

# Image Processing (for future features)
Pillow==10.1.0

# Financial Data
yfinance==0.2.28
ccxt==4.1.74

# Machine Learning
scikit-learn==1.3.2
xgboost==2.0.2
lightgbm==4.1.0

# Visualization (for backend analysis)
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0
