-- 数据库设计 v1.0
-- 创建数据库
CREATE DATABASE IF NOT EXISTS `elementary_learning_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `elementary_learning_db`;

-- 学生表
CREATE TABLE `students` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `nickname` VARCHAR(50) NOT NULL COMMENT '昵称',
  `avatar_url` VARCHAR(255) DEFAULT NULL COMMENT '头像地址',
  `grade` INT NOT NULL COMMENT '年级',
  `textbook_version_id` INT NOT NULL COMMENT '教材版本ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT='学生信息表';

-- 家长表
CREATE TABLE `parents` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `phone_number` VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号，用于登录',
  `password_hash` VARCHAR(255) NOT NULL COMMENT '加密后的密码',
  `nickname` VARCHAR(50) DEFAULT NULL COMMENT '昵称',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT='家长信息表';

-- 学生-家长关联表
CREATE TABLE `student_parent_link` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `student_id` BIGINT NOT NULL,
  `parent_id` BIGINT NOT NULL,
  `relationship` VARCHAR(20) COMMENT '关系，如：爸爸、妈妈',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `idx_student_parent` (`student_id`, `parent_id`),
  FOREIGN KEY (`student_id`) REFERENCES `students`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`parent_id`) REFERENCES `parents`(`id`) ON DELETE CASCADE
) COMMENT='学生与家长的关联关系表';

-- 未来还会需要教材版本表、积分表等等，此处为v1.0核心用户系统。


-- 教材版本表
CREATE TABLE `textbook_versions` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(50) NOT NULL COMMENT '教材版本名称，如：人教版、苏教版',
  `subject` VARCHAR(20) NOT NULL COMMENT '所属科目，如：语文、数学、英语',
  `applicable_grades` VARCHAR(50) COMMENT '适用年级，如：1-6'
) COMMENT='教材版本信息表';


-- 课文表
CREATE TABLE `lessons` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `title` VARCHAR(100) NOT NULL COMMENT '课文标题',
  `lesson_number` INT COMMENT '课程序号',
  `textbook_version_id` INT NOT NULL,
  `grade` INT NOT NULL,
  FOREIGN KEY (`textbook_version_id`) REFERENCES `textbook_versions`(`id`)
) COMMENT='课文信息表';

-- 句子表 (包含音频等信息)
CREATE TABLE `sentences` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `lesson_id` BIGINT NOT NULL,
  `sentence_order` INT NOT NULL COMMENT '句子在课文中的顺序',
  `text_content` TEXT NOT NULL COMMENT '句子文本内容',
  `audio_url` VARCHAR(255) COMMENT '标准朗读音频地址',
  `start_timestamp` FLOAT COMMENT '音频开始时间点',
  `end_timestamp` FLOAT COMMENT '音频结束时间点',
  FOREIGN KEY (`lesson_id`) REFERENCES `lessons`(`id`) ON DELETE CASCADE
) COMMENT='句子信息表';


-- 生字表
CREATE TABLE `characters` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `lesson_id` BIGINT COMMENT '关联的课文ID，可以为NULL',
  `character_text` VARCHAR(5) NOT NULL COMMENT '单个汉字',
  `pinyin` VARCHAR(30) NOT NULL COMMENT '拼音',
  `strokes` INT COMMENT '笔画数',
  `radical` VARCHAR(10) COMMENT '部首',
  `structure` VARCHAR(20) COMMENT '字形结构，如：左右结构',
  `audio_url` VARCHAR(255) COMMENT '发音音频地址',
  `stroke_animation_url` VARCHAR(255) COMMENT '笔顺动画资源地址'
) COMMENT='生字信息表';

-- 数学练习题表
CREATE TABLE `math_exercises` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `textbook_version_id` INT NOT NULL COMMENT '教材版本ID',
  `grade` INT NOT NULL COMMENT '年级',
  `chapter` VARCHAR(50) COMMENT '章节',
  `exercise_type` VARCHAR(30) NOT NULL COMMENT '练习类型：addition, subtraction, multiplication, division',
  `difficulty_level` INT DEFAULT 1 COMMENT '难度等级 1-5',
  `question` VARCHAR(100) NOT NULL COMMENT '题目，如：3 + 5 = ?',
  `correct_answer` VARCHAR(20) NOT NULL COMMENT '正确答案',
  `options` JSON COMMENT '选择题选项，JSON格式',
  `explanation` TEXT COMMENT '解题说明',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_textbook_grade` (`textbook_version_id`, `grade`),
  INDEX `idx_exercise_type` (`exercise_type`),
  FOREIGN KEY (`textbook_version_id`) REFERENCES `textbook_versions`(`id`) ON DELETE CASCADE
) COMMENT='数学练习题表';

-- 学习记录表
CREATE TABLE `learning_records` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `student_id` BIGINT NOT NULL COMMENT '学生ID',
  `content_type` VARCHAR(20) NOT NULL COMMENT '内容类型：lesson, character, math_exercise, english_word',
  `content_id` BIGINT NOT NULL COMMENT '内容ID',
  `action_type` VARCHAR(20) NOT NULL COMMENT '操作类型：view, practice, complete',
  `is_correct` BOOLEAN COMMENT '是否正确（仅练习题）',
  `time_spent` INT COMMENT '花费时间（秒）',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_student_content` (`student_id`, `content_type`),
  INDEX `idx_created_at` (`created_at`),
  FOREIGN KEY (`student_id`) REFERENCES `students`(`id`) ON DELETE CASCADE
) COMMENT='学习记录表';

-- 错题本表
CREATE TABLE `wrong_questions` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `student_id` BIGINT NOT NULL COMMENT '学生ID',
  `content_type` VARCHAR(20) NOT NULL COMMENT '内容类型：math_exercise, english_word等',
  `content_id` BIGINT NOT NULL COMMENT '内容ID',
  `wrong_answer` VARCHAR(100) COMMENT '错误答案',
  `retry_count` INT DEFAULT 0 COMMENT '重做次数',
  `is_mastered` BOOLEAN DEFAULT FALSE COMMENT '是否已掌握',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_student_type` (`student_id`, `content_type`),
  FOREIGN KEY (`student_id`) REFERENCES `students`(`id`) ON DELETE CASCADE
) COMMENT='错题本表';
