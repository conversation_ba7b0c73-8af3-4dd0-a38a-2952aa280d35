-- Merging decision tree log ---
manifest
ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:2:1-64:12
INJECTED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:2:1-64:12
INJECTED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:2:1-64:12
INJECTED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:2:1-64:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a92ec79585cffeb394139b023740469\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\abc752c93138f8c097c427e2d1cfd0d3\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\067a37e237c05460810f9f277f8a584d\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\95602d9e58c05302d7b199e835d43608\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b36af8a886450074d6162902a51e235f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4487a3ad6da06723d087bdf02bb6212f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bbf9a8b156fa420b5bdcba47fcc5dbcc\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e2100e335e320ce9f7d00232cdd6ecf\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\091a3284e1a4dc532944a24ac50d7eb7\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6aac3b3dedcfdd7e5ad9b9976b2a0712\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\04cbc788dc368aaa114d050c4ee80c6e\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\586aaef9cc7b931892e6d835ad6bc99e\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd01bc6fe90ff89257de9b3fddbd915b\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\75430ae0bb5fc32db242c11acf14d810\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\aae8de59d12aa99a6bbf2e13dbb7830c\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf65474ba1a366f73047e913760fe941\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ddb5585b1c51e8bca0bfec1fc4b8bcd5\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea2513363d3f32d4ffb9d885f5bb824a\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1998a5b97855770a469c4c0f6ac2ff\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\abac53953083f897e88d18fa78efaf14\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\bcc3fb5a217e851b87eb85758de9a548\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\f552d381a4d68594072bd5f3cc37425f\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe57ec642b760abd119345d9083b9c3\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\c159823cd1d5b89649bf0a3c97706e19\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\81e6a57d5e25883e08f3f201e44bac3c\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\2216d807e39e4d0ad5c14ea6533e364e\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0bb11b1c721d2ccd7d5b795aedf051c\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\55d6400460d0a92f9c14db5102d6c5ca\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\04a60b50f4f9f1a1d0f65646d590b29d\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b32cc995a07f9e43ad6e96e5d0a4e2a\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fbaf43bac10d410b67ca8e8b41e4c08\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-livedata:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\f149972dcc2086112599bf9e6302b5ef\transformed\runtime-livedata-1.6.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4df73b4c2d29b2ee6d6405fd9c7e463\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c35d31911b1cf341f8d6742e8d2ae7f9\transformed\activity-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3980a140e6a763fb7ce351a902913430\transformed\activity-compose-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\412960d2e644cc6534bfc5724cc17cfb\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39c67d931621a4f2affb04e55a6b9d79\transformed\activity-1.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\464fe66b12ab402611cd361fca1d25a7\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6046befa68638da83c6d2ae196e7998f\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b188ac107923aed40d7280e63837d04b\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a1f48f776539bdce15fc290f8f421df\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a08d198b9c0aabe88b2d25fb1803f0e2\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\edd54d07eefba3de62cf3a424ba35a99\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\96056d7fd1109e2695ec224e30a717db\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a52c49df1ebcdfa482361ad588178e37\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7a976ce1b4bf90df4237a3614c14070\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dec948b9f09d4d2ea008ad8467768397\transformed\lifecycle-viewmodel-2.8.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94846f2e2b6d8b762b746d3cbe3bbeb6\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3121edf18686d7604b1eec829eb8c555\transformed\lifecycle-livedata-core-ktx-2.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\18f0d342697045afee3821219cdbfd53\transformed\lifecycle-livedata-core-2.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c9d1ccbaefd891ddd9f1cf64befcde0\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6817582c8de4319ffe182939b2b4b537\transformed\lifecycle-viewmodel-ktx-2.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b644477bea90e56aa6841d3d5cfcd45e\transformed\lifecycle-livedata-2.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e89c7198796348a35cd4e45f7fa7f84\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\31d65ad1fefe9c1d16196b373d5ef2cf\transformed\lifecycle-viewmodel-savedstate-2.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\449f33422d7e13b8be9da16a763d6e24\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\be5c7bc7ecd14619d60db4b085b1cddc\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8a6fb48f2af62a21b8983a783d922af1\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f11a28a7e824ba62711219801cfa226\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2262cda324a9c4192fbaf465b29147d8\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b96dd2dffa7d8d920f9bb92528af68ee\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\df40381d346eae08000be068c6d29355\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ff825a51e922be0f197fe7521e4577e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94f6c5b28a22bda54ada88c18854768c\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bbe371e66941691f6f2c06f572d22568\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9af66da9ecfa08435b32349cc2f63841\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\617ecbf72f7cd31e3b222745db0245ac\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27a1974ffd655a45dfd663e4e0a5fd24\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:5:22-64
application
ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:7:5-62:19
INJECTED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:7:5-62:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a92ec79585cffeb394139b023740469\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a92ec79585cffeb394139b023740469\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\abc752c93138f8c097c427e2d1cfd0d3\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\abc752c93138f8c097c427e2d1cfd0d3\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\c159823cd1d5b89649bf0a3c97706e19\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\c159823cd1d5b89649bf0a3c97706e19\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\81e6a57d5e25883e08f3f201e44bac3c\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\81e6a57d5e25883e08f3f201e44bac3c\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b96dd2dffa7d8d920f9bb92528af68ee\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b96dd2dffa7d8d920f9bb92528af68ee\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ff825a51e922be0f197fe7521e4577e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ff825a51e922be0f197fe7521e4577e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:12:9-35
	android:label
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:11:9-41
	android:fullBackupContent
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:10:9-54
	tools:targetApi
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:15:9-29
	android:allowBackup
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:8:9-35
	android:theme
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:13:9-65
	android:dataExtractionRules
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:9:9-65
	android:usesCleartextTraffic
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:14:9-44
activity#com.example.elementarylearningcompanion.MainActivity
ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:16:9-26:20
	android:label
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:19:13-45
	android:exported
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:18:13-36
	android:theme
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:20:13-69
	android:name
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:17:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:21:13-25:29
action#android.intent.action.MAIN
ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:22:17-69
	android:name
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:22:25-66
category#android.intent.category.LAUNCHER
ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:24:17-77
	android:name
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:24:27-74
activity#com.example.elementarylearningcompanion.OnboardingActivity
ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:27:9-31:72
	android:label
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:30:13-33
	android:exported
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:29:13-37
	android:theme
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:31:13-69
	android:name
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:28:13-47
activity#com.example.elementarylearningcompanion.LessonActivity
ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:32:9-36:72
	android:label
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:35:13-33
	android:exported
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:36:13-69
	android:name
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:33:13-43
activity#com.example.elementarylearningcompanion.MathActivity
ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:37:9-41:72
	android:label
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:40:13-33
	android:exported
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:39:13-37
	android:theme
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:41:13-69
	android:name
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:38:13-41
activity#com.example.elementarylearningcompanion.EnglishActivity
ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:42:9-46:72
	android:label
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:45:13-33
	android:exported
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:44:13-37
	android:theme
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:46:13-69
	android:name
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:43:13-44
activity#com.example.elementarylearningcompanion.WrongQuestionActivity
ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:47:9-51:72
	android:label
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:50:13-32
	android:exported
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:49:13-37
	android:theme
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:51:13-69
	android:name
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:48:13-50
activity#com.example.elementarylearningcompanion.ProfileActivity
ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:52:9-56:72
	android:label
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:55:13-33
	android:exported
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:54:13-37
	android:theme
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:56:13-69
	android:name
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:53:13-44
activity#com.example.elementarylearningcompanion.HomeActivity
ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:57:9-61:72
	android:label
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:60:13-31
	android:exported
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:59:13-37
	android:theme
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:61:13-69
	android:name
		ADDED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:58:13-41
uses-sdk
INJECTED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml
INJECTED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a92ec79585cffeb394139b023740469\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a92ec79585cffeb394139b023740469\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\abc752c93138f8c097c427e2d1cfd0d3\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\abc752c93138f8c097c427e2d1cfd0d3\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\067a37e237c05460810f9f277f8a584d\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\067a37e237c05460810f9f277f8a584d\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\95602d9e58c05302d7b199e835d43608\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\95602d9e58c05302d7b199e835d43608\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b36af8a886450074d6162902a51e235f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b36af8a886450074d6162902a51e235f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4487a3ad6da06723d087bdf02bb6212f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4487a3ad6da06723d087bdf02bb6212f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bbf9a8b156fa420b5bdcba47fcc5dbcc\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bbf9a8b156fa420b5bdcba47fcc5dbcc\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e2100e335e320ce9f7d00232cdd6ecf\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e2100e335e320ce9f7d00232cdd6ecf\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\091a3284e1a4dc532944a24ac50d7eb7\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\091a3284e1a4dc532944a24ac50d7eb7\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6aac3b3dedcfdd7e5ad9b9976b2a0712\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6aac3b3dedcfdd7e5ad9b9976b2a0712\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\04cbc788dc368aaa114d050c4ee80c6e\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\04cbc788dc368aaa114d050c4ee80c6e\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\586aaef9cc7b931892e6d835ad6bc99e\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\586aaef9cc7b931892e6d835ad6bc99e\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd01bc6fe90ff89257de9b3fddbd915b\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd01bc6fe90ff89257de9b3fddbd915b\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\75430ae0bb5fc32db242c11acf14d810\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\75430ae0bb5fc32db242c11acf14d810\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\aae8de59d12aa99a6bbf2e13dbb7830c\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\aae8de59d12aa99a6bbf2e13dbb7830c\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf65474ba1a366f73047e913760fe941\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf65474ba1a366f73047e913760fe941\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ddb5585b1c51e8bca0bfec1fc4b8bcd5\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ddb5585b1c51e8bca0bfec1fc4b8bcd5\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea2513363d3f32d4ffb9d885f5bb824a\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea2513363d3f32d4ffb9d885f5bb824a\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1998a5b97855770a469c4c0f6ac2ff\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb1998a5b97855770a469c4c0f6ac2ff\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\abac53953083f897e88d18fa78efaf14\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\abac53953083f897e88d18fa78efaf14\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\bcc3fb5a217e851b87eb85758de9a548\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\bcc3fb5a217e851b87eb85758de9a548\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\f552d381a4d68594072bd5f3cc37425f\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\f552d381a4d68594072bd5f3cc37425f\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe57ec642b760abd119345d9083b9c3\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe57ec642b760abd119345d9083b9c3\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\c159823cd1d5b89649bf0a3c97706e19\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\c159823cd1d5b89649bf0a3c97706e19\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\81e6a57d5e25883e08f3f201e44bac3c\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\81e6a57d5e25883e08f3f201e44bac3c\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\2216d807e39e4d0ad5c14ea6533e364e\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\2216d807e39e4d0ad5c14ea6533e364e\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0bb11b1c721d2ccd7d5b795aedf051c\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0bb11b1c721d2ccd7d5b795aedf051c\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\55d6400460d0a92f9c14db5102d6c5ca\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\55d6400460d0a92f9c14db5102d6c5ca\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\04a60b50f4f9f1a1d0f65646d590b29d\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\04a60b50f4f9f1a1d0f65646d590b29d\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b32cc995a07f9e43ad6e96e5d0a4e2a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\9b32cc995a07f9e43ad6e96e5d0a4e2a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fbaf43bac10d410b67ca8e8b41e4c08\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fbaf43bac10d410b67ca8e8b41e4c08\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\f149972dcc2086112599bf9e6302b5ef\transformed\runtime-livedata-1.6.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\f149972dcc2086112599bf9e6302b5ef\transformed\runtime-livedata-1.6.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4df73b4c2d29b2ee6d6405fd9c7e463\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4df73b4c2d29b2ee6d6405fd9c7e463\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c35d31911b1cf341f8d6742e8d2ae7f9\transformed\activity-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c35d31911b1cf341f8d6742e8d2ae7f9\transformed\activity-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3980a140e6a763fb7ce351a902913430\transformed\activity-compose-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3980a140e6a763fb7ce351a902913430\transformed\activity-compose-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\412960d2e644cc6534bfc5724cc17cfb\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\412960d2e644cc6534bfc5724cc17cfb\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39c67d931621a4f2affb04e55a6b9d79\transformed\activity-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39c67d931621a4f2affb04e55a6b9d79\transformed\activity-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\464fe66b12ab402611cd361fca1d25a7\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\464fe66b12ab402611cd361fca1d25a7\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6046befa68638da83c6d2ae196e7998f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6046befa68638da83c6d2ae196e7998f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b188ac107923aed40d7280e63837d04b\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b188ac107923aed40d7280e63837d04b\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a1f48f776539bdce15fc290f8f421df\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2a1f48f776539bdce15fc290f8f421df\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a08d198b9c0aabe88b2d25fb1803f0e2\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a08d198b9c0aabe88b2d25fb1803f0e2\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\edd54d07eefba3de62cf3a424ba35a99\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\edd54d07eefba3de62cf3a424ba35a99\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\96056d7fd1109e2695ec224e30a717db\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\96056d7fd1109e2695ec224e30a717db\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a52c49df1ebcdfa482361ad588178e37\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a52c49df1ebcdfa482361ad588178e37\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7a976ce1b4bf90df4237a3614c14070\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7a976ce1b4bf90df4237a3614c14070\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dec948b9f09d4d2ea008ad8467768397\transformed\lifecycle-viewmodel-2.8.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dec948b9f09d4d2ea008ad8467768397\transformed\lifecycle-viewmodel-2.8.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94846f2e2b6d8b762b746d3cbe3bbeb6\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94846f2e2b6d8b762b746d3cbe3bbeb6\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3121edf18686d7604b1eec829eb8c555\transformed\lifecycle-livedata-core-ktx-2.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3121edf18686d7604b1eec829eb8c555\transformed\lifecycle-livedata-core-ktx-2.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\18f0d342697045afee3821219cdbfd53\transformed\lifecycle-livedata-core-2.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\18f0d342697045afee3821219cdbfd53\transformed\lifecycle-livedata-core-2.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c9d1ccbaefd891ddd9f1cf64befcde0\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c9d1ccbaefd891ddd9f1cf64befcde0\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6817582c8de4319ffe182939b2b4b537\transformed\lifecycle-viewmodel-ktx-2.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6817582c8de4319ffe182939b2b4b537\transformed\lifecycle-viewmodel-ktx-2.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b644477bea90e56aa6841d3d5cfcd45e\transformed\lifecycle-livedata-2.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b644477bea90e56aa6841d3d5cfcd45e\transformed\lifecycle-livedata-2.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e89c7198796348a35cd4e45f7fa7f84\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e89c7198796348a35cd4e45f7fa7f84\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\31d65ad1fefe9c1d16196b373d5ef2cf\transformed\lifecycle-viewmodel-savedstate-2.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\31d65ad1fefe9c1d16196b373d5ef2cf\transformed\lifecycle-viewmodel-savedstate-2.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\449f33422d7e13b8be9da16a763d6e24\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\449f33422d7e13b8be9da16a763d6e24\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\be5c7bc7ecd14619d60db4b085b1cddc\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\be5c7bc7ecd14619d60db4b085b1cddc\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8a6fb48f2af62a21b8983a783d922af1\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8a6fb48f2af62a21b8983a783d922af1\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f11a28a7e824ba62711219801cfa226\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f11a28a7e824ba62711219801cfa226\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2262cda324a9c4192fbaf465b29147d8\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2262cda324a9c4192fbaf465b29147d8\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b96dd2dffa7d8d920f9bb92528af68ee\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b96dd2dffa7d8d920f9bb92528af68ee\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\df40381d346eae08000be068c6d29355\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\df40381d346eae08000be068c6d29355\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ff825a51e922be0f197fe7521e4577e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ff825a51e922be0f197fe7521e4577e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94f6c5b28a22bda54ada88c18854768c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\94f6c5b28a22bda54ada88c18854768c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bbe371e66941691f6f2c06f572d22568\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bbe371e66941691f6f2c06f572d22568\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9af66da9ecfa08435b32349cc2f63841\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9af66da9ecfa08435b32349cc2f63841\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\617ecbf72f7cd31e3b222745db0245ac\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\617ecbf72f7cd31e3b222745db0245ac\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27a1974ffd655a45dfd663e4e0a5fd24\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27a1974ffd655a45dfd663e4e0a5fd24\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\c159823cd1d5b89649bf0a3c97706e19\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\c159823cd1d5b89649bf0a3c97706e19\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\c159823cd1d5b89649bf0a3c97706e19\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\81e6a57d5e25883e08f3f201e44bac3c\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\81e6a57d5e25883e08f3f201e44bac3c\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\81e6a57d5e25883e08f3f201e44bac3c\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ff825a51e922be0f197fe7521e4577e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ff825a51e922be0f197fe7521e4577e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.elementarylearningcompanion.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.elementarylearningcompanion.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
